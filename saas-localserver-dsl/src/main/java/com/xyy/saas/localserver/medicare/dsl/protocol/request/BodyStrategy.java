package com.xyy.saas.localserver.medicare.dsl.protocol.request;

import lombok.Getter;

/**
 * Body的生成策略
 *
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/08 15:14
 */
public enum BodyStrategy {

    /**
     * 单data的
     */
    jsonNode(JsonNodeBodyBuilderStrategy.class);


    @Getter
    private Class<? extends RequestBodyBuilderStrategy> clazz;

    private BodyStrategy(Class<? extends RequestBodyBuilderStrategy> clazz) {
        this.clazz = clazz;
    }


}
