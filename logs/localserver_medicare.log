16:12:30.879 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
16:12:31.390 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 56329 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
16:12:31.391 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
16:12:31.393 INFO  [calserver.LocalserverApplication:660] [] - The following 2 profiles are active: "local", "test"
16:12:37.659 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
16:12:37.660 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
16:12:37.682 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO]
16:12:37.684 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO]
16:12:37.687 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantTransmissionServicePackRelationDO]
16:12:37.691 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO]
16:12:37.694 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO]
16:12:37.699 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantCertificateDO]
16:12:37.701 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notify.NotifyTemplateDO]
16:12:37.704 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notify.NotifyMessageDO]
16:12:37.706 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDetailDO]
16:12:37.709 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO]
16:12:37.710 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.logger.OperateLogDO]
16:12:37.712 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.logger.LoginLogDO]
16:12:37.713 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsCodeDO]
16:12:37.716 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsChannelDO]
16:12:37.717 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsTemplateDO]
16:12:37.719 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsLogDO]
16:12:37.721 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2RefreshTokenDO]
16:12:37.723 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ApproveDO]
16:12:37.725 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2CodeDO]
16:12:37.726 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ClientDO]
16:12:37.728 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO]
16:12:37.730 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserClockInLogDO]
16:12:37.732 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO]
16:12:37.733 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserFingerPrintDO]
16:12:37.736 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notice.NoticeDO]
16:12:37.737 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.PostDO]
16:12:37.738 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO]
16:12:37.739 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.UserPostDO]
16:12:37.741 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oa.OaWhiteListDO]
16:12:37.742 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.MenuDO]
16:12:37.743 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.UserRoleDO]
16:12:37.744 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO]
16:12:37.745 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleMenuDO]
16:12:37.747 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO]
16:12:37.748 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dict.DictTypeDO]
16:12:37.750 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmProcessDefinitionInfoDO]
16:12:37.754 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmProcessExpressionDO]
16:12:37.755 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmProcessListenerDO]
16:12:37.756 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmFormDO]
16:12:37.757 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmUserGroupDO]
16:12:37.758 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmCategoryDO]
16:12:37.760 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.task.BpmProcessInstanceCopyDO]
16:12:37.762 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.oa.BpmOALeaveDO]
16:12:37.763 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO]
16:12:37.764 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO]
16:12:37.764 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notify.NotifyTemplateDO]
16:12:37.765 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notify.NotifyMessageDO]
16:12:37.765 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.logger.OperateLogDO]
16:12:37.766 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.logger.LoginLogDO]
16:12:37.766 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsCodeDO]
16:12:37.767 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsChannelDO]
16:12:37.767 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsTemplateDO]
16:12:37.768 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsLogDO]
16:12:37.769 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.mail.MailTemplateDO]
16:12:37.770 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.mail.MailLogDO]
16:12:37.772 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.mail.MailAccountDO]
16:12:37.773 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2RefreshTokenDO]
16:12:37.774 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ApproveDO]
16:12:37.775 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2CodeDO]
16:12:37.775 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ClientDO]
16:12:37.776 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO]
16:12:37.777 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO]
16:12:37.777 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notice.NoticeDO]
16:12:37.779 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserDO]
16:12:37.781 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.social.SocialClientDO]
16:12:37.783 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserBindDO]
16:12:37.784 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.PostDO]
16:12:37.784 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO]
16:12:37.785 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.UserPostDO]
16:12:37.785 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.MenuDO]
16:12:37.786 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.UserRoleDO]
16:12:37.786 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO]
16:12:37.787 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleMenuDO]
16:12:37.787 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO]
16:12:37.788 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dict.DictTypeDO]
16:12:37.789 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.config.MemberConfigDO]
16:12:37.791 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.group.MemberGroupDO]
16:12:37.793 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.point.MemberPointRecordDO]
16:12:37.795 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO]
16:12:37.797 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.signin.MemberSignInConfigDO]
16:12:37.799 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.signin.MemberSignInRecordDO]
16:12:37.800 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.address.MemberAddressDO]
16:12:37.802 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.level.MemberExperienceRecordDO]
16:12:37.804 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.level.MemberLevelRecordDO]
16:12:37.806 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.level.MemberLevelDO]
16:12:37.808 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.tag.MemberTagDO]
16:12:37.808 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03CourseDO$Demo03CourseDOBuilder.class]
16:12:37.809 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03GradeDO.class]
16:12:37.809 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03StudentDO.class]
16:12:37.809 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03StudentDO$Demo03StudentDOBuilder.class]
16:12:37.810 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03CourseDO.class]
16:12:37.810 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03GradeDO$Demo03GradeDOBuilder.class]
16:12:37.810 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo02/Demo02CategoryDO.class]
16:12:37.811 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo02/Demo02CategoryDO$Demo02CategoryDOBuilder.class]
16:12:37.811 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo01/Demo01ContactDO$Demo01ContactDOBuilder.class]
16:12:37.811 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo01/Demo01ContactDO.class]
16:12:37.812 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.logger.ApiAccessLogDO]
16:12:37.814 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.logger.ApiErrorLogDO]
16:12:37.818 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.file.FileConfigDO]
16:12:37.820 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.file.FileContentDO]
16:12:37.824 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO]
16:12:37.826 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.config.ConfigDO]
16:12:37.827 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.codegen.CodegenTableDO]
16:12:37.829 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.codegen.CodegenColumnDO]
16:12:37.831 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.job.JobDO]
16:12:37.832 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.job.JobLogDO]
16:12:37.836 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.db.DataSourceConfigDO]
16:12:37.878 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
16:12:37.909 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
16:12:37.925 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolManager$NamedThreadFactory.class]
16:12:37.926 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolManager.class]
16:12:37.926 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/RedisUtils.class]
16:12:37.927 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ColumnUtil$SFunction.class]
16:12:37.927 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolManager$ExecutorHolder.class]
16:12:37.927 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ColumnUtil.class]
16:12:37.928 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ColumnUtil$TestUserDemo.class]
16:12:37.928 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/UrlConUtil.class]
16:12:37.929 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/LocalDateUtil.class]
16:12:37.929 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ColumnUtil$TableField.class]
16:12:37.930 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/IdCardUtil.class]
16:12:37.931 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/DictValid.class]
16:12:37.931 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/ValidNumberFormat.class]
16:12:37.932 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/ValidDateFormat.class]
16:12:37.933 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/ValidDateFormatValidator.class]
16:12:37.934 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/ValidNumberValidator.class]
16:12:37.934 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/DictValidator.class]
16:12:37.934 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/converter/CustomDateConverter.class]
16:12:37.935 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/EasyExcelUtil.class]
16:12:37.935 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/AutoCheckExcelListener$1.class]
16:12:37.936 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/AutoCheckExcelListener.class]
16:12:37.936 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/SpelParserUtil.class]
16:12:37.936 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolExecutorMdcWrapper.class]
16:12:37.937 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/GuidUtils.class]
16:12:37.937 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ExceptionUtils.class]
16:12:37.937 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/MathUtil.class]
16:12:37.937 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/DingUtil.class]
16:12:37.938 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ConditionUtil.class]
16:12:37.938 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/UserUtil.class]
16:12:37.938 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolManager$LogAndAbortPolicy.class]
16:12:37.938 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/TimeWatchUtil.class]
16:12:37.939 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolExecutorMdcWrapper$MdcTaskDecorator.class]
16:12:37.939 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/PrefUtil.class]
16:12:37.939 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/AssertUtils.class]
16:12:37.940 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/FieldCompareUtil.class]
16:12:38.430 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/webclient/ForwardWebClientConfig.class]
16:12:38.431 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/webclient/InquiryForwardProperties$InquiryDubboForwardProperties.class]
16:12:38.431 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/webclient/InquiryForwardProperties.class]
16:12:38.432 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/servlet/TomcatServerConfiguration.class]
16:12:38.432 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/swagger/SwaggerRegistryConfiguration.class]
16:12:38.433 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/swagger/SwaggerRegistryConfiguration$1.class]
16:12:38.433 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/LocalRedisConfig.class]
16:12:38.434 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalStringValueOperations.class]
16:12:38.434 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalRedisConnection.class]
16:12:38.435 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalRedisTemplate.class]
16:12:38.435 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalRedisConnectionFactory.class]
16:12:38.436 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalStringRedisTemplate.class]
16:12:38.436 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalHashOperations.class]
16:12:38.437 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalValueOperations.class]
16:12:38.437 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/auto/MultiEnvAutoConfiguration.class]
16:12:38.438 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/bean/DefaultTenantContextInfoProvider.class]
16:12:38.438 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/mq/FlowableRocketMQConsumeMessageHook.class]
16:12:38.439 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/mq/local/LocalRocketMQTemplate.class]
16:12:38.440 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/mq/LocalRocketMQConfig.class]
16:12:38.440 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboReferenceTransmitterAutoConfiguration.class]
16:12:38.441 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboBootstrapConfiguration$1.class]
16:12:38.441 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboReferenceKernelAutoConfiguration.class]
16:12:38.441 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboReferenceUserAutoConfiguration.class]
16:12:38.442 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/filter/DubboTenantFilter.class]
16:12:38.443 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboBootstrapConfiguration$ServiceClassChecker.class]
16:12:38.443 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboBootstrapProperties.class]
16:12:38.444 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboBootstrapConfiguration.class]
16:12:38.444 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/anno/ArrayContainsCondition.class]
16:12:38.444 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/anno/ConditionalOnArrayContainsProperty.class]
16:12:38.464 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.user.server.dal.dataobject.tenant.TenantThirdAppDO]
16:12:38.772 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO]
16:12:38.778 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.CatalogDO]
16:12:38.781 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO]
16:12:38.784 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.productcategory.ProductCategoryDO]
16:12:38.786 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibSyncDO]
16:12:38.791 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO]
16:12:38.792 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductQualificationInfoDO]
16:12:38.794 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO]
16:12:38.798 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO]
16:12:38.799 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO]
16:12:38.802 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentRecordDO]
16:12:38.803 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentDetailDO]
16:12:38.805 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeDetailDO]
16:12:38.807 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeRecordDO]
16:12:39.021 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[cn.iocoder.yudao.module.*.dal.dataobject, com.xyy.saas.localserver.entity, com.xyy.saas.inquiry]],扫描到@DataSyncEntity的class size:[88]
16:12:40.299 INFO  [.RepositoryConfigurationDelegate:295] [] - Multiple Spring Data modules found, entering strict repository configuration mode
16:12:40.307 INFO  [.RepositoryConfigurationDelegate:143] [] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
16:12:40.495 INFO  [.RepositoryConfigurationDelegate:211] [] - Finished Spring Data repository scanning in 122 ms. Found 0 Redis repository interfaces.
16:12:43.654 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration' of type [cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:43.661 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'lockFailureStrategy' of type [cn.iocoder.yudao.framework.lock4j.core.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:43.829 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:43.857 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:43.901 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:45.425 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration' of type [cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:45.442 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'dataPermissionAnnotationAdvisor' of type [cn.iocoder.yudao.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:45.520 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration' of type [cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
16:12:45.535 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration' of type [cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [tenantRocketMQInitializer] is declared through a non-static factory method on that class; consider declaring it as static instead.
16:12:45.582 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:45.594 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:45.770 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:45.781 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:45.792 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
16:12:47.128 INFO  [.embedded.tomcat.TomcatWebServer:111] [] - Tomcat initialized with port 8071 (http)
16:12:47.162 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Initializing ProtocolHandler ["http-nio-8071"]
16:12:47.171 INFO  [he.catalina.core.StandardService:173] [] - Starting service [Tomcat]
16:12:47.171 INFO  [che.catalina.core.StandardEngine:173] [] - Starting Servlet engine: [Apache Tomcat/10.1.30]
16:12:47.387 INFO  [nerBase.[Tomcat].[localhost].[/]:173] [] - Initializing Spring embedded WebApplicationContext
16:12:47.388 INFO  [rvletWebServerApplicationContext:296] [] - Root WebApplicationContext: initialization completed in 15688 ms
16:12:48.994 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join properties config complete
16:12:49.218 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join SqlInjector init
16:12:57.479 ERROR [server.entity.mqtt.MqttSubscriber:44] [] - [MQTT] Initial subscription failed
org.eclipse.paho.client.mqttv3.MqttException: 已断开连接
	at org.eclipse.paho.client.mqttv3.internal.CommsReceiver.run(CommsReceiver.java:197)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.io.EOFException: null
	at java.base/java.io.DataInputStream.readUnsignedByte(DataInputStream.java:297)
	at java.base/java.io.DataInputStream.readByte(DataInputStream.java:275)
	at org.eclipse.paho.client.mqttv3.internal.wire.MqttInputStream.readMqttWireMessage(MqttInputStream.java:92)
	at org.eclipse.paho.client.mqttv3.internal.CommsReceiver.run(CommsReceiver.java:137)
	... 1 common frames omitted
16:12:57.499 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mqttListener': Injection of resource dependencies failed
16:12:57.508 INFO  [he.catalina.core.StandardService:173] [] - Stopping service [Tomcat]
16:12:57.748 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
16:12:57.824 ERROR [cs.LoggingFailureAnalysisReporter:40] [] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'java.util.List' that could not be found.


Action:

Consider defining a bean of type 'java.util.List' in your configuration.

16:13:27.493 ERROR [server.entity.mqtt.MqttSubscriber:68] [] - [MQTT] 延迟重连失败
org.eclipse.paho.client.mqttv3.MqttException: 客户机已关闭
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.connect(MqttAsyncClient.java:732)
	at org.eclipse.paho.client.mqttv3.MqttClient.connect(MqttClient.java:331)
	at com.xyy.saas.localserver.entity.mqtt.MqttSubscriber.lambda$scheduleDelayedRetry$0(MqttSubscriber.java:63)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
18:00:00.706 INFO  [e.validator.internal.util.Version:21] [] - HV000001: Hibernate Validator 8.0.1.Final
18:00:00.871 INFO  [ocalserver.LocalserverApplication:50] [] - Starting LocalserverApplication using Java 21.0.3 with PID 73253 (/Users/<USER>/codes/xyy/ls/saas-localserver/saas-localserver-application/target/classes started by caojialin in /Users/<USER>/codes/xyy/ls)
18:00:00.872 DEBUG [ocalserver.LocalserverApplication:51] [] - Running with Spring Boot v3.3.4, Spring v6.1.13
18:00:00.874 INFO  [calserver.LocalserverApplication:660] [] - The following 2 profiles are active: "local", "test"
18:00:04.433 WARN  [NamingStrategy$SnakeCaseStrategy:207] [] - PropertyNamingStrategy.SnakeCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.SnakeCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
18:00:04.433 WARN  [gStrategy$UpperCamelCaseStrategy:207] [] - PropertyNamingStrategy.UpperCamelCaseStrategy is used but it has been deprecated due to risk of deadlock. Consider using PropertyNamingStrategies.UpperCamelCaseStrategy instead. See https://github.com/FasterXML/jackson-databind/issues/2715 for more details.
18:00:04.447 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO]
18:00:04.450 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO]
18:00:04.451 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantTransmissionServicePackRelationDO]
18:00:04.454 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO]
18:00:04.457 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO]
18:00:04.460 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantCertificateDO]
18:00:04.462 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notify.NotifyTemplateDO]
18:00:04.465 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notify.NotifyMessageDO]
18:00:04.466 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDetailDO]
18:00:04.469 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO]
18:00:04.471 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.logger.OperateLogDO]
18:00:04.472 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.logger.LoginLogDO]
18:00:04.473 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsCodeDO]
18:00:04.476 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsChannelDO]
18:00:04.478 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsTemplateDO]
18:00:04.480 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsLogDO]
18:00:04.481 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2RefreshTokenDO]
18:00:04.483 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ApproveDO]
18:00:04.484 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2CodeDO]
18:00:04.485 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ClientDO]
18:00:04.487 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO]
18:00:04.488 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserClockInLogDO]
18:00:04.490 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO]
18:00:04.491 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserFingerPrintDO]
18:00:04.493 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notice.NoticeDO]
18:00:04.495 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.PostDO]
18:00:04.496 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO]
18:00:04.497 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.UserPostDO]
18:00:04.499 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oa.OaWhiteListDO]
18:00:04.500 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.MenuDO]
18:00:04.501 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.UserRoleDO]
18:00:04.502 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO]
18:00:04.503 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleMenuDO]
18:00:04.504 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO]
18:00:04.506 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dict.DictTypeDO]
18:00:04.509 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmProcessDefinitionInfoDO]
18:00:04.512 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmProcessExpressionDO]
18:00:04.513 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmProcessListenerDO]
18:00:04.514 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmFormDO]
18:00:04.515 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmUserGroupDO]
18:00:04.516 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmCategoryDO]
18:00:04.518 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.task.BpmProcessInstanceCopyDO]
18:00:04.521 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.bpm.dal.dataobject.oa.BpmOALeaveDO]
18:00:04.522 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO]
18:00:04.522 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO]
18:00:04.523 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notify.NotifyTemplateDO]
18:00:04.524 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notify.NotifyMessageDO]
18:00:04.524 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.logger.OperateLogDO]
18:00:04.525 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.logger.LoginLogDO]
18:00:04.525 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsCodeDO]
18:00:04.526 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsChannelDO]
18:00:04.526 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsTemplateDO]
18:00:04.527 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsLogDO]
18:00:04.528 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.mail.MailTemplateDO]
18:00:04.529 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.mail.MailLogDO]
18:00:04.531 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.mail.MailAccountDO]
18:00:04.531 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2RefreshTokenDO]
18:00:04.532 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ApproveDO]
18:00:04.533 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2CodeDO]
18:00:04.533 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ClientDO]
18:00:04.533 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO]
18:00:04.534 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO]
18:00:04.534 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.notice.NoticeDO]
18:00:04.535 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserDO]
18:00:04.536 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.social.SocialClientDO]
18:00:04.538 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserBindDO]
18:00:04.540 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.PostDO]
18:00:04.540 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO]
18:00:04.541 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dept.UserPostDO]
18:00:04.541 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.MenuDO]
18:00:04.542 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.UserRoleDO]
18:00:04.542 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO]
18:00:04.543 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleMenuDO]
18:00:04.544 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO]
18:00:04.545 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.system.dal.dataobject.dict.DictTypeDO]
18:00:04.546 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.config.MemberConfigDO]
18:00:04.548 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.group.MemberGroupDO]
18:00:04.550 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.point.MemberPointRecordDO]
18:00:04.551 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO]
18:00:04.555 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.signin.MemberSignInConfigDO]
18:00:04.556 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.signin.MemberSignInRecordDO]
18:00:04.558 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.address.MemberAddressDO]
18:00:04.560 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.level.MemberExperienceRecordDO]
18:00:04.561 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.level.MemberLevelRecordDO]
18:00:04.565 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.level.MemberLevelDO]
18:00:04.568 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.member.dal.dataobject.tag.MemberTagDO]
18:00:04.570 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03CourseDO$Demo03CourseDOBuilder.class]
18:00:04.570 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03GradeDO.class]
18:00:04.571 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03StudentDO.class]
18:00:04.572 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03StudentDO$Demo03StudentDOBuilder.class]
18:00:04.575 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03CourseDO.class]
18:00:04.575 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo03/Demo03GradeDO$Demo03GradeDOBuilder.class]
18:00:04.576 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo02/Demo02CategoryDO.class]
18:00:04.577 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo02/Demo02CategoryDO$Demo02CategoryDOBuilder.class]
18:00:04.578 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo01/Demo01ContactDO$Demo01ContactDOBuilder.class]
18:00:04.579 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[cn.iocoder.yudao.module.infra.dal.dataobject.demo],扫描到class:[jar:file:/Users/<USER>/.m2/repository/cn/iocoder/boot/yudao-module-infra-biz/2.3.0-SNAPSHOT/yudao-module-infra-biz-2.3.0-SNAPSHOT.jar!/cn/iocoder/yudao/module/infra/dal/dataobject/demo/demo01/Demo01ContactDO.class]
18:00:04.600 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.logger.ApiAccessLogDO]
18:00:04.603 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.logger.ApiErrorLogDO]
18:00:04.607 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.file.FileConfigDO]
18:00:04.610 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.file.FileContentDO]
18:00:04.613 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO]
18:00:04.615 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.config.ConfigDO]
18:00:04.616 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.codegen.CodegenTableDO]
18:00:04.617 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.codegen.CodegenColumnDO]
18:00:04.620 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.job.JobDO]
18:00:04.621 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.job.JobLogDO]
18:00:04.624 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[cn.iocoder.yudao.module.infra.dal.dataobject.db.DataSourceConfigDO]
18:00:04.666 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.merchant.MedicareMerchant]
18:00:04.695 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.localserver.entity.product.MedicareProduct]
18:00:04.709 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolManager$NamedThreadFactory.class]
18:00:04.710 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolManager.class]
18:00:04.710 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/RedisUtils.class]
18:00:04.711 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ColumnUtil$SFunction.class]
18:00:04.712 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolManager$ExecutorHolder.class]
18:00:04.712 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ColumnUtil.class]
18:00:04.712 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ColumnUtil$TestUserDemo.class]
18:00:04.713 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/UrlConUtil.class]
18:00:04.713 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/LocalDateUtil.class]
18:00:04.713 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ColumnUtil$TableField.class]
18:00:04.714 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/IdCardUtil.class]
18:00:04.714 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/DictValid.class]
18:00:04.715 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/ValidNumberFormat.class]
18:00:04.716 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/ValidDateFormat.class]
18:00:04.717 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/ValidDateFormatValidator.class]
18:00:04.717 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/ValidNumberValidator.class]
18:00:04.718 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/validator/DictValidator.class]
18:00:04.719 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/converter/CustomDateConverter.class]
18:00:04.720 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/EasyExcelUtil.class]
18:00:04.720 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/AutoCheckExcelListener$1.class]
18:00:04.721 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/excel/AutoCheckExcelListener.class]
18:00:04.721 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/SpelParserUtil.class]
18:00:04.722 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolExecutorMdcWrapper.class]
18:00:04.722 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/GuidUtils.class]
18:00:04.722 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ExceptionUtils.class]
18:00:04.723 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/MathUtil.class]
18:00:04.723 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/DingUtil.class]
18:00:04.724 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ConditionUtil.class]
18:00:04.724 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/UserUtil.class]
18:00:04.724 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolManager$LogAndAbortPolicy.class]
18:00:04.724 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/TimeWatchUtil.class]
18:00:04.725 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/ThreadPoolExecutorMdcWrapper$MdcTaskDecorator.class]
18:00:04.725 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/PrefUtil.class]
18:00:04.726 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/AssertUtils.class]
18:00:04.726 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.util],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-pojo/2.0.0-SNAPSHOT/saas-inquiry-pojo-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/util/FieldCompareUtil.class]
18:00:04.957 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/webclient/ForwardWebClientConfig.class]
18:00:04.958 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/webclient/InquiryForwardProperties$InquiryDubboForwardProperties.class]
18:00:04.959 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/webclient/InquiryForwardProperties.class]
18:00:04.959 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/servlet/TomcatServerConfiguration.class]
18:00:04.959 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/swagger/SwaggerRegistryConfiguration.class]
18:00:04.960 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/swagger/SwaggerRegistryConfiguration$1.class]
18:00:04.960 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/LocalRedisConfig.class]
18:00:04.960 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalStringValueOperations.class]
18:00:04.961 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalRedisConnection.class]
18:00:04.961 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalRedisTemplate.class]
18:00:04.961 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalRedisConnectionFactory.class]
18:00:04.961 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalStringRedisTemplate.class]
18:00:04.962 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalHashOperations.class]
18:00:04.962 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/redis/local/LocalValueOperations.class]
18:00:04.962 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/auto/MultiEnvAutoConfiguration.class]
18:00:04.963 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/bean/DefaultTenantContextInfoProvider.class]
18:00:04.963 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/mq/FlowableRocketMQConsumeMessageHook.class]
18:00:04.963 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/mq/local/LocalRocketMQTemplate.class]
18:00:04.964 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/mq/LocalRocketMQConfig.class]
18:00:04.964 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboReferenceTransmitterAutoConfiguration.class]
18:00:04.964 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboBootstrapConfiguration$1.class]
18:00:04.965 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboReferenceKernelAutoConfiguration.class]
18:00:04.965 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboReferenceUserAutoConfiguration.class]
18:00:04.966 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/filter/DubboTenantFilter.class]
18:00:04.967 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboBootstrapConfiguration$ServiceClassChecker.class]
18:00:04.967 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboBootstrapProperties.class]
18:00:04.967 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/dubbo/DubboBootstrapConfiguration.class]
18:00:04.967 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/anno/ArrayContainsCondition.class]
18:00:04.967 DEBUG [y.ClassPathDataSyncEntityScanner:154] [] - 数据同步扫描@DataSyncEntity类,排除包:[com.xyy.saas.inquiry.config],扫描到class:[jar:file:/Users/<USER>/.m2/repository/com/xyy/saas/saas-inquiry-multi-env/2.0.0-SNAPSHOT/saas-inquiry-multi-env-2.0.0-SNAPSHOT.jar!/com/xyy/saas/inquiry/config/anno/ConditionalOnArrayContainsProperty.class]
18:00:04.985 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.user.server.dal.dataobject.tenant.TenantThirdAppDO]
18:00:05.104 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO]
18:00:05.106 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.CatalogDO]
18:00:05.108 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO]
18:00:05.110 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.productcategory.ProductCategoryDO]
18:00:05.111 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibSyncDO]
18:00:05.113 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO]
18:00:05.114 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductQualificationInfoDO]
18:00:05.115 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO]
18:00:05.119 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO]
18:00:05.121 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO]
18:00:05.123 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentRecordDO]
18:00:05.125 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentDetailDO]
18:00:05.126 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeDetailDO]
18:00:05.127 DEBUG [y.ClassPathDataSyncEntityScanner:119] [] - 数据同步扫描@DataSyncEntity类,注册Entity Class:[com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeRecordDO]
18:00:05.272 INFO  [ty.ClassPathDataSyncEntityScanner:61] [] - 数据同步扫描basePackage:[[cn.iocoder.yudao.module.*.dal.dataobject, com.xyy.saas.localserver.entity, com.xyy.saas.inquiry]],扫描到@DataSyncEntity的class size:[88]
18:00:06.069 INFO  [.RepositoryConfigurationDelegate:295] [] - Multiple Spring Data modules found, entering strict repository configuration mode
18:00:06.072 INFO  [.RepositoryConfigurationDelegate:143] [] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
18:00:06.159 INFO  [.RepositoryConfigurationDelegate:211] [] - Finished Spring Data repository scanning in 67 ms. Found 0 Redis repository interfaces.
18:00:07.485 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration' of type [cn.iocoder.yudao.framework.lock4j.config.YudaoLock4jConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:07.490 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'lockFailureStrategy' of type [cn.iocoder.yudao.framework.lock4j.core.DefaultLockFailureStrategy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:07.668 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration' of type [com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:07.693 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'mzt.log.record-com.mzt.logapi.starter.configuration.LogRecordProperties' of type [com.mzt.logapi.starter.configuration.LogRecordProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:07.740 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'logRecordPerformanceMonitor' of type [com.mzt.logapi.service.impl.DefaultLogRecordPerformanceMonitor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:08.130 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration' of type [cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:08.136 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'dataPermissionAnnotationAdvisor' of type [cn.iocoder.yudao.framework.datapermission.core.aop.DataPermissionAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [myBatisMapperFactoryBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:08.179 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration' of type [cn.iocoder.yudao.framework.quartz.config.YudaoAsyncAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [threadPoolTaskExecutorBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
18:00:08.185 WARN  [elegate$BeanPostProcessorChecker:429] [] - Bean 'cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration' of type [cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [tenantRocketMQInitializer] is declared through a non-static factory method on that class; consider declaring it as static instead.
18:00:08.209 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.ListenerContainerConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:08.213 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration' of type [org.apache.rocketmq.spring.autoconfigure.MessageConverterConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:08.323 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'createRocketMQMessageConverter' of type [org.apache.rocketmq.spring.support.RocketMQMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:08.333 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketmq-org.apache.rocketmq.spring.autoconfigure.RocketMQProperties' of type [org.apache.rocketmq.spring.autoconfigure.RocketMQProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:08.343 WARN  [elegate$BeanPostProcessorChecker:437] [] - Bean 'rocketMQMessageListenerContainerRegistrar' of type [org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
18:00:09.224 INFO  [.embedded.tomcat.TomcatWebServer:111] [] - Tomcat initialized with port 8071 (http)
18:00:09.245 INFO  [.coyote.http11.Http11NioProtocol:173] [] - Initializing ProtocolHandler ["http-nio-8071"]
18:00:09.253 INFO  [he.catalina.core.StandardService:173] [] - Starting service [Tomcat]
18:00:09.253 INFO  [che.catalina.core.StandardEngine:173] [] - Starting Servlet engine: [Apache Tomcat/10.1.30]
18:00:09.500 INFO  [nerBase.[Tomcat].[localhost].[/]:173] [] - Initializing Spring embedded WebApplicationContext
18:00:09.501 INFO  [rvletWebServerApplicationContext:296] [] - Root WebApplicationContext: initialization completed in 8473 ms
18:00:10.984 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join properties config complete
18:00:11.144 INFO  [MybatisPlusJoinAutoConfiguration:139] [] - mybatis plus join SqlInjector init
18:00:18.790 ERROR [server.entity.mqtt.MqttSubscriber:44] [] - [MQTT] Initial subscription failed
org.eclipse.paho.client.mqttv3.MqttException: 已断开连接
	at org.eclipse.paho.client.mqttv3.internal.CommsReceiver.run(CommsReceiver.java:197)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.io.EOFException: null
	at java.base/java.io.DataInputStream.readUnsignedByte(DataInputStream.java:297)
	at java.base/java.io.DataInputStream.readByte(DataInputStream.java:275)
	at org.eclipse.paho.client.mqttv3.internal.wire.MqttInputStream.readMqttWireMessage(MqttInputStream.java:92)
	at org.eclipse.paho.client.mqttv3.internal.CommsReceiver.run(CommsReceiver.java:137)
	... 1 common frames omitted
18:00:18.813 WARN  [rvletWebServerApplicationContext:633] [] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mqttListener': Injection of resource dependencies failed
18:00:18.821 INFO  [he.catalina.core.StandardService:173] [] - Stopping service [Tomcat]
18:00:19.134 INFO  [g.ConditionEvaluationReportLogger:82] [] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
18:00:19.209 ERROR [cs.LoggingFailureAnalysisReporter:40] [] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'java.util.List' that could not be found.


Action:

Consider defining a bean of type 'java.util.List' in your configuration.

18:00:48.806 ERROR [server.entity.mqtt.MqttSubscriber:68] [] - [MQTT] 延迟重连失败
org.eclipse.paho.client.mqttv3.MqttException: 客户机已关闭
	at org.eclipse.paho.client.mqttv3.MqttAsyncClient.connect(MqttAsyncClient.java:732)
	at org.eclipse.paho.client.mqttv3.MqttClient.connect(MqttClient.java:331)
	at com.xyy.saas.localserver.entity.mqtt.MqttSubscriber.lambda$scheduleDelayedRetry$0(MqttSubscriber.java:63)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
