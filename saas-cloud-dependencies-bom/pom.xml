<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>local-server-cloud</artifactId>
		<groupId>com.xyy.saas</groupId>
		<version>${revision}</version>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>saas-cloud-dependencies-bom</artifactId>
	<version>${revision}</version>
	<packaging>pom</packaging>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<artifactId>spring-boot-starter</artifactId>
				<groupId>org.springframework.boot</groupId>
				<version>${springboot.version}</version>
			</dependency>
			<dependency>
				<artifactId>spring-boot-starter-web</artifactId>
				<groupId>org.springframework.boot</groupId>
				<version>${springboot.version}</version>
			</dependency>
			<dependency>
				<artifactId>spring-boot-starter-actuator</artifactId>
				<groupId>org.springframework.boot</groupId>
				<version>${springboot.version}</version>
			</dependency>
			<!-- dubbo dependency -->
			<dependency>
				<artifactId>dubbo-spring-boot-starter</artifactId>
				<groupId>org.apache.dubbo</groupId>
				<version>${dubbo.version}</version>
			</dependency>

			<dependency>
				<groupId>com.google.protobuf</groupId>
				<artifactId>protobuf-java</artifactId>
				<version>${protobuf.version}</version>
			</dependency>

			<!-- registry dependency -->
			<dependency>
				<artifactId>nacos-client</artifactId>
				<groupId>com.alibaba.nacos</groupId>
				<version>${nacos.version}</version>
			</dependency>
			<dependency>
				<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
				<groupId>com.alibaba.cloud</groupId>
				<version>${springcloud.alibaba.version}</version>
			</dependency>
			<dependency>
				<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
				<groupId>com.alibaba.cloud</groupId>
				<version>${springcloud.alibaba.version}</version>
			</dependency>
			<dependency>
				<artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
				<groupId>org.springframework.cloud</groupId>
				<version>${springcloud.zookeeper.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>


</project>
