package com.xyy.saas.eventbus.rocketmq.core.original;

import com.xyy.saas.eventbus.rocketmq.core.EventBusMessageListener;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.Collection;

/**
 * <AUTHOR> wh
 * @date : 2023/11/27 18:05
 * @description:
 */
public interface EventBusMessageMulticaster {
    boolean multicastMessage(MessageExt message, String uniqueConsumerId);

    void addMessageListeners(Collection<EventBusMessageListener<?>> listeners, String uniqueConsumerId);
}
