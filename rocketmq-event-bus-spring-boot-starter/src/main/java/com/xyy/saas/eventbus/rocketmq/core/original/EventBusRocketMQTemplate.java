package com.xyy.saas.eventbus.rocketmq.core.original;

import com.xyy.saas.eventbus.rocketmq.autoconfigure.EventBusRocketMQProperties;
import com.xyy.saas.eventbus.rocketmq.constants.EventBusMessageConstants;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.eventbus.rocketmq.utils.JsonUtil;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.MessageQueueSelector;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * RocketMQ消息发送模板
 * <AUTHOR> zhangpeng
 * @date :  2024/09/14 15:08
 */
@Slf4j
public class EventBusRocketMQTemplate implements DisposableBean {

	@Setter
	private RocketMQTemplate rocketMQTemplate;
	@Setter
	private EventBusRocketMQProperties eventBusRocketMQProperties;
	@Setter
	private StandardEnvironment environment;

	private String topic;

	private ExecutorService executorService;

	public void sendMessage(EventBusAbstractMessage abstractMessage) {
		sendMessage(abstractMessage, null, false, false);
	}

	/**
	 * 发送延时消息
	 *
	 * @param abstractMessage
	 * @param localDateTime
	 */
	public void sendMessage(EventBusAbstractMessage abstractMessage, LocalDateTime localDateTime) {
		sendMessage(abstractMessage, localDateTime, false, false);
	}

	/**
	 * 发送延时消息 by oneWay
	 *
	 * @param abstractMessage
	 * @param localDateTime
	 */
	public void sendMessageByOneway(EventBusAbstractMessage abstractMessage, LocalDateTime localDateTime) {
		sendMessage(abstractMessage, localDateTime, true, false);
	}

	/**
	 * 发送分区有序消息
	 *
	 * @param abstractMessage
	 * @param localDateTime
	 */
	public void sendOrderedMessage(EventBusAbstractMessage abstractMessage, LocalDateTime localDateTime) {
		sendMessage(abstractMessage, localDateTime, false, true);
	}

	/**
	 * 发送分区有序消息
	 *
	 * @param abstractMessage
	 */
	public void sendOrderedMessage(EventBusAbstractMessage abstractMessage) {
		sendMessage(abstractMessage, null, false, true);
	}

	public void sendMessage(EventBusAbstractMessage abstractMessage, LocalDateTime localDateTime, boolean isOneway,
							boolean isOrder) {
		Map<String, Object> map = new HashMap<>();
		map.put(EventBusMessageConstants.EVENT_BUS_DATA, abstractMessage);
		map.put(EventBusMessageConstants.EVENT_MESSAGE_VERSION, abstractMessage.getVersion());
		String content = JsonUtil.toJSONString(map);
		// todo: 发包可考虑使用 class 分发消息而非tag
		Message message = createMessage(abstractMessage, localDateTime, content);
//		message.putUserProperty("tenantId", 1);
		readyToSend(isOneway, isOrder, message, abstractMessage);
		log.info("发送消息 topic {} msgId {} message {}", topic, abstractMessage.getMsgId(), content);
	}

	private void readyToSend(boolean isOneway, boolean isOrder, Message message,
							 EventBusAbstractMessage abstractMessage) {
		MessageExt messageExt = (MessageExt) message;
		try {
			if (isOrder) {
				String shardingKey = message.getUserProperty("shardingKey");
				Assert.isTrue(StringUtils.hasText(shardingKey), "顺序消息必须提供shardingKey");
				MessageQueueSelector selector = (mqs, msg, shardingKeys) -> {
					int select = Math.abs(shardingKeys.hashCode());
					if (select < 0) {
						select = 0;
					}

					return mqs.get(select % mqs.size());
				};
				rocketMQTemplate.getProducer().send(message, selector, shardingKey);
				abstractMessage.setMsgId(messageExt.getMsgId());
			} else if (isOneway) {
				rocketMQTemplate.getProducer().sendOneway(message);
				abstractMessage.setMsgId(messageExt.getMsgId());
			} else {
				rocketMQTemplate.getProducer().send(message);
				abstractMessage.setMsgId(messageExt.getMsgId());
			}
		} catch (Exception e) {
			log.error("发送消息失败 topic {} msgId {} message {}", topic, abstractMessage.getMsgId(), e.getMessage(), e);
		}

	}

	private Message createMessage(EventBusAbstractMessage abstractMessage, LocalDateTime localDateTime,
								  String jsonString) {
//		Message message = new Message(topic, abstractMessage.getTag(), jsonString.getBytes(StandardCharsets.UTF_8));
		MessageExt message = new MessageExt();
		message.setTopic(topic);
		//这里的tag是否需要拼上环境，由appendProfile=true就拼,
		//判断abstractMessage.getTag()是空就默认返回""
		;
		String tag = Optional.ofNullable(abstractMessage.getTag()).orElse("");
		tag = eventBusRocketMQProperties.getTagByAppendProfile(environment, tag);
		message.setTags(tag);
		message.setBody(jsonString.getBytes(StandardCharsets.UTF_8));

		if (Objects.nonNull(localDateTime)) {
			message.setDelayTimeMs(localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli() - System.currentTimeMillis());
//			message.setStartDeliverTime(localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
		}

		if (Objects.nonNull(abstractMessage.getKey())) {
			message.setKeys(abstractMessage.getKey());
//			message.setKey(abstractMessage.getKey());
		}

		if (Objects.nonNull(abstractMessage.getShardingKey())) {
			// todo: 设置分区有序消息的shardingKey
//			message.setShardingKey(abstractMessage.getShardingKey());
		}
		return message;
	}

//	public void setProducer(Producer producer) {
//		this.producer = producer;
//	}

	public void setTopic(String topic) {
		this.topic = topic;
	}

	@Override
	public void destroy() {
//		if (Objects.nonNull(producer)) {
//			producer.shutdown();
//		}
	}
}
