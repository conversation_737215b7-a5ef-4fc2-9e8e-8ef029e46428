package com.xyy.saas.eventbus.rocketmq.core.aliyun;

import com.aliyun.openservices.ons.api.Message;
import com.xyy.saas.eventbus.rocketmq.core.EventBusMessageListener;

import java.util.Collection;

/**
 * <AUTHOR> wh
 * @date : 2023/11/27 18:05
 * @description:
 */
public interface AliyunEventBusMessageMulticaster {
    boolean multicastMessage(Message message, String uniqueConsumerId);

    void addMessageListeners(Collection<EventBusMessageListener<?>> listeners, String uniqueConsumerId);
}
