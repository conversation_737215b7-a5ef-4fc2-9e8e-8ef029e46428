package com.xyy.saas.eventbus.rocketmq.autoconfigure;

import com.xyy.saas.eventbus.rocketmq.autoconfigure.aliyun.EventBusAliyunConsumerRegisterAutoConfiguration;
import com.xyy.saas.eventbus.rocketmq.autoconfigure.aliyun.EventBusAliyunProducerRegisterAutoConfiguration;
import com.xyy.saas.eventbus.rocketmq.core.DefaultEventBusMessageListenerFactory;
import com.xyy.saas.eventbus.rocketmq.core.aliyun.AliyunEventBusErrorHandler;
import com.xyy.saas.eventbus.rocketmq.core.aliyun.AliyunDefaultEventBusErrorHandler;
import com.xyy.saas.eventbus.rocketmq.core.aliyun.AliyunEventBusMessageMulticaster;
import com.xyy.saas.eventbus.rocketmq.core.aliyun.AliyunEventBusSimpleEventMulticaster;
import com.xyy.saas.eventbus.rocketmq.core.original.DefaultEventBusErrorHandler;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusErrorHandler;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusMessageMulticaster;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusSimpleEventMulticaster;
import com.xyy.saas.eventbus.rocketmq.storage.MethodSuccessStorage;
import com.xyy.saas.eventbus.rocketmq.storage.RedisMethodSuccessStorageImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR> ZhangPeng
 * @date : 2023/11/29 10:13
 * @description:
 */
@EnableConfigurationProperties(EventBusRocketMQProperties.class)
@Import({EventBusRocketMQPropertiesHolder.class, DefaultEventBusMessageListenerFactory.class})
@AutoConfigureAfter(RocketMQAutoConfiguration.class)
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "rocketmq", value = "name-server")
@Configuration
@Slf4j
public class EventBusRocketMQAutoConfiguration {

    private final RedissonClient redissonClient;

    @ConditionalOnMissingBean(MethodSuccessStorage.class)
    @Bean
    public MethodSuccessStorage methodSuccessStorage() {
		log.error("EventBusRocketMQAutoConfiguration 初始化 MethodSuccessStorage");
        return new RedisMethodSuccessStorageImpl(redissonClient);
    }

	@ConditionalOnProperty(name = "rocketmq.name-server", matchIfMissing = false, havingValue = "")
	@ConditionalOnMissingBean(EventBusErrorHandler.class)
	@Bean
	public EventBusErrorHandler eventBusErrorHandler(EventBusRocketMQProperties eventBusRocketMQProperties) {
		log.error("EventBusRocketMQAutoConfiguration 初始化 原生RocktMQ环境 EventBusErrorHandler");
		return new DefaultEventBusErrorHandler(eventBusRocketMQProperties);
	}

	@ConditionalOnProperty(name = "rocketmq.name-server", matchIfMissing = false, havingValue = "")
	@ConditionalOnMissingBean(EventBusMessageMulticaster.class)
	@Bean
	public EventBusSimpleEventMulticaster eventBusSimpleEventMulticaster(@Autowired(required = false) EventBusErrorHandler errorHandler,
																			   MethodSuccessStorage methodSuccessStorage) {
		log.error("EventBusRocketMQAutoConfiguration 初始化 原生RocktMQ环境 EventBusSimpleEventMulticaster");
		return new EventBusSimpleEventMulticaster(errorHandler, methodSuccessStorage);
	}


	@Bean
	@ConditionalOnProperty(name = "rocketmq.name-server", matchIfMissing = false, havingValue = "")
	public EventBusProducerRegisterAutoConfiguration eventBusProducerRegisterAutoConfiguration(EventBusRocketMQPropertiesHolder eventBusRocketMQPropertiesHolder) {
		log.error("EventBusRocketMQAutoConfiguration 初始化 原生RocktMQ环境 EventBusProducerRegisterAutoConfiguration");
		return new EventBusProducerRegisterAutoConfiguration(eventBusRocketMQPropertiesHolder);
	}

	/**
	 * 原生RocktMQ环境消费组注册
	 * @param eventBusRocketMQPropertiesHolder
	 * @return
	 */
	@ConditionalOnProperty(name = "rocketmq.name-server", matchIfMissing = false, havingValue = "")
	@Bean
	public EventBusConsumerRegisterAutoConfiguration eventBusConsumerRegisterAutoConfiguration(EventBusRocketMQPropertiesHolder eventBusRocketMQPropertiesHolder) {
		log.error("EventBusRocketMQAutoConfiguration 初始化 原生RocktMQ环境 EventBusConsumerRegisterAutoConfiguration");
		return new EventBusConsumerRegisterAutoConfiguration(eventBusRocketMQPropertiesHolder);
	}

	@ConditionalOnProperty(name = "event.bus.aliYunNameServer", matchIfMissing = false, havingValue = "")
	@ConditionalOnMissingBean(AliyunEventBusErrorHandler.class)
	@Bean
	public AliyunEventBusErrorHandler aliyunEventBusErrorHandler(EventBusRocketMQProperties eventBusRocketMQProperties) {
		log.error("EventBusRocketMQAutoConfiguration 初始化 阿里云RocktMQ环境 AliyunEventBusErrorHandler");
		return new AliyunDefaultEventBusErrorHandler(eventBusRocketMQProperties);
	}


	@ConditionalOnProperty(name = "event.bus.aliYunNameServer", matchIfMissing = false, havingValue = "")
	@ConditionalOnMissingBean(AliyunEventBusMessageMulticaster.class)
	@Bean
	public AliyunEventBusSimpleEventMulticaster aliyunEventBusSimpleEventMulticaster(@Autowired(required = false) AliyunEventBusErrorHandler errorHandler,
																					 MethodSuccessStorage methodSuccessStorage) {
		log.error("EventBusRocketMQAutoConfiguration 初始化 阿里云RocktMQ环境 AliyunEventBusSimpleEventMulticaster");
		return new AliyunEventBusSimpleEventMulticaster(errorHandler, methodSuccessStorage);
	}

	@Bean
	@ConditionalOnProperty(name = "event.bus.aliYunNameServer", matchIfMissing = false, havingValue = "")
	public EventBusAliyunProducerRegisterAutoConfiguration eventBusAliyunProducerRegisterAutoConfiguration(EventBusRocketMQPropertiesHolder eventBusRocketMQPropertiesHolder) {
		log.error("EventBusRocketMQAutoConfiguration 初始化 阿里云RocktMQ环境 EventBusAliyunProducerRegisterAutoConfiguration");
		return new EventBusAliyunProducerRegisterAutoConfiguration(eventBusRocketMQPropertiesHolder);
	}

    /**
	 * 阿里云RocktMQ环境消费组注册
     * @param eventBusRocketMQPropertiesHolder
     * @return
     */
	@Bean
	@ConditionalOnProperty(name = "event.bus.aliYunNameServer", matchIfMissing = false, havingValue = "")
    public EventBusAliyunConsumerRegisterAutoConfiguration eventBusAliyunConsumerRegisterAutoConfiguration(EventBusRocketMQPropertiesHolder eventBusRocketMQPropertiesHolder) {
        log.error("EventBusRocketMQAutoConfiguration 初始化 阿里云RocktMQ环境 EventBusAliyunConsumerRegisterAutoConfiguration");
		return new EventBusAliyunConsumerRegisterAutoConfiguration(eventBusRocketMQPropertiesHolder);
    }
}
