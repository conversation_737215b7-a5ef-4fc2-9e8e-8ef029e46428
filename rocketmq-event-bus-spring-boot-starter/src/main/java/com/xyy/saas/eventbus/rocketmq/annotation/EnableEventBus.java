package com.xyy.saas.eventbus.rocketmq.annotation;

import com.xyy.saas.eventbus.rocketmq.autoconfigure.aliyun.EventBusAliyunConsumerRegisterAutoConfiguration;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

/**
 * <AUTHOR> wh
 * @date : 2023/11/24 16:23
 * @description:
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Import(EventBusAliyunConsumerRegisterAutoConfiguration.class)
public @interface EnableEventBus {
}
