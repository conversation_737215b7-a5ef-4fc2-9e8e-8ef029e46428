package com.xyy.saas.eventbus.rocketmq.autoconfigure;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> wh
 * @date : 2023/11/24 16:24
 * @description:
 */
@ConfigurationProperties(prefix = "event.bus")
@Data
public class EventBusRocketMQProperties {

	private String nameServer;

	//是否在消息的tag上拼上spring.profiles.active
	private boolean appendProfile;
	//---------------------------------------阿里云RocketMQ-------------------------
    private String aliMQAccessKey;

    private String aliMQSecretKey;

    private String aliYunNameServer;

    private String domain;

    private String subgroup;

    private Producer producer;

    private Consumer consumer;

    private String topic;

    private Integer maxReconsumeTimes = 8;

    /**
     * 是否注册消费者
     */
    private boolean consumerFlag = true;

    /**
     * 是否开启消息轨迹
     */
    private boolean enableMsgTrace = false;

    /**
     * 飞书监控报警webhook 没有则不推送
     */
    private String larkWebHook;

    /**
     * 飞书报警图片
     */
    private String larkImage = "";

	/**
	 * 根据配置获取MQ消息的tag，是否拼上spring.profiles.active
	 * 优先级：启动参数tag > -Dtag > -Dspring.profiles.active > yml配置tag spring.profiles.active
	 * @param environment
	 */
	public String getTagByAppendProfile(StandardEnvironment environment, String tag) {
		if (this.appendProfile) {
			// 获取启动参数中的tag
			String tagProfile = System.getProperty("tag");
			if (StringUtils.hasText(tagProfile)) {
				tag = tagProfile + "-" + tag;
			}
			// 如果environment中没有profile，则尝试从系统属性中获取
			String profileFromProperty = System.getProperty("spring.profiles.active");
			if (StringUtils.hasText(profileFromProperty)) {
				tag =  profileFromProperty+ "-" + tag;
			} else {
				// 获取spring.profiles.active
				// 尝试从environment中获取profile
				String[] activeProfiles = environment.getActiveProfiles();
				if (activeProfiles.length > 0 && StringUtils.hasText(activeProfiles[0])) {
					tag = activeProfiles[0] + "-" + tag;
				}
			}
		}
		// 去掉最后一个-
		if (tag != null && tag.endsWith("-")) {
			tag = tag.substring(0, tag.length() - 1);
		}
		return tag;
	}

    @Data
    public static class Producer {

        private String nameServer;

        private String topic;

        private String groupID;

        /**
         * 事件执行时间超时监控
         */
        private Long eventBusTimeOut = 15L;

        /**
         * 消费线程数 默认20
         */
        private Integer consumeThreadNums = 20;

        /**
         * 最大消费重试次数 默认 16次
         */
        private Integer maxReconsumeTimes = 16;

    }

    @Data
    public static final class Consumer {

        private String groupId;

        private String nameServer;

        private Integer consumerThreadNums = 20;

        private Integer consumerThreadMaxNums = 20;

        private Integer maxReconsumeTimes = 16;

    }

}
