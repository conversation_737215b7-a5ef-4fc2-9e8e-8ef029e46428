package com.xyy.saas.eventbus.rocketmq.core.aliyun;

import com.aliyun.openservices.ons.api.Consumer;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.DisposableBean;

/**
 * <AUTHOR> wh
 * @date : 2023/11/27 18:26
 * @description:
 */
@RequiredArgsConstructor
public class AliyunEventBusConsumerHolder implements DisposableBean {

    private final Consumer consumer;


    @Override
    public void destroy() {
        if (Objects.nonNull(consumer)) {
            this.consumer.shutdown();
        }

    }
}
