<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>local-server-cloud</artifactId>
		<groupId>com.xyy.saas</groupId>
		<version>${revision}</version>
	</parent>
	<artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>
	<version>${revision}</version>

	<packaging>jar</packaging>
	<name>rocketmq-event-bus-spring-boot-starter</name>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-autoconfigure</artifactId>
		</dependency>

		<!--<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-client</artifactId>
		</dependency>-->
		<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-spring-boot-starter</artifactId>
			<version>2.3.1</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>ons-client</artifactId>
			<version>1.9.1.Final</version>
		</dependency>

		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson</artifactId>
			<optional>true</optional>
			<version>3.32.0</version>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-http</artifactId>
			<version>5.8.32</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>junit</groupId>-->
<!--			<artifactId>junit</artifactId>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->

	</dependencies>


	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<source>21</source>
					<target>21</target>
					<parameters>true</parameters>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.5.0</version>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.3.1</version>
				<configuration>
					<attach>true</attach>
				</configuration>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-site-plugin</artifactId>
				<version>3.20.0</version>
			</plugin>
			<!-- Javadoc -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.10.0</version>
				<configuration>
					<javadocExecutable>${java.home}/bin/javadoc</javadocExecutable>
					<additionalJOptions>
						<additionalJOption>-Xdoclint:none</additionalJOption>
					</additionalJOptions>
				</configuration>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
<!--			&lt;!&ndash; Gpg Signature &ndash;&gt;-->
<!--			<plugin>-->
<!--				<groupId>org.apache.maven.plugins</groupId>-->
<!--				<artifactId>maven-gpg-plugin</artifactId>-->
<!--				<version>3.2.5</version>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<id>sign-artifacts</id>-->
<!--						<phase>verify</phase>-->
<!--						<goals>-->
<!--							<goal>sign</goal>-->
<!--						</goals>-->
<!--					</execution>-->
<!--				</executions>-->
<!--			</plugin>-->

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
