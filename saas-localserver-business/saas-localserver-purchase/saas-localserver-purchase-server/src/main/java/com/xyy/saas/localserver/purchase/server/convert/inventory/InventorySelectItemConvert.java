package com.xyy.saas.localserver.purchase.server.convert.inventory;

import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeChangeDTO;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDetailDTO;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Stream;

/**
 * 库存预占单详情转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface InventorySelectItemConvert {

        InventorySelectItemConvert INSTANCE = Mappers.getMapper(InventorySelectItemConvert.class);

        // ========== 公共转换方法 ==========

        /**
         * 将退货单详情转换为库存预占明细
         * 根据追溯码情况选择不同的处理方式：
         * 1. 有追溯码：按追溯码分组处理
         * 2. 无追溯码：按先进先出处理
         *
         * @param detail    退货单详情
         * @param selectMap 预占选择映射
         */
        default void convert2SelectItem(ReturnBillDetailDTO detail,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap) {
                Optional.ofNullable(detail.getExt())
                                .map(ExtDTO::getTraceCodes)
                                .filter(CollectionUtils::isNotEmpty)
                                .ifPresentOrElse(
                                                traceCodes -> addDirectItemByTraceCode(detail, traceCodes, selectMap),
                                                () -> addFifoItem(detail, selectMap, detail.getOutboundQuantity()));
        }

        /**
         * 将采购单明细转换为库存预占明细
         * 根据追溯码情况选择不同的处理方式：
         * 1. 有追溯码：按追溯码分组处理
         * 2. 无追溯码：按先进先出处理
         *
         * @param detail    采购单明细
         * @param selectMap 预占选择映射
         */
        default void convert2SelectItem(PurchaseBillDetailDTO detail,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap) {
                Optional.ofNullable(detail.getExt())
                                .map(ExtDTO::getTraceCodes)
                                .filter(CollectionUtils::isNotEmpty)
                                .ifPresentOrElse(
                                                traceCodes -> addDirectItemByTraceCode(detail, traceCodes, selectMap),
                                                () -> addFifoItem(detail, selectMap, detail.getPurchaseQuantity()));
        }

        /**
         * 添加库存选择项
         * 根据库存选择策略添加预占项
         *
         * @param inventorySelectStrategy 库存选择策略
         * @param selectMap               预占选择映射
         * @param detail                  收货单明细
         * @param supplierGuid            供应商ID
         * @param positionChangeStock     货位库存变更
         * @param tracePref               库存追溯前缀
         * @param rootTracePref           总部库存追溯前缀
         * @param traceCodes              追溯码列表
         */
        default void addInventorySelectItem(InventorySelectEnum inventorySelectStrategy,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap,
                        ReceiveBillDetailDTO detail,
                        String supplierGuid,
                        Map<String, BigDecimal> positionChangeStock,
                        String tracePref,
                        String rootTracePref,
                        List<TraceCodeChangeDTO> traceCodes) {

                // 校验必要参数
                if (detail == null || positionChangeStock == null || positionChangeStock.isEmpty()) {
                        return;
                }

                // 构建库存选择项
                InventorySelectItemDTO selectItem = InventorySelectItemDTO.builder()
                                .productPref(detail.getProductPref())
                                .lotNo(detail.getLotNo())
                                .positionChangeStock(positionChangeStock)
                                .tracePref(tracePref)
                                .rootTracePref(rootTracePref)
                                .supplierGuid(supplierGuid)
                                .traceCodes(traceCodes)
                                .build();

                // 添加到库存增加DTO
                selectMap.computeIfAbsent(inventorySelectStrategy, k -> new ArrayList<>()).add(selectItem);
        }

        // ========== 追溯码相关方法 ==========

        // /**
        // * 构建追溯码到批次的映射
        // * 将追溯码按批次分组，建立映射关系
        // *
        // * @param changeDetail 库存变更明细
        // * @param tracePrefExtractor 追溯前缀提取器
        // * @return 追溯码到批次的映射
        // */
        // default Map<String, String> buildTraceCodeToBatchMap(InventoryChangeDetailDTO
        // changeDetail,
        // Function<InventoryChangeDetailDTO, String> tracePrefExtractor) {
        //
        // if (CollectionUtils.isEmpty(changeDetail.getTraceCodes())) {
        // return Collections.emptyMap();
        // }
        //
        // String tracePref = tracePrefExtractor.apply(changeDetail);
        // if (StringUtils.isBlank(tracePref)) {
        // return Collections.emptyMap();
        // }
        //
        // return changeDetail.getTraceCodes().stream()
        // .filter(traceCode -> StringUtils.isNotBlank(traceCode.getTraceCode()))
        // .collect(Collectors.toMap(
        // TraceCodeChangeDTO::getTraceCode,
        // t -> tracePref,
        // (existing, replacement) -> existing));
        // }

        /**
         * 过滤指定追踪前缀的追溯码
         * 根据追溯前缀过滤追溯码列表
         *
         * @param traceCodes 追溯码列表
         * @param tracePref  追溯前缀
         * @return 过滤后的追溯码列表
         */
        default List<TraceCodeChangeDTO> filterTraceCodesByTracePref(List<TraceCodeChangeDTO> traceCodes,
                        String tracePref) {
                if (CollectionUtils.isEmpty(traceCodes) || StringUtils.isBlank(tracePref)) {
                        return Collections.emptyList();
                }

                return traceCodes.stream()
                                .filter(traceCode -> tracePref.equals(traceCode.getTracePref()) ||
                                                tracePref.equals(traceCode.getRootTracePref()))
                                .toList();
        }

        /**
         * 获取收货明细中的追溯码
         *
         * @param detail 收货单明细
         * @return 追溯码列表
         */
        default List<TraceCodeChangeDTO> getDetailTraceCodes(ReceiveBillDetailDTO detail) {
                return Optional.ofNullable(detail.getExt())
                                .map(ExtDTO::getTraceCodes)
                                .orElse(Collections.emptyList());
        }

        // ========== 库存变更相关方法 ==========

        /**
         * 按追踪前缀合并明细
         * 将相同追踪前缀的明细合并，合并规则：
         * 1. 合并入库数量和出库数量
         * 2. 合并追溯码（去重）
         * 3. 保留其他字段不变
         *
         * @param details 库存变更明细列表
         * @return 合并后的明细列表
         */
        default List<InventoryChangeDetailDTO> mergeDetailsByTracePref(List<InventoryChangeDetailDTO> details) {
                if (CollectionUtils.isEmpty(details)) {
                        return Collections.emptyList();
                }

                return details.parallelStream()
                                .collect(Collectors.groupingBy(
                                                InventoryChangeDetailDTO::getRootTracePref,
                                                ConcurrentHashMap::new,
                                                Collectors.collectingAndThen(
                                                                Collectors.reducing(this::mergeSameTraceDetails),
                                                                opt -> opt.orElse(null))))
                                .values()
                                .stream()
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
        }

        /**
         * 合并相同追踪维度的明细
         * 合并规则：
         * 1. 入库数量相加
         * 2. 出库数量相加
         * 3. 追溯码合并（去重）
         * 4. 其他字段取第一个明细的值
         *
         * @param first  第一个明细
         * @param second 第二个明细
         * @return 合并后的明细
         */
        default InventoryChangeDetailDTO mergeSameTraceDetails(InventoryChangeDetailDTO first,
                        InventoryChangeDetailDTO second) {
                if (first == null) {
                        return second;
                }
                if (second == null) {
                        return first;
                }

                // 合并数量
                BigDecimal firstInNumber = Optional.ofNullable(first.getInNumber()).orElse(BigDecimal.ZERO);
                BigDecimal secondInNumber = Optional.ofNullable(second.getInNumber()).orElse(BigDecimal.ZERO);
                BigDecimal firstOutNumber = Optional.ofNullable(first.getOutNumber()).orElse(BigDecimal.ZERO);
                BigDecimal secondOutNumber = Optional.ofNullable(second.getOutNumber()).orElse(BigDecimal.ZERO);

                // 合并追溯码（使用Set去重提高性能）
                Set<String> firstCodes = Optional.ofNullable(first.getTraceCodes())
                                .orElse(Collections.emptyList())
                                .stream()
                                .map(TraceCodeChangeDTO::getTraceCode)
                                .collect(Collectors.toSet());

                List<TraceCodeChangeDTO> mergedTraceCodes = Stream.concat(
                                Optional.ofNullable(first.getTraceCodes()).orElse(Collections.emptyList()).stream(),
                                Optional.ofNullable(second.getTraceCodes()).orElse(Collections.emptyList()).stream()
                                                .filter(t -> !firstCodes.contains(t.getTraceCode())))
                                .collect(Collectors.toList());

                return InventoryChangeDetailDTO.builder()
                                .productPref(first.getProductPref())
                                .lotNo(first.getLotNo())
                                .rootTracePref(first.getRootTracePref())
                                .tracePref(first.getTracePref())
                                .inNumber(firstInNumber.add(secondInNumber))
                                .outNumber(firstOutNumber.add(secondOutNumber))
                                .traceCodes(mergedTraceCodes)
                                .build();
        }

        /**
         * 合并两个不同追踪维度的DTO
         * 合并规则：
         * 1. 入库数量 = 配送出库数量 - 源单入库数量
         * 2. 追溯码取配送单中不在源单中的部分
         * 3. 其他字段取源单的值
         *
         * @param source   源明细
         * @param delivery 配送明细
         * @return 合并后的明细
         */
        default InventoryChangeDetailDTO mergeDetails(InventoryChangeDetailDTO source,
                        InventoryChangeDetailDTO delivery) {
                // 数值计算
                BigDecimal inNumber = Optional.ofNullable(source.getInNumber()).orElse(BigDecimal.ZERO);
                BigDecimal outNumber = Optional.ofNullable(delivery.getOutNumber()).orElse(BigDecimal.ZERO);

                // 合并追溯码（使用Set去重提高性能）
                Set<String> sourceCodes = Optional.ofNullable(source.getTraceCodes())
                                .orElse(Collections.emptyList())
                                .stream()
                                .map(TraceCodeChangeDTO::getTraceCode)
                                .collect(Collectors.toSet());

                List<TraceCodeChangeDTO> mergedTraceCodes = Optional.ofNullable(delivery.getTraceCodes())
                                .orElse(Collections.emptyList())
                                .stream()
                                .filter(t -> !sourceCodes.contains(t.getTraceCode()))
                                .collect(Collectors.toList());

                return InventoryChangeDetailDTO.builder()
                                .productPref(source.getProductPref())
                                .lotNo(source.getLotNo())
                                .rootTracePref(source.getRootTracePref())
                                .tracePref(source.getTracePref())
                                .inNumber(outNumber.subtract(inNumber))
                                .traceCodes(mergedTraceCodes)
                                .build();
        }

        /**
         * 处理合格和不合格数量分配
         * 根据合格和不合格数量进行库存分配
         *
         * @param detail      收货单明细
         * @param changeStock 变更库存
         * @return 货位库存变更映射
         */
        default Map<String, BigDecimal> allocateQualifiedAndUnqualifiedQuantity(ReceiveBillDetailDTO detail,
                        BigDecimal changeStock) {

                if (changeStock == null || changeStock.compareTo(BigDecimal.ZERO) <= 0) {
                        return Collections.emptyMap();
                }

                Map<String, BigDecimal> positionChangeStock = new HashMap<>(2);

                // 处理合格数量
                BigDecimal qualifiedQuantity = Optional.ofNullable(detail.getQualifiedQuantity())
                                .orElse(BigDecimal.ZERO);
                BigDecimal unqualifiedQuantity = Optional.ofNullable(detail.getUnqualifiedQuantity())
                                .orElse(BigDecimal.ZERO);
                BigDecimal remainingStock = changeStock;

                // 优先分配合格数量
                if (qualifiedQuantity.compareTo(BigDecimal.ZERO) > 0 &&
                                StringUtils.isNotBlank(detail.getQualifiedPositionGuid())) {

                        BigDecimal allocatedQualified = remainingStock.min(qualifiedQuantity);
                        positionChangeStock.put(detail.getQualifiedPositionGuid(), allocatedQualified);
                        remainingStock = remainingStock.subtract(allocatedQualified);
                }

                // 分配不合格数量
                if (unqualifiedQuantity.compareTo(BigDecimal.ZERO) > 0 &&
                                remainingStock.compareTo(BigDecimal.ZERO) > 0 &&
                                StringUtils.isNotBlank(detail.getUnqualifiedPositionGuid())) {

                        BigDecimal allocatedUnqualified = remainingStock.min(unqualifiedQuantity);
                        positionChangeStock.put(detail.getUnqualifiedPositionGuid(), allocatedUnqualified);
                }

                return positionChangeStock;
        }

        /**
         * 更新剩余数量
         * 更新合格和不合格数量的剩余值
         *
         * @param positionChangeStock 货位库存变更
         * @param quantities          数量数组 [合格数量, 不合格数量]
         * @return 是否已分配完毕
         */
        default boolean updateRemainingQuantities(Map<String, BigDecimal> positionChangeStock,
                        BigDecimal[] quantities) {

                // 计算已分配的总数量
                BigDecimal allocatedQuantity = positionChangeStock.values().stream()
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 更新合格数量
                if (quantities[0].compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal allocatedQualified = allocatedQuantity.min(quantities[0]);
                        quantities[0] = quantities[0].subtract(allocatedQualified);
                        allocatedQuantity = allocatedQuantity.subtract(allocatedQualified);
                }

                // 更新不合格数量
                if (quantities[1].compareTo(BigDecimal.ZERO) > 0 &&
                                allocatedQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        quantities[1] = quantities[1].subtract(allocatedQuantity);
                }

                // 判断是否已分配完毕
                return quantities[0].compareTo(BigDecimal.ZERO) <= 0 &&
                                quantities[1].compareTo(BigDecimal.ZERO) <= 0;
        }

        // /**
        // * 构建追溯码变更库存映射
        // * 根据追溯码情况处理库存变更
        // *
        // * @param detail 收货单明细
        // * @param changeDetails 库存变更明细列表
        // * @param tracePrefExtractor 追溯前缀提取器
        // * @param traceCodeSetter 追溯码设置器
        // * @return 追溯码变更库存映射
        // */
        // default Map<String, BigDecimal> buildTraceChangeStockMap(ReceiveBillDetailDTO
        // detail,
        // List<InventoryChangeDetailDTO> changeDetails,
        // Function<InventoryChangeDetailDTO, String> tracePrefExtractor,
        // BiConsumer<TraceCodeChangeDTO, String> traceCodeSetter) {
        //
        // Map<String, BigDecimal> traceChangeStock = new HashMap<>();
        // List<TraceCodeChangeDTO> detailTraceCodes = getDetailTraceCodes(detail);
        //
        // for (InventoryChangeDetailDTO changeDetail : changeDetails) {
        // if (CollectionUtils.isNotEmpty(detailTraceCodes)) {
        // // 有追溯码：按追溯码处理
        // processWithTraceCodes(detailTraceCodes, changeDetail, tracePrefExtractor,
        // traceCodeSetter, traceChangeStock);
        // } else {
        // // 无追溯码：直接使用上游出库数量
        // addOutNumberToStock(tracePrefExtractor, changeDetail, traceChangeStock);
        // }
        // }
        //
        // return traceChangeStock;
        // }

        /**
         * 构建拒收货位库存变更映射
         * 处理拒收情况下的库存变更
         * 
         * @param detail       收货单明细
         * @param changeDetail 库存变更明细
         * @return 货位库存变更映射
         */
        default Map<String, BigDecimal> buildRejectPositionChangeStock(ReceiveBillDetailDTO detail,
                        InventoryChangeDetailDTO changeDetail) {
                Map<String, BigDecimal> positionChangeStock = new HashMap<>();

                BigDecimal unqualifiedQuantity = Optional.ofNullable(detail.getUnqualifiedQuantity())
                                .orElse(BigDecimal.ZERO);

                if (unqualifiedQuantity.compareTo(BigDecimal.ZERO) > 0 &&
                                detail.getUnqualifiedPositionGuid() != null) {
                        positionChangeStock.put(detail.getUnqualifiedPositionGuid(), changeDetail.getInNumber());
                }

                return positionChangeStock;
        }

        // ========== 私有辅助方法 ==========

        /**
         * 固定批次数据组装（存在追溯码的数据）
         * 将追溯码按批次分组，并计算每个批次的数量
         *
         * @param detail     退货单详情
         * @param traceCodes 追溯码列表
         * @param selectMap  预占选择映射
         */
        private void addDirectItemByTraceCode(ReturnBillDetailDTO detail,
                        List<TraceCodeChangeDTO> traceCodes,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap) {
                addDirectItemByTraceCodeInternal(detail, traceCodes, selectMap);
        }

        /**
         * 固定批次数据组装（存在追溯码的数据）
         * 将追溯码按批次分组，并计算每个批次的数量
         *
         * @param detail     采购单明细
         * @param traceCodes 追溯码列表
         * @param selectMap  预占选择映射
         */
        private void addDirectItemByTraceCode(PurchaseBillDetailDTO detail,
                        List<TraceCodeChangeDTO> traceCodes,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap) {
                addDirectItemByTraceCodeInternal(detail, traceCodes, selectMap);
        }

        /**
         * 固定批次数据组装（内部实现）
         * 将追溯码按批次分组，并计算每个批次的数量
         *
         * @param detail     单据明细
         * @param traceCodes 追溯码列表
         * @param selectMap  预占选择映射
         */
        private void addDirectItemByTraceCodeInternal(Object detail,
                        List<TraceCodeChangeDTO> traceCodes,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap) {
                traceCodes.stream()
                                .collect(Collectors.groupingBy(
                                                TraceCodeChangeDTO::getTracePref,
                                                Collectors.collectingAndThen(
                                                                Collectors.toList(),
                                                                list -> new AbstractMap.SimpleEntry<>(
                                                                                list.stream()
                                                                                                .map(TraceCodeChangeDTO::getChangeNumber)
                                                                                                .reduce(BigDecimal.ZERO,
                                                                                                                BigDecimal::add),
                                                                                list))))
                                .forEach((tracePref, entry) -> {
                                        InventorySelectItemDTO item;
                                        if (detail instanceof ReturnBillDetailDTO) {
                                                item = buildItem((ReturnBillDetailDTO) detail, tracePref,
                                                                entry.getKey(),
                                                                entry.getValue());
                                        } else if (detail instanceof PurchaseBillDetailDTO) {
                                                item = buildItem((PurchaseBillDetailDTO) detail, tracePref,
                                                                entry.getKey(), entry.getValue());
                                        } else {
                                                throw new IllegalArgumentException("Unsupported detail type");
                                        }
                                        selectMap.computeIfAbsent(InventorySelectEnum.DIRECT, k -> new ArrayList<>())
                                                        .add(item);
                                });
        }

        /**
         * 动态批次数据组装（先进先出）
         * 按先进先出原则处理无追溯码的情况
         *
         * @param detail    退货单详情
         * @param selectMap 预占选择映射
         * @param quantity  数量
         */
        private void addFifoItem(ReturnBillDetailDTO detail,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap,
                        BigDecimal quantity) {
                selectMap.computeIfAbsent(InventorySelectEnum.FIFO, k -> new ArrayList<>())
                                .add(buildItem(detail, null, quantity, null));
        }

        /**
         * 动态批次数据组装（先进先出）
         * 按先进先出原则处理无追溯码的情况
         *
         * @param detail    采购单明细
         * @param selectMap 预占选择映射
         * @param quantity  数量
         */
        private void addFifoItem(PurchaseBillDetailDTO detail,
                        Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap,
                        BigDecimal quantity) {
                selectMap.computeIfAbsent(InventorySelectEnum.FIFO, k -> new ArrayList<>())
                                .add(buildItem(detail, null, quantity, null));
        }

        /**
         * 组装库存预占明细
         *
         * @param detail             退货单详情
         * @param tracePref          库存追溯前缀
         * @param changeNumber       变更数量
         * @param traceCodes         追溯码列表
         * @return 库存预占明细
         */
        private InventorySelectItemDTO buildItem(ReturnBillDetailDTO detail,
                        String tracePref,
                        BigDecimal changeNumber,
                        List<TraceCodeChangeDTO> traceCodes) {
                return buildInventorySelectItem(
                                detail.getPositionGuid(),
                                detail.getProductPref(),
                                detail.getLotNo(),
                                tracePref,
                                changeNumber,
                                detail.getSupplierGuid(),
                                traceCodes);
        }

        /**
         * 组装库存预占明细
         *
         * @param detail             采购单明细
         * @param tracePref 库存追溯前缀
         * @param changeNumber       变更数量
         * @param traceCodes         追溯码列表
         * @return 库存预占明细
         */
        private InventorySelectItemDTO buildItem(PurchaseBillDetailDTO detail,
                        String tracePref,
                        BigDecimal changeNumber,
                        List<TraceCodeChangeDTO> traceCodes) {
                return buildInventorySelectItem(
                                detail.getPositionGuid(),
                                detail.getProductPref(),
                                detail.getLotNo(),
                                tracePref,
                                changeNumber,
                                detail.getSupplierGuid(),
                                traceCodes);
        }

        /**
         * 构建库存预占明细
         *
         * @param positionGuid       货位ID
         * @param productPref        商品编码
         * @param lotNo              批号
         * @param tracePref          库存追溯前缀
         * @param changeNumber       变更数量
         * @param supplierGuid       供应商ID
         * @param traceCodes         追溯码列表
         * @return 库存预占明细
         */
        private InventorySelectItemDTO buildInventorySelectItem(String positionGuid,
                        String productPref,
                        String lotNo,
                        String tracePref,
                        BigDecimal changeNumber,
                        String supplierGuid,
                        List<TraceCodeChangeDTO> traceCodes) {
                return InventorySelectItemDTO.builder()
                                .positionGuid(positionGuid)
                                .productPref(productPref)
                                .lotNo(lotNo)
                                .tracePref(tracePref)
                                .changeNumber(changeNumber)
                                .supplierGuid(supplierGuid)
                                .traceCodes(traceCodes)
                                .build();
        }
}