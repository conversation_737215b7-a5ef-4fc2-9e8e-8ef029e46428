package com.xyy.saas.localserver.purchase.server.mqtt;

import com.xyy.saas.localserver.entity.mqtt.MqttMessageHandler;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.server.service.receive.ReceiveBillService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

// 总部配送消息处理
@Slf4j
public class HeadDispatchHandler implements MqttMessageHandler<ReceiveBillDTO> {

    @Resource
    private ReceiveBillService receiveBillService;

    /** 总部发货 */
    private static final String MESSAGE_TYPE = "HEAD_DISPATCH";

    @Override
    public String getMessageType() {
        return MESSAGE_TYPE;
    }


    @Override
    public Class<ReceiveBillDTO> getDataClass() {
        return ReceiveBillDTO.class; // 直接返回具体类型
    }

    @Override
    public void handle(String topic, ReceiveBillDTO data) {

        // 处理收货
        receiveBillService.receive(data);
    }
}
