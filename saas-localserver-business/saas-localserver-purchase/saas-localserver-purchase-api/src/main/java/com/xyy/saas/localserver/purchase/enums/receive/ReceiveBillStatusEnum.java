package com.xyy.saas.localserver.purchase.enums.receive;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.RECEIVE_BILL_STATUS_NOT_EXISTS;

/**
 * 采购收货单状态枚举
 */
@Getter
@AllArgsConstructor
public enum ReceiveBillStatusEnum {

    PENDING_RECEIVE(1, "待收货"),
    PENDING_CHECK(2, "待验收"),
    PENDING_STORAGE(3, "待入库"),
    STORED(4, "已入库"),
    REJECTED(5, "已拒收");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态码获取枚举
     */
    public static ReceiveBillStatusEnum fromCode(Integer code) {
        for (ReceiveBillStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw exception(RECEIVE_BILL_STATUS_NOT_EXISTS);
    }
}