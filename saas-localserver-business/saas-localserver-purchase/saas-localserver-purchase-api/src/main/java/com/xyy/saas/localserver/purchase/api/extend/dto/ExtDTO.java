package com.xyy.saas.localserver.purchase.api.extend.dto;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeChangeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * 单据详情 - 扩展信息 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "单据详情 - 扩展信息 DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExtDTO {

    /** 商品信息 */
    @Schema(description = "商品信息")
    private ProductInfoDto productInfo;

    /** 追溯码信息 */
    @Schema(description = "追溯码信息")
    private List<TraceCodeChangeDTO> traceCodes;

}