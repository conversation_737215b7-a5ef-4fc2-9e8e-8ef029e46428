package com.xyy.saas.localserver.purchase.enums.returned;

import com.xyy.saas.localserver.entity.enums.bill.BillTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.xyy.saas.localserver.entity.enums.bill.BillTypeEnum.RETURN;

@Getter
@AllArgsConstructor
public enum ReturnBillTypeEnum {

    PURCHASE_RETURN(1, "采购退货单",false,RETURN),
    STORE_RETURN(2, "门店退货单",false,RETURN),
    STORE_ALLOCATION(3, "门店调剂单",false,RETURN);

    private final Integer code;
    private final String description;
    private final Boolean allowPartialCampOn;
    private final BillTypeEnum billType;

    /**
     * 根据编码获取枚举实例
     */
    public static ReturnBillTypeEnum fromCode(Integer code) {
        for (ReturnBillTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的单据来源编码: " + code);
    }
}