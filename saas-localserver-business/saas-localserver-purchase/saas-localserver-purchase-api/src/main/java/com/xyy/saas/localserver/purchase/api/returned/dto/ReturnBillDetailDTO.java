package com.xyy.saas.localserver.purchase.api.returned.dto;

import com.xyy.saas.localserver.entity.pojo.BaseDto;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 退货单明细 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "退货单明细 DTO")
@Data
@Accessors(chain = true)
public class ReturnBillDetailDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;

    /** 单号 */
    @Schema(description = "单号")
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    private String lotNo;

    /** 生产日期 */
    @Schema(description = "生产日期")
    private LocalDate productionDate;

    /** 有效期 */
    @Schema(description = "有效期")
    private LocalDate expiryDate;

    /** 总部（合格）货位编号 */
    @Schema(description = "总部货位编号")
    private String positionGuid;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称")
    private String supplierName;

    /** 税率 */
    @Schema(description = "税率")
    private BigDecimal inTaxRate;

    /** 含税成本价（单价） */
    @Schema(description = "含税成本价")
    private BigDecimal price;

    /** 销项税率 */
    @Schema(description = "销项税率")
    private BigDecimal outTaxRate;

    /** 出库数量 */
    @Schema(description = "出库数量")
    private BigDecimal outboundQuantity;

    /** 成本均价 */
    @Schema(description = "成本均价")
    private BigDecimal outboundPrice;

    /** 含税成本金额（总金额） */
    @Schema(description = "含税成本金额")
    private BigDecimal outboundAmount;

    /** 渠道ID */
    @Schema(description = "渠道ID")
    private String channelId;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private ExtDTO ext;

    /** 退货原因(1-破损、2-召回、3-滞销、4-过期失效、5-近效期、6-质量问题、7-其他) */
    @Schema(description = "退货原因")
    private Integer returnReason;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;
}