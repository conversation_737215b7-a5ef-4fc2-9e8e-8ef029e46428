package com.xyy.saas.localserver.purchase.enums.receive;

import com.xyy.saas.localserver.entity.enums.bill.BillTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.RECEIVE_BILL_TYPE_NOT_EXISTS;

/**
 * 采购收货单类型枚举
 */
@Getter
@AllArgsConstructor
public enum ReceiveBillTypeEnum {

    PURCHASE_ORDER_RECEIVE(1, "采购订单收货", BillTypeEnum.PURCHASE_ORDER_RECEIVE),
    REJECT_RECEIVE(2, "拒收收货", BillTypeEnum.REJECT_RECEIVE),
    RETURN_RECEIVE(3, "退货收货", BillTypeEnum.RETURN_RECEIVE),
    ALLOCATION_RECEIVE(4, "调剂收货", BillTypeEnum.ALLOCATION_RECEIVE),
    REQUISITION_RECEIVE(5, "要货收货", BillTypeEnum.REQUISITION_RECEIVE),
    DISTRIBUTION_RECEIVE(6, "铺货收货", BillTypeEnum.DISTRIBUTION_RECEIVE);

    /**
     * 类型码
     */
    private final Integer code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 单据类型
     */
    private final BillTypeEnum billType;

    /**
     * 根据类型码获取枚举
     */
    public static ReceiveBillTypeEnum fromCode(Integer code) {
        for (ReceiveBillTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw exception(RECEIVE_BILL_TYPE_NOT_EXISTS);
    }
}