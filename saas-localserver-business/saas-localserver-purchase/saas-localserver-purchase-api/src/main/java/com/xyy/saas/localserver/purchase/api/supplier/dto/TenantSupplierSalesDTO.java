package com.xyy.saas.localserver.purchase.api.supplier.dto;

import com.xyy.saas.localserver.entity.pojo.BaseDto;
import com.xyy.saas.localserver.entity.pojo.TenantDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.time.LocalDateTime;

/**
 * 租户供应商销售 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "租户供应商销售 DTO")
@Data
@Accessors(chain = true)
public class TenantSupplierSalesDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 供应商编号 */
    @Schema(description = "供应商编号")
    private String supplierGuid;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;

    /** 销售人员姓名 */
    @Schema(description = "销售人员姓名")
    private String salesName;

    /** 授权区域 */
    @Schema(description = "授权区域")
    private String authorizedArea;

    /** 授权书号 */
    @Schema(description = "授权书号")
    private String authorizationNum;

    /** 授权书号有效期 */
    @Schema(description = "授权书号有效期")
    private LocalDateTime authorizationNumExpirationDate;

    /** 手机号码 */
    @Schema(description = "手机号码")
    private String phoneNumber;

    /** 授权信息 */
    @Schema(description = "授权信息")
    private String authorizedVarieties;

    /** 身份证号 */
    @Schema(description = "身份证号")
    private String idCard;

    /** 身份证有效期 */
    @Schema(description = "身份证有效期")
    private LocalDateTime idCardExpirationDate;

    /** 身份证附件 */
    @Schema(description = "身份证附件")
    private String idCardAttachment;

    /** 授权书附件 */
    @Schema(description = "授权书附件")
    private String authorizationAttachment;

    /** 经营范围 */
    @Schema(description = "经营范围")
    private String authorizedScope;
}