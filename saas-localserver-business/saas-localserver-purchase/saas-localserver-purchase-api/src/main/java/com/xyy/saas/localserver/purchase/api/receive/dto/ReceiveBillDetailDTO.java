package com.xyy.saas.localserver.purchase.api.receive.dto;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.localserver.entity.pojo.BaseDto;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收货单明细 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "收货单明细 DTO")
@Data
@Accessors(chain = true)
public class ReceiveBillDetailDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;

    /** 验收入库单号 */
    @Schema(description = "验收入库单号")
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    private String lotNo;

    /** 生产日期（YYYY-MM-DD） */
    @Schema(description = "生产日期")
    private LocalDate productionDate;

    /** 有效期至（YYYY-MM-DD） */
    @Schema(description = "有效期")
    private LocalDate expiryDate;

    /** 含税成本价（单价） */
    @Schema(description = "含税成本价")
    private BigDecimal price;

    /** 税率（百分比） */
    @Schema(description = "税率")
    private BigDecimal taxRate;

    /** 折扣（百分比） */
    @Schema(description = "折扣")
    private BigDecimal discount;

    /** 折后含税单价 */
    @Schema(description = "折后含税单价")
    private BigDecimal discountedPrice;

    /** 到货数量（发货数量） */
    @Schema(description = "到货数量")
    private BigDecimal arriveQuantity;

    /** 收货数量 */
    @Schema(description = "收货数量")
    private BigDecimal receiveQuantity;

    /** 拒收数量 */
    @Schema(description = "拒收数量")
    private BigDecimal rejectQuantity;

    /** 收货金额（折后单价*收货数量） */
    @Schema(description = "收货金额")
    private BigDecimal receiveAmount;

    /** 验收结论（1-合格、0-锁定） */
    @Schema(description = "验收结论")
    private Boolean acceptConclusion;

    /** 抽样数量 */
    @Schema(description = "抽样数量")
    private BigDecimal sampleQuantity;

    /** 不合格品数量 */
    @Schema(description = "不合格品数量")
    private BigDecimal unqualifiedQuantity;

    /** 不合格品总金额（折后总金额） */
    @Schema(description = "不合格品总金额")
    private BigDecimal unqualifiedAmount;

    /** 不合格原因 */
    @Schema(description = "不合格原因")
    private String unqualifiedReason;

    /** 不合格品隔离区编码 */
    @Schema(description = "不合格品隔离区编码")
    private String unqualifiedPositionGuid;

    /** 合格品数量 */
    @Schema(description = "合格品数量")
    private BigDecimal qualifiedQuantity;

    /** 合格品总金额（折后总金额） */
    @Schema(description = "合格品总金额")
    private BigDecimal qualifiedAmount;

    /** 合格品储存区编码 */
    @Schema(description = "合格品储存区编码")
    private String qualifiedPositionGuid;

    /** 入库数量 */
    @Schema(description = "入库数量")
    private BigDecimal warehouseQuantity;

    /** 入库金额（折后总金额） */
    @Schema(description = "入库金额")
    private BigDecimal warehouseAmount;

    /** 处理措施 */
    @Schema(description = "处理措施")
    private String treatment;

    /** 渠道ID */
    @Schema(description = "渠道ID")
    private String channelId;

    /** 灭菌批次 */
    @Schema(description = "灭菌批次")
    private String sterilizationBatchNo;

    /** 源行号（和三方ERP对接时需要） */
    @Schema(description = "源行号")
    private String sourceLineNo;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息（当前商品信息） */
    @Schema(description = "扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;

    /** 药品类型 */
    @Schema(description = "药品类型")
    private Integer medicineType;

    /** 商品信息 */
    @Schema(description = "商品信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductInfoDto productInfo;
}