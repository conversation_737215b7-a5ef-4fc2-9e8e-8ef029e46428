package com.xyy.saas.localserver.purchase.api.purchase.dto;

import com.xyy.saas.localserver.entity.pojo.BaseDto;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 采购单明细 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "采购单明细 DTO")
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseBillDetailDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 租户编号 */
    @Schema(description = "租户编号")
    private Long tenantId;

    /** 计划单号 */
    @Schema(description = "计划单号")
    private String planBillNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    private String purchaseBillNo;

    /** 商品编码 */
    @Schema(description = "商品编码")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    private String lotNo;

    /** 生产日期（YYYY-MM-DD） */
    @Schema(description = "生产日期")
    private LocalDate productionDate;

    /** 有效期至（YYYY-MM-DD） */
    @Schema(description = "有效期")
    private LocalDate expiryDate;

    /** （合格）存储区编号 */
    @Schema(description = "存储区编号")
    private String positionGuid;

    /** 进项税率（百分比） */
    @Schema(description = "进项税率")
    private BigDecimal inTaxRate;

    /** 销项税率（百分比） */
    @Schema(description = "销项税率")
    private BigDecimal outTaxRate;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称")
    private String supplierName;

    /** 采购单价 */
    @Schema(description = "采购单价")
    private BigDecimal price;

    /** 采购总金额 */
    @Schema(description = "采购总金额")
    private BigDecimal purchaseAmount;

    /** 采购数量 */
    @Schema(description = "采购数量")
    private BigDecimal purchaseQuantity;

    /** 已发货数量 */
    @Schema(description = "已发货数量")
    private BigDecimal deliveredQuantity;

    /** 可退数量（原单退货扣减可退数量，下游收货单的实际入库数量） */
    @Schema(description = "可退数量")
    private BigDecimal returnableQuantity;

    /** 渠道ID */
    @Schema(description = "渠道ID")
    private String channelId;

    /** 来源行号 */
    @Schema(description = "来源行号")
    private String sourceLineNo;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级")
    private String medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量")
    private BigDecimal medicareMinPackageNum;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;

    /** 入库数量 */
    @Schema(description = "入库数量")
    private BigDecimal warehouseQuantity;
}