package com.xyy.saas.localserver.purchase.api.supplier.dto;

import com.xyy.saas.localserver.entity.pojo.BaseDto;
import com.xyy.saas.localserver.entity.pojo.TenantDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 租户供应商关系 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "租户供应商关系 DTO")
@Data
@Accessors(chain = true)
public class TenantSupplierRelationDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    private String supplierGuid;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;

    /** 状态 */
    @Schema(description = "状态")
    private Integer status;

    /** 供应商信息 */
    @Schema(description = "供应商信息")
    private SupplierDTO supplier;
}