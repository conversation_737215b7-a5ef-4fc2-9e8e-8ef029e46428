package com.xyy.saas.localserver.purchase.api.receive.dto;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.localserver.entity.pojo.BaseDto;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoiceDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 收货单 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "收货单 DTO")
@Data
@Accessors(chain = true)
public class ReceiveBillDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 收货（收货/验收/入库）单号 */
    @Schema(description = "收货单号")
    private String billNo;

    /** 来源单号 */
    @Schema(description = "来源单号")
    private String sourceBillNo;

    /** 配送出库单号 */
    @Schema(description = "配送出库单号")
    private String deliveryBillNo;

    /** 随货同行单号 */
    @Schema(description = "随货同行单号")
    private String shipmentNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号")
    private String mallOrderNo;

    /** 出库门店租户id */
    private Long outboundTenantId;

    /** 租户编号 */
    @Schema(description = "租户编号")
    private Long tenantId;

    /** 租户类型（1-单体门店、2-连锁门店、3-连锁总部） */
    @Schema(description = "租户类型")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID")
    private Long headTenantId;

    /** 综合单据号（单号混合） */
    @Schema(description = "综合单据号")
    private String compositeBillNo;

    /** 单据类型（1-采购收货、2-拒收收货、3-退货收货、4-调剂收货、5-要货收货、6-铺货收货） */
    @Schema(description = "单据类型")
    private Integer billType;

    /** 采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓）） */
    @Schema(description = "采购方式")
    private Integer purchaseMode;

    /** 状态（1-待收货、2-待验收、3-待入库、4-已入库、5-已拒收） */
    @Schema(description = "状态")
    private Integer status;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称")
    private String supplierName;

    /** 供应商销售员 */
    @Schema(description = "供应商销售员")
    private String supplierSales;

    /** 商品种类 */
    @Schema(description = "商品种类")
    private Integer productKind;

    /** 收货数量 */
    @Schema(description = "收货数量")
    private BigDecimal receiveQuantity;

    /** 折扣（百分比） */
    @Schema(description = "折扣")
    private BigDecimal discount;

    /** 折扣总金额 */
    @Schema(description = "折扣总金额")
    private BigDecimal discountAmount;

    /** 收货内容 */
    @Schema(description = "收货内容")
    private String receiveContent;

    /** 收货金额（折后总金额） */
    @Schema(description = "收货金额")
    private BigDecimal receiveAmount;

    /** 配送员 */
    @Schema(description = "配送员")
    private String deliverer;

    /** 配送出库时间 */
    @Schema(description = "配送出库时间")
    private LocalDateTime deliveryTime;

    /** 收货员 */
    @Schema(description = "收货员")
    private String receiver;

    /** 收货时间 */
    @Schema(description = "收货时间")
    private LocalDateTime receiveTime;

    /** 验收员 */
    @Schema(description = "验收员")
    private String accepter;

    /** 验收时间 */
    @Schema(description = "验收时间")
    private LocalDateTime acceptTime;

    /** 入库员 */
    @Schema(description = "入库员")
    private String warehouser;

    /** 入库时间 */
    @Schema(description = "入库时间")
    private LocalDateTime warehouseTime;

    /** 复核员 */
    @Schema(description = "复核员")
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    private LocalDateTime checkTime;

    /** 质检员 */
    @Schema(description = "质检员")
    private String qualityInspector;

    /** 质检报告单 */
    @Schema(description = "质检报告单")
    private String qualityInspectionReport;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;

    /** 版本(乐观锁) */
    @Schema(description = "版本")
    private Integer version;

    /** 是否提交 */
    @Schema(description = "是否提交")
    private Boolean submitted;

    /** 下单人 */
    @Schema(description = "下单人")
    private String purchaser;

    /** 下单时间 */
    @Schema(description = "下单时间")
    private LocalDateTime purchaseTime;

    /** 收货明细 */
    @Schema(description = "收货明细")
    private List<ReceiveBillDetailDTO> details;

    /** 运输信息 */
    @Schema(description = "运输信息")
    private PurchaseTransportDTO transport;

    /** 发票信息 */
    @Schema(description = "发票信息")
    private PurchaseInvoiceDTO invoice;

    /** 三方ERP单据信息 */
    @Schema(description = "三方ERP单据信息")
    private PurchaseErpBillDTO erpBill;

    /** 调剂单信息 */
    @Schema(description = "调剂单信息")
    private PurchaseBillDTO allocationBill;

}