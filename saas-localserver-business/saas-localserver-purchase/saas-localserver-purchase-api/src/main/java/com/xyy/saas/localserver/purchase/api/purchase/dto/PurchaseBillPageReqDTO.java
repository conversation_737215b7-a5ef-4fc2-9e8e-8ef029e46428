package com.xyy.saas.localserver.purchase.api.purchase.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.localserver.entity.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 采购单分页查询 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "采购单分页查询 DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PurchaseBillPageReqDTO extends PageParam {

    /** 计划单号 */
    @Schema(description = "计划单号")
    private String planBillNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    private String purchaseBillNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号")
    private String mallOrderNo;

    /** 租户类型 */
    @Schema(description = "租户类型", example = "1")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", example = "29907")
    private Long headTenantId;

    /** 租户ID */
    @Schema(description = "租户ID", example = "29907")
    private Long tenantId;

    /** 入库租户ID */
    @Schema(description = "入库租户ID", example = "10271")
    private Long inboundTenantId;

    /** 出库租户ID */
    @Schema(description = "出库租户ID", example = "10271")
    private Long outboundTenantId;

    /** 综合单据号 */
    @Schema(description = "综合单据号")
    private String compositeBillNo;

    /** 单据类型 */
    @Schema(description = "单据类型")
    private Integer billType;

    /** 导入方式 */
    @Schema(description = "导入方式")
    private Integer importMode;

    /** 是否远程收货 */
    @Schema(description = "是否远程收货")
    private Boolean remoteReceived;

    /** 已提交 */
    @Schema(description = "已提交")
    private Boolean submitted;

    /** 采购状态 */
    @Schema(description = "采购状态", example = "2")
    private Integer status;

    /** 采购状态范围 */
    @Schema(description = "采购状态范围")
    private List<Integer> statusScope;

    /** 页面显示状态 */
    @Schema(description = "页面显示状态", example = "2")
    private Integer displayStatus;

    /** 页面显示状态名称 */
    @Schema(description = "页面显示状态名称", example = "2")
    private Integer displayStatusName;

    /** 药品类型 */
    @Schema(description = "药品类型", example = "1")
    private Integer medicineType;

    /** 采购方式 */
    @Schema(description = "采购方式")
    private Integer purchaseMode;

    /** 供应商编码 */
    @Schema(description = "供应商编码", example = "30225")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "张三")
    private String supplierName;

    /** 采购内容 */
    @Schema(description = "采购内容")
    private String purchaseContent;

    /** 采购总数量 */
    @Schema(description = "采购总数量")
    private BigDecimal purchaseQuantity;

    /** 采购金额 */
    @Schema(description = "采购金额")
    private BigDecimal purchaseAmount;

    /** 已发货数量 */
    @Schema(description = "已发货数量")
    private BigDecimal deliveredQuantity;

    /** 可退总数量 */
    @Schema(description = "可退总数量")
    private BigDecimal returnableQuantity;

    /** 商品种类 */
    @Schema(description = "商品种类")
    private Integer productKind;

    /** 计划员 */
    @Schema(description = "计划员")
    private String planner;

    /** 计划时间 */
    @Schema(description = "计划时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] planTime;

    /** 采购员 */
    @Schema(description = "采购员")
    private String purchaser;

    /** 采购时间 */
    @Schema(description = "采购时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] purchaseTime;

    /** 复核员 */
    @Schema(description = "复核员")
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] checkTime;

    /** 收货人 */
    @Schema(description = "收货人")
    private String receiver;

    /** 收货人电话 */
    @Schema(description = "收货人电话")
    private String receiverPhone;

    /** 收货人所在区域 */
    @Schema(description = "收货人所在区域")
    private String receiverArea;

    /** 收货人地址 */
    @Schema(description = "收货人地址")
    private String receiverAddress;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    private String remark;

    /** 版本 */
    @Schema(description = "版本")
    private Integer version;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}