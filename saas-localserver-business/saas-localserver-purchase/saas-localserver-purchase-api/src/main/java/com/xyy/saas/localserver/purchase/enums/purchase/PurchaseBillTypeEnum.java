package com.xyy.saas.localserver.purchase.enums.purchase;

import com.xyy.saas.localserver.entity.enums.bill.BillTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PurchaseBillTypeEnum {

    PURCHASE_ORDER(1,"采购订单",false,BillTypeEnum.PURCHASE_ORDER),
    STORE_REQUISITION(2, "门店要货",true,BillTypeEnum.STORE_REQUISITION),
    STORE_ALLOCATION(3, "门店调剂",false,BillTypeEnum.STORE_ALLOCATION),
    HEADQUARTERS_DISTRIBUTION(4, "总部铺货",false,BillTypeEnum.HEADQUARTERS_DISTRIBUTION);

    private final Integer code;
    private final String description;
    private final Boolean allowPartialCampOn;
    /**
     * 单据类型
     */
    private final BillTypeEnum billType;

    /**
     * 根据编码获取枚举实例
     */
    public static PurchaseBillTypeEnum fromCode(Integer code) {
        for (PurchaseBillTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的单据来源编码: " + code);
    }


}