package com.xyy.saas.localserver.purchase.api.stockout.dto;

import com.xyy.saas.localserver.entity.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.List;

/**
 * 出库单 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "出库单 DTO")
@Data
@Accessors(chain = true)
public class StockoutBillDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;

    /** 单号 */
    @Schema(description = "单号")
    private String billNo;

    /** 门店租户ID */
    @Schema(description = "门店租户ID")
    private Long storeTenantId;

    /** 要货单号(连锁才有) */
    @Schema(description = "要货单号")
    private String requisitionBillNo;

    /** 配送单号 */
    @Schema(description = "配送单号")
    private String deliveryBillNo;

    /** 综合单据号（单号混合） */
    @Schema(description = "综合单据号")
    private String compositeBillNo;

    /** 状态（0-缺货、1-已完成） */
    @Schema(description = "状态")
    private Integer status;

    /** 商品种类 */
    @Schema(description = "商品种类")
    private Integer productKind;

    /** 缺货内容 */
    @Schema(description = "缺货内容")
    private String stockoutContent;

    /** 缺货数量 */
    @Schema(description = "缺货数量")
    private BigDecimal stockoutQuantity;

    /** 要货数量 */
    @Schema(description = "要货数量")
    private BigDecimal requireQuantity;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;

    /** 版本(乐观锁) */
    @Schema(description = "版本")
    private Integer version;

    /** 缺货单详情 */
    @Schema(description = "缺货单详情")
    private List<StockoutBillDetailDTO> details;
}