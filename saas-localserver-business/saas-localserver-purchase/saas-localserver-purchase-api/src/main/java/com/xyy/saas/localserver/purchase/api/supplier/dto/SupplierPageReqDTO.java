package com.xyy.saas.localserver.purchase.api.supplier.dto;

import lombok.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 供应商信息分页查询 Request DTO
 *
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SupplierPageReqDTO extends PageParam {

    // ========== 基础信息 ==========

    /** 供应商编码 */
    private String guid;

    /** 商城供应商编码 */
    private String sourceSupplierGuid;

    /** 供应商名称 */
    private String name;

    /** 供应商类别 */
    private Integer type;

    /** 助记码 */
    private String mnemonicCode;

    /** 系统默认 */
    private Boolean systemDefault;

    /** 来源 */
    private Long source;

    // ========== 资质信息 ==========

    /** 法定代表人 */
    private String legalRepresentative;

    /** 注册地址 */
    private String registeredAddress;

    /** 经营范围 */
    private String businessScope;

    /** 营业执照编码 */
    private String businessLicense;

    /** 发证机关 */
    private String licenceAuthority;

    /** 是否三证合一 */
    private Boolean triCertMerged;

    /** 开户银行 */
    private String depositBank;

    /** 银行账号 */
    private String bankAccount;

    /** 开户户名 */
    private String accountName;

    /** 组织机构代码 */
    private String organizationCertificationCode;

    /** 组织机构税务登记号 */
    private String organizationCertificationTaxNo;

    /** 组织机构代码证发证机关 */
    private String organizationCertificationAuthority;

    // ========== 地址信息 ==========

    /** 注册地址code */
    private String registeredAddressCod;

    /** 仓库地址code */
    private String storeAddressCode;

    /** 仓库明细地址 */
    private String storeAddress;

    // ========== 附件信息 ==========

    /** 印章印模附件 */
    private String signet;

    /** 随货同行单样式附件 */
    private String shipmentTemplate;

    /** 资质与经营范围json数据 */
    private String qualificationInfos;

    // ========== 委托人信息 ==========

    /** 委托人身份证号 */
    private String proxyIdCard;

    // ========== 码上放心信息 ==========

    /** 码上放心-企业唯一标识 */
    private String msfxRefEntId;

    /** 码上放心-企业ID */
    private String msfxEntId;

    /** 关联分发业务 */
    private Boolean relateDistribute;

    // ========== 其他信息 ==========

    /** 备注 */
    private String remark;

    /** 是否禁用 */
    private Boolean disabled;

    // ========== 时间范围 ==========

    /** 注册日期 */
    private LocalDate[] registeredDate;

    /** 有效期至 */
    private LocalDate[] expirationDate;

    /** 有效期至方式 */
    private Integer expirationDateType;

    /** 组织机构发证日期 */
    private LocalDate[] organizationCertificationDate;

    /** 组织机构有效期至 */
    private LocalDate[] organizationCertificationExpirationDate;

    /** 委托人身份证有效期 */
    private LocalDate[] proxyIdCardExpirationDate;

    /** 创建时间 */
    private LocalDateTime[] createTime;
}