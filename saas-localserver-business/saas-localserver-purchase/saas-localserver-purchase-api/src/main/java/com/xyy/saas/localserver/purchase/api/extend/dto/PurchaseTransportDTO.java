package com.xyy.saas.localserver.purchase.api.extend.dto;

import com.xyy.saas.localserver.entity.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购运输信息 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "采购运输信息 DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PurchaseTransportDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;

    /** 采购单/收货单/配送单号 */
    @Schema(description = "采购单/收货单/配送单号")
    private String billNo;

    /** 委托经办人 */
    @Schema(description = "委托经办人")
    private String authorizedRepresentative;

    /** 承运人 */
    @Schema(description = "承运人")
    private String carrierName;

    /** 承运单位名称 */
    @Schema(description = "承运单位名称")
    private String carrierEntity;

    /** 承运单位统一社会信用代码（国家标准长度） */
    @Schema(description = "承运单位统一社会信用代码（国家标准长度）")
    private String carrierEntityUscc;

    /** 运输方式（如：road/air/ship） */
    @Schema(description = "运输方式（如：road/air/ship）")
    private String transportMode;

    /** 启运地址 */
    @Schema(description = "启运地址")
    private String departureAddress;

    /** 实际启运时间 */
    @Schema(description = "实际启运时间")
    private LocalDateTime departureTime;

    /** 启运温度（℃） */
    @Schema(description = "启运温度（℃）")
    private BigDecimal departureTemperature;

    /** 启运湿度（%） */
    @Schema(description = "启运湿度（%）")
    private BigDecimal departureHumidity;

    /** 运单号 */
    @Schema(description = "运单号")
    private String trackingNo;

    /** 运输工具类型（1-汽运、2-货运、3-冷藏车、4-冷藏箱、5-保温箱、6-其他） */
    @Schema(description = "运输工具类型（1-汽运、2-货运、3-冷藏车、4-冷藏箱、5-保温箱、6-其他）")
    private Integer transportVehicle;

    /** 运输工具标识（车牌号/航班号等） */
    @Schema(description = "运输工具标识（车牌号/航班号等）")
    private String vehicleIdentifier;

    /** 驾驶员姓名 */
    @Schema(description = "驾驶员姓名")
    private String driver;

    /** 驾驶员证件 */
    @Schema(description = "驾驶员证件")
    private String driverCredential;

    /** 运输温度（℃）监控（json格式，每天的温度） */
    @Schema(description = "运输温度（℃）监控（json格式，每天的温度）")
    private String transportTemperatureMonitor;

    /** 运输湿度（%）监控（json格式，每天的湿度） */
    @Schema(description = "运输湿度（%）监控（json格式，每天的湿度）")
    private String transportHumidityMonitor;

    /** 到货时间 */
    @Schema(description = "到货时间")
    private LocalDateTime arrivalTime;

    /** 到货温度（℃） */
    @Schema(description = "到货温度（℃）")
    private BigDecimal arrivalTemperature;

    /** 到货湿度（%） */
    @Schema(description = "到货湿度（%）")
    private BigDecimal arrivalHumidity;

    /** 随货同行单时间 */
    @Schema(description = "随货同行单时间")
    private LocalDateTime shipmentTime;

    /** 随货同行单文件地址 */
    @Schema(description = "随货同行单文件地址")
    private String shipmentFileUrl;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;
}