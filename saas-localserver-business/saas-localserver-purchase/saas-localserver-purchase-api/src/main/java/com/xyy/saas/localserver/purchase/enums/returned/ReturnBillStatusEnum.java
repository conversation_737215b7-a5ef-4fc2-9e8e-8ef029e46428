package com.xyy.saas.localserver.purchase.enums.returned;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采购退货单状态枚举
 */
@Getter
@AllArgsConstructor
public enum ReturnBillStatusEnum {

    PENDING_APPROVAL(1, "待审批"),
    PENDING_OUTBOUND(2, "待出库"),
    PENDING_REVIEW(3, "待复核"),
    OUTBOUND(4, "已出库"),
    REJECTED(5, "已驳回"),
    REVOKED(6, "已撤销");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态码获取枚举
     */
    public static ReturnBillStatusEnum fromCode(Integer code) {
        for (ReturnBillStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}