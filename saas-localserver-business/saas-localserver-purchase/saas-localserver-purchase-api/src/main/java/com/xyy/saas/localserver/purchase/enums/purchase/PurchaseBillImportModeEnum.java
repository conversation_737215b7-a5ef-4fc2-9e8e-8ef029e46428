package com.xyy.saas.localserver.purchase.enums.purchase;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(description = "收货单导入方式枚举")
public enum PurchaseBillImportModeEnum {

    PURCHASE_PLAN(1, "采购计划（正常创建）"),
    ONE_STEP_WAREHOUSING(2, "一步入库"),
    API_PULL(3, "接口拉取（比如药帮忙订单）"),
    EXCEL_IMPORT(4, "excel导入");

    @Schema(description = "导入方式编码")
    private final Integer code;

    @Schema(description = "导入方式描述")
    private final String description;

    public static PurchaseBillImportModeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PurchaseBillImportModeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}