package com.xyy.saas.localserver.purchase.api.supplier.dto;

import lombok.*;
import java.time.LocalDateTime;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 租户-供应商关联关系分页查询 Request DTO
 *
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantSupplierRelationPageReqDTO extends PageParam {

    // ========== 关联信息 ==========

    /** 供应商编号 */
    private String supplierGuid;

    /** 首营状态：1-审核中，2-审核通过，3-审核未通过 */
    private Integer status;

    // ========== 时间范围 ==========

    /** 创建时间 */
    private LocalDateTime[] createTime;
}