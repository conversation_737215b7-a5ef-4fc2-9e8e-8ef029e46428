package com.xyy.saas.localserver.purchase.api.returned.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 退货单分页 Request DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "退货单分页 Request DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ReturnBillPageReqDTO extends PageParam {

    /** 单号 */
    @Schema(description = "单号")
    private String billNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    private String purchaseBillNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号")
    private String mallOrderNo;

    /** 入库门店租户ID */
    @Schema(description = "入库门店租户ID", example = "2524")
    private Long inboundTenantId;

    /** 租户类型 */
    @Schema(description = "租户类型（1-单体门店、2-连锁门店、3-连锁总部）", example = "1")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", example = "2524")
    private Long headTenantId;

    /** 综合单据号 */
    @Schema(description = "综合单据号")
    private String compositeBillNo;

    /** 单据类型 */
    @Schema(description = "单据类型（1-采购退货、2-门店退货、3-门店调剂）")
    private Integer billType;

    /** 状态 */
    @Schema(description = "状态（1-待审批、2-待出库、3-待复核、4-已出库、5-已驳回、6-已撤销）", example = "2")
    private Integer status;

    /** 页面显示状态 */
    @Schema(description = "页面显示状态", example = "2")
    private Integer displayStatus;

    /** 已提交 */
    @Schema(description = "已提交", example = "false")
    private Boolean submitted;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "李四")
    private String supplierName;

    /** 商品种类 */
    @Schema(description = "商品种类")
    private Integer productKind;

    /** 退货内容 */
    @Schema(description = "退货内容")
    private String returnContent;

    /** 退货数量 */
    @Schema(description = "退货数量")
    private BigDecimal returnQuantity;

    /** 退货总金额 */
    @Schema(description = "退货总金额")
    private BigDecimal returnAmount;

    /** 成本总金额 */
    @Schema(description = "成本总金额")
    private BigDecimal costAmount;

    /** 出库操作员 */
    @Schema(description = "出库操作员")
    private String operator;

    /** 出库时间 */
    @Schema(description = "出库时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] operateTime;

    /** 复核员 */
    @Schema(description = "复核员")
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] checkTime;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    private String remark;

    /** 版本 */
    @Schema(description = "版本")
    private Integer version;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}