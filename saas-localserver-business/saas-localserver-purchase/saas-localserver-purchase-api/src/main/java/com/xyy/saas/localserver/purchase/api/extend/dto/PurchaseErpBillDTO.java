package com.xyy.saas.localserver.purchase.api.extend.dto;

import com.xyy.saas.localserver.entity.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 采购ERP单据信息 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "采购ERP单据信息 DTO")
@Data
@Accessors(chain = true)
public class PurchaseErpBillDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;

    /** 采购单/收货单/配送单号 */
    @Schema(description = "采购单/收货单/配送单号")
    private String billNo;

    /** 三方erp订单号 */
    @Schema(description = "三方erp订单号")
    private String erpBillNo;

    /** 三方erp销售单号 */
    @Schema(description = "三方erp销售单号")
    private String salesBillNo;

    /** 三方erp出库单号 */
    @Schema(description = "三方erp出库单号")
    private String outboundBillNo;

    /** 三方erp入库单号 */
    @Schema(description = "三方erp入库单号")
    private String warehouseBillNo;

    /** 三方erp销售退回入库单号(神农XSTHRK) */
    @Schema(description = "三方erp销售退回入库单号(神农XSTHRK)")
    private String refundStorageBillNo;

    /** 综合单据号（单号混合） */
    @Schema(description = "综合单据号（单号混合）")
    private String compositeBillNo;

    /** 三方erp取消原因 */
    @Schema(description = "三方erp取消原因")
    private String cancelReason;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;
}