package com.xyy.saas.localserver.purchase.api.purchase.dto;

import com.xyy.saas.localserver.entity.pojo.BaseDto;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购单 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "采购单 DTO")
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseBillDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 计划单号 */
    @Schema(description = "计划单号")
    private String planBillNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    private String purchaseBillNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号")
    private String mallOrderNo;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;

    /** 租户类型（1-单体门店、2-连锁门店、3-连锁总部） */
    @Schema(description = "租户类型（1-单体门店、2-连锁门店、3-连锁总部）")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID")
    private Long headTenantId;

    /** 入库租户ID（主配） */
    @Schema(description = "入库租户ID（主配）")
    private Long inboundTenantId;

    /** 出库租户ID（调剂） */
    @Schema(description = "出库租户ID（调剂）")
    private Long outboundTenantId;

    /** 综合单据号（单号混合） */
    @Schema(description = "综合单据号（单号混合）")
    private String compositeBillNo;

    /** 单据来源（1-采购订单、2-门店要货、3-门店调剂、4-总部铺货） */
    @Schema(description = "单据来源（1-采购订单、2-门店要货、3-门店调剂、4-总部铺货）")
    private Integer billType;

    /** 导入方式（1-采购计划（正常创建）、2-一键入库 、3-接口拉取（比如药帮忙订单）、4-excel导入） */
    @Schema(description = "导入方式（1-采购计划（正常创建）、2-一键入库 、3-接口拉取（比如药帮忙订单）、4-excel导入）")
    private Integer importMode;

    /** 是否远程收货 */
    @Schema(description = "是否远程收货")
    private Boolean remoteReceived;

    /** 已提交（类同于是否暂存） */
    @Schema(description = "已提交（类同于是否暂存）")
    private Boolean submitted;

    /**
     * 采购状态（1-采购计划、2-已下单待审批、3-已审批待发货、4-发货中（部分发货）、5-已发货（全部发货）、6-已完成（已收货）、7-采购失败、8-已撤销（审批前撤销）、9-已驳回（审批不通过））
     */
    @Schema(description = "采购状态（1-采购计划、2-已下单待审批、3-已审批待发货、4-发货中（部分发货）、5-已发货（全部发货）、6-已完成（已收货）、7-采购失败、8-已撤销（审批前撤销）、9-已驳回（审批不通过））")
    private Integer status;

    /** 药品类型（1-中药、2-非中药） */
    @Schema(description = "药品类型（1-中药、2-非中药）")
    private Integer medicineType;

    /** 采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓）） */
    @Schema(description = "采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓））")
    private Integer purchaseMode;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称")
    private String supplierName;

    /** 采购内容 */
    @Schema(description = "采购内容")
    private String purchaseContent;

    /** 采购总数量（原单退货扣减可退数量） */
    @Schema(description = "采购总数量（原单退货扣减可退数量）")
    private BigDecimal purchaseQuantity;

    /** 采购金额（总金额） */
    @Schema(description = "采购金额（总金额）")
    private BigDecimal purchaseAmount;

    /** 已发货数量 */
    @Schema(description = "已发货数量")
    private BigDecimal deliveredQuantity;

    /** 可退总数量（原单退货扣减可退数量，下游收货单的实际入库数量） */
    @Schema(description = "可退总数量（原单退货扣减可退数量，下游收货单的实际入库数量）")
    private BigDecimal returnableQuantity;

    /** 商品种类 */
    @Schema(description = "商品种类")
    private Integer productKind;

    /** 计划员（计划审核员） */
    @Schema(description = "计划员（计划审核员）")
    private String planner;

    /** 计划时间（计划审核时间） */
    @Schema(description = "计划时间（计划审核时间）")
    private LocalDateTime planTime;

    /** 采购员（采购审核员） */
    @Schema(description = "采购员（采购审核员）")
    private String purchaser;

    /** 采购时间（采购审核时间） */
    @Schema(description = "采购时间（采购审核时间）")
    private LocalDateTime purchaseTime;

    /** 复核员 */
    @Schema(description = "复核员")
    private String checker;

    /** 复核时间（采购审核时间） */
    @Schema(description = "复核时间（采购审核时间）")
    private LocalDateTime checkTime;

    /** 收货人（门店收货人信息） */
    @Schema(description = "收货人（门店收货人信息）")
    private String receiver;

    /** 收货人电话 */
    @Schema(description = "收货人电话")
    private String receiverPhone;

    /** 收货人所在区域 */
    @Schema(description = "收货人所在区域")
    private String receiverArea;

    /** 收货人地址 */
    @Schema(description = "收货人地址")
    private String receiverAddress;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;

    /** 版本(乐观锁) */
    @Schema(description = "版本(乐观锁)")
    private Integer version;

    /** 采购单明细 */
    @Schema(description = "采购单明细")
    private List<PurchaseBillDetailDTO> details;

    /** 采购扩展信息-运输信息 */
    @Schema(description = "采购扩展信息-运输信息")
    private PurchaseTransportDTO transport;

    /** 三方ERP单据信息 */
    @Schema(description = "三方ERP单据信息")
    private PurchaseErpBillDTO erpBill;
}