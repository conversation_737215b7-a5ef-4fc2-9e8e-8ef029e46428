package com.xyy.saas.localserver.purchase.api.supplier.dto;

import lombok.*;
import java.time.LocalDateTime;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 租户-供应商销售人员分页查询 Request DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantSupplierSalesPageReqDTO extends PageParam {

    // ========== 基础信息 ==========

    /** 供应商编号 */
    private String supplierGuid;

    /** 销售人员姓名 */
    private String salesName;

    /** 手机号码 */
    private String phoneNumber;

    // ========== 授权信息 ==========

    /** 授权区域 */
    private String authorizedArea;

    /** 授权书号 */
    private String authorizationNum;

    /** 授权信息 */
    private String authorizedVarieties;

    /** 经营范围 */
    private String authorizedScope;

    // ========== 证件信息 ==========

    /** 身份证号 */
    private String idCard;

    /** 身份证附件 */
    private String idCardAttachment;

    /** 授权书附件 */
    private String authorizationAttachment;

    // ========== 时间范围 ==========

    /** 授权书号有效期 */
    private LocalDateTime[] authorizationNumExpirationDate;

    /** 身份证有效期 */
    private LocalDateTime[] idCardExpirationDate;

    /** 创建时间 */
    private LocalDateTime[] createTime;
}