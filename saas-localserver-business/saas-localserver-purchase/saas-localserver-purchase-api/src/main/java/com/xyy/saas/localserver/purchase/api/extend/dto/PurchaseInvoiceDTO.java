package com.xyy.saas.localserver.purchase.api.extend.dto;

import com.xyy.saas.localserver.entity.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购发票信息 DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "采购发票信息 DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PurchaseInvoiceDTO extends BaseDto {

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 租户ID */
    @Schema(description = "租户ID")
    private Long tenantId;

    /** 采购单/收货单/配送单号 */
    @Schema(description = "采购单/收货单/配送单号")
    private String billNo;

    /** 发票号 */
    @Schema(description = "发票号")
    private String invoiceNo;

    /** 发票代码 */
    @Schema(description = "发票代码")
    private String invoiceCode;

    /** 发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送 */
    @Schema(description = "发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送")
    private String invoiceType;

    /** 发票是否随货通行 0：不随行 1：随行，用于委托配送 */
    @Schema(description = "发票是否随货通行 0：不随行 1：随行，用于委托配送")
    private Boolean accompanyingShipment;

    /** 发票金额 */
    @Schema(description = "发票金额")
    private BigDecimal invoiceAmount;

    /** 发票文件名称 */
    @Schema(description = "发票文件名称")
    private String invoiceFileName;

    /** 发票文件地址 */
    @Schema(description = "发票文件地址")
    private String invoiceFileUrl;

    /** 实际开(发)票时间 */
    @Schema(description = "实际开(发)票时间")
    private LocalDateTime issuedTime;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;
}