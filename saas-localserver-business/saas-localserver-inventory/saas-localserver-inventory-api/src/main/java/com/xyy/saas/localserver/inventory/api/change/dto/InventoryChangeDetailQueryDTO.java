package com.xyy.saas.localserver.inventory.api.change.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 库存变动明细 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryChangeDetailQueryDTO implements Serializable {

    private static final long serialVersionUID = -6562662907173050388L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 来源类型
     */
    private Integer billType;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 货位guid
     */
    private String positionGuid;
    /**
     * 批次库存guid
     */
    private String batchGuid;
    /**
     * 供应商编号
     */
    private String supplierGuid;
    /**
     * 追踪维度
     */
    private String tracePref;
    /**
     * 来源追踪维度
     */
    private String sourceTracePref;
    /**
     * 总部追踪维度
     */
    private String rootTracePref;

}