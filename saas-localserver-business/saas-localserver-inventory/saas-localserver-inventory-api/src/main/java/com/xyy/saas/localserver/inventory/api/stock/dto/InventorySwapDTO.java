package com.xyy.saas.localserver.inventory.api.stock.dto;

import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 一入一出DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventorySwapDTO implements Serializable {

    private static final long serialVersionUID = 5503454662245096261L;

    /*租户id*/
    private Long tenantId;

    /* 业务类型 */
    @NotNull(message = "业务类型不能为空")
    private Integer inBillType;

    /* 业务单号 */
    @NotNull(message = "业务单号不能为空")
    private String inBillNo;

    /* 入库时间 */
    private LocalDateTime inTime;

    /* 业务类型 */
    @NotNull(message = "业务类型不能为空")
    private Integer outBillType;

    /* 业务单号 */
    @NotNull(message = "业务单号不能为空")
    private String outBillNo;

    //todo 此处涉及出库流程，需要增加出库时间参数

    private LocalDateTime outTime;

    /* 选取策略 */
    private InventorySelectEnum selectStrategy;

    /* 库存批号DTO */
    @NotEmpty(message = "库存批号列表不能为空")
    private List<InventorySelectItemDTO> items;

    /* 选取策略-库存批号DTO MAP对象 */
    @NotEmpty(message = "库存批号列表不能为空")
    private Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap;

}
