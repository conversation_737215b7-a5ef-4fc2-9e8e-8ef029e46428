package com.xyy.saas.localserver.inventory.api.campon.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 库存释放DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventorySimpleReleaseDTO implements Serializable {

    private static final long serialVersionUID = 7859508382447016665L;

    /*租户id*/
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    /* 预占单单号 */
    private String campOnNo;

    /* 来源单号/业务单号 */
    private String sourceNo;

}
