package com.xyy.saas.localserver.inventory.api.stock.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 库存移动DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryMoveDTO implements Serializable {

    private static final long serialVersionUID = -6188920556864070231L;

    /* 库存出库 */
    @NotNull(message = "库存出库不能为空")
    private InventoryReduceDTO inventoryReduce;

    /* 库存入库 */
    @NotNull(message = "库存入库不能为空")
    private InventoryIncreaseDTO inventoryIncrease;

}
