package com.xyy.saas.localserver.inventory.api.bill.dto;

import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存单据 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBillDTO implements Serializable {

    private static final long serialVersionUID = -7698197977290717788L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 单据类型：101-货位移动 102-盘点 103-报损报溢 104-库存拆零 105-质量复查
     */
    private Integer billType;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 来源类型
     */
    private Integer sourceType;
    /**
     * 来源编号
     */
    private String sourceNo;
    /**
     * 来源描述
     */
    private String sourceDescription;
    /**
     * 审批状态: 1-暂存 2-待检验 3-审批中 5-已驳回 6-已完成
     */
    private Integer status;
    /**
     * 操作员
     */
    private String operator;
    /**
     * 操作时间
     */
    private LocalDateTime operateTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扩展信息
     */
    private String ext;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 详情
     */
    private List<InventoryBillDetailDTO> list;

}