package com.xyy.saas.localserver.inventory.api.change.dto;

import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeChangeDTO;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存变动明细 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryChangeDetailDTO implements Serializable {

    private static final long serialVersionUID = 3066713083753920994L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 来源类型
     */
    private Integer billType;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 领域单据时间
     */
    private LocalDateTime billTime;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 货位guid
     */
    private String positionGuid;
    /**
     * 批次库存guid
     */
    private String batchGuid;
    /**
     * 追踪维度
     */
    private String tracePref;
    /**
     * 来源追踪维度
     */
    private String sourceTracePref;
    /**
     * 总部追踪维度
     */
    private String rootTracePref;
    /**
     * 供应商编号
     */
    private String supplierGuid;
    /**
     * 入库数量
     */
    private BigDecimal inNumber;
    /**
     * 入库单价
     */
    private BigDecimal inPrice;
    /**
     * 入库总金额
     */
    private BigDecimal inAmount;
    /**
     * 出库数量
     */
    private BigDecimal outNumber;
    /**
     * 出库单价
     */
    private BigDecimal outPrice;
    /**
     * 出库总金额
     */
    private BigDecimal outAmount;
    /**
     * 批次库存结存数量
     */
    private BigDecimal batchNumber;
    /**
     * 批次库存结存金额
     */
    private BigDecimal batchAmount;
    /**
     * 批号库存结存数量
     */
    private BigDecimal lotNumber;
    /**
     * 批号库存结存金额
     */
    private BigDecimal lotAmount;
    /**
     * 商品结存库存数量
     */
    private BigDecimal stockNumber;
    /**
     * 商品结存库存金额
     */
    private BigDecimal stockAmount;
    /**
     * 追溯码详情
     */
    private String changeDetail;
    /**
     * 商品成本价
     */
    private BigDecimal costPrice;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扩展信息
     */
    private String ext;
    /**
     * 来源guid
     */
    private String sourceGuid;
    /**
     * 总部guid
     */
    private String rootGuid;
    /**
     * 追溯码信息
     */
    private List<TraceCodeChangeDTO> traceCodes;

}