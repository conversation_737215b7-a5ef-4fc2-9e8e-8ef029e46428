package com.xyy.saas.localserver.inventory.api.stock.dto;

import com.xyy.saas.localserver.inventory.enums.AreaTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存处理结果DTO
 */
@Data
public class InventoryStockResultDTO implements Serializable {

    private static final long serialVersionUID = -6315418686221574310L;

    /**
     * 来源编号
     */
    private String sourceNo;

    /**
     * 来源单号
     */
    private String sourcePref;

    /**
     * 批号库存ID
     */
    private Integer lotId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 商品编号
     */
    private String productPref;

    /**
     * 批号
     */
    private String lotNumber;

    /**
     * 货架guid
     */
    private String positionGuid;

    /**
     * 货区类型
     */
    private AreaTypeEnum areaTypeEnum;

    /**
     * 生产日期
     */
    private Date producedDate;

    /**
     * 效期至
     */
    private Date expirationDate;

    /**
     * 供应商
     */
    private String providePref;

    /**
     * 变动数量
     */
    private BigDecimal changeNumber;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 总部数据Id
     */
    private String rootId;

    /**
     * 入库含税价
     */
    private BigDecimal inTaxPrice;

    /**
     * 入库时间
     */
    private Date inTime;

    /**
     * 预占ID
     */
    private Integer campOnBillId;

    /**
     * 预占详情ID
     */
    private Integer campOnBillDetailId;

}
