package com.xyy.saas.localserver.inventory.api.stock.dto;

import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeChangeDTO;
import com.xyy.saas.localserver.inventory.enums.AreaTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 库存选取列表 DTO
 */
@Data
@SuperBuilder
@NoArgsConstructor
public class InventorySelectItemDTO implements Serializable {

    private static final long serialVersionUID = 633005965898363949L;

    /* 商品编号 */
    private String productPref;

    /* 库存批号 */
    private String lotNo;

    /* 货架编号 */
    private String positionGuid;

    /* 变动数量 */
    private BigDecimal changeNumber;

    /* 货区 */
    private AreaTypeEnum areaType;

    /* 入库追踪维度 */
    private String tracePref;

    /* 各货位变动库存数量 */
    private Map<String, BigDecimal> positionChangeStock;

    /* 成本价 */
    private BigDecimal costPrice;

    /* 供应商guid */
    private String supplierGuid;

    /* 入库含税价 */
    private BigDecimal inTaxPrice;

    /* 入库税率 */
    private BigDecimal taxRate;

    /* 灭菌批号 */
    private String sterilizationBatchNo;

    /* root入库追踪维度 */
    private String rootTracePref;

    /* 追溯码信息 */
    private List<TraceCodeChangeDTO> traceCodes;

}
