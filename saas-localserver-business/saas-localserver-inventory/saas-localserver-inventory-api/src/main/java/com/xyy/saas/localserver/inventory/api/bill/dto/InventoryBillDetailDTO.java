package com.xyy.saas.localserver.inventory.api.bill.dto;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存单据详情 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBillDetailDTO implements Serializable {

    private static final long serialVersionUID = -7408560809868419250L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 货位guid
     */
    private String positionGuid;
    /**
     * 登记数量
     */
    private BigDecimal registerQuantity;
    /**
     * 变动数量
     */
    private BigDecimal changeQuantity;
    /**
     * 变动前数量
     */
    private BigDecimal beforeQuantity;
    /**
     * 实际数量
     */
    private BigDecimal actualQuantity;
    /**
     * 成本价
     */
    private BigDecimal costPrice;
    /**
     * 零售价
     */
    private BigDecimal retailPrice;
    /**
     * 库存金额
     */
    private BigDecimal stockAmount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扩展信息
     */
    private String ext;

}