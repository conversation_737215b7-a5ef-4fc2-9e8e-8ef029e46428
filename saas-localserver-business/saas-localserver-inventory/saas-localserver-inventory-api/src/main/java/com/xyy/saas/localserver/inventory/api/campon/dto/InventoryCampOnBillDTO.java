package com.xyy.saas.localserver.inventory.api.campon.dto;

import lombok.*;

import java.io.Serializable;

/**
 * 预占单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryCampOnBillDTO implements Serializable {

    private static final long serialVersionUID = -1898244964802337151L;

    private Long id;
    /**
     * 编号
     */
    private String billNo;
    /**
     * 来源类型
     */
    private Integer sourceType;
    /**
     * 来源单号
     */
    private String sourceNo;
    /**
     * 上级预占单编号
     */
    private String parentNo;
    /**
     * 状态: 1:预占中; 2:预占释放
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本号
     */
    private Long version;

}