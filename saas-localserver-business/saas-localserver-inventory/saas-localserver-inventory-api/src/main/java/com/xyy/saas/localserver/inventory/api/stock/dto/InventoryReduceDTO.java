package com.xyy.saas.localserver.inventory.api.stock.dto;

import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 库存出库DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryReduceDTO implements Serializable {

    private static final long serialVersionUID = -6188920556795970231L;

    /*租户id*/
    private Long tenantId;

    /* 业务类型 */
    @NotNull(message = "业务类型不能为空")
    private Integer billType;

    /* 业务单号 */
    @NotNull(message = "业务单号不能为空")
    private String billNo;

    /* 出库时间 */
    private LocalDateTime outTime;

    /* 选取策略-库存批号DTO MAP对象 */
    @NotEmpty(message = "库存批号列表不能为空")
    private Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap;

}
