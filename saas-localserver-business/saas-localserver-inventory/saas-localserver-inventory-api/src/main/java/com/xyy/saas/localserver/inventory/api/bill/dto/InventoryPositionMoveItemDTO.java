package com.xyy.saas.localserver.inventory.api.bill.dto;

import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeChangeDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 库存单据详情 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryPositionMoveItemDTO implements Serializable {

    private static final long serialVersionUID = 6332471520091190854L;

    /**
     * 源货位guid
     */
    private String originalPositionGuid;
    /**
     * 目标货位guid
     */
    private String targetPositionGuid;
    /**
     * 变动数量
     */
    private BigDecimal changeQuantity;
    /**
     *  追溯码信息
     */
    private List<TraceCodeChangeDTO> traceCodes;

}