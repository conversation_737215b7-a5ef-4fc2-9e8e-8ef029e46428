package com.xyy.saas.localserver.inventory.api.stock.dto;

import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存处理DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryStockDTO implements Serializable {

    private static final long serialVersionUID = 3104887760922034152L;

    /* 业务类型 */
    private Integer billType;

    /* 业务单号 */
    private String billNo;

    /* 出入库时间 */
    private LocalDateTime stockTime;

    /* 选取策略 */
    private InventorySelectEnum selectStrategy;

    /* 是否允许选择部分库存 */
    private boolean allowSelectPartial;

    /* 库存批号DTO */
    @NotEmpty(message = "库存批号列表不能为空")
    private List<InventorySelectItemDTO> items;

    /* 批次DTO */
    private List<InventorySelectBatchItemDTO> batchList;

}
