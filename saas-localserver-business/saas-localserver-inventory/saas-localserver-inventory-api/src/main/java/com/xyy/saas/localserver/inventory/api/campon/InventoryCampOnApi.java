package com.xyy.saas.localserver.inventory.api.campon;

import com.xyy.saas.localserver.inventory.api.campon.dto.*;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;

import java.util.List;

/**
 * 库存预占Api
 */
public interface InventoryCampOnApi {

    /**
     * 对指定库存进行预占操作（即锁定库存）。
     *
     * @param inventoryCampOn 预占请求参数，包含商品信息、数量、批号等
     * @return 返回预占结果信息，如预占单据 ID、实际预占数量等
     */
    InventoryCampOnResultDTO campOn(InventoryCampOnDTO inventoryCampOn);

    /**
     * 对指定库存进行批量预占操作（即锁定库存）。
     *
     * @param inventoryCampOnList 预占请求参数，包含商品信息、数量、批号等
     * @return 返回预占结果信息，如预占单据 ID、实际预占数量等
     */
    List<InventoryCampOnResultDTO> batchCampOn(List<InventoryCampOnDTO> inventoryCampOnList);

    /**
     * 释放已预占的库存（仅释放，不触发出库）。
     *
     * @param inventorySimpleRelease 释放预占请求参数
     * @return 返回释放结果信息
     */
    InventoryCampOnResultDTO releaseCampOn(InventorySimpleReleaseDTO inventorySimpleRelease);

    /**
     * 释放预占库存并完成出库操作。
     *
     * @param inventorySimpleRelease 出库释放请求参数
     * @return 返回库存变动明细列表，记录此次出库账页信息
     */
    List<InventoryChangeDetailDTO> releaseStock(InventorySimpleReleaseDTO inventorySimpleRelease);

    /**
     * 根据释放请求参数，释放预占库存并完成出库操作。
     *
     * <p>适用于需要额外释放参数（如目标商品、指定数量等）的场景。</p>
     *
     * @param inventoryRelease 出库释放请求参数
     * @return 返回库存变动明细列表，记录此次出库账页信息
     */
    List<InventoryChangeDetailDTO> releaseStock(InventoryReleaseDTO inventoryRelease);

    /**
     * 查询预占单
     *
     * @param inventoryCampOnQuery 库存预占查询请求参数
     * @return 返回预占信息
     */
    InventoryCampOnResultDTO queryCampOnBill(InventoryCampOnQueryDTO inventoryCampOnQuery);

}
