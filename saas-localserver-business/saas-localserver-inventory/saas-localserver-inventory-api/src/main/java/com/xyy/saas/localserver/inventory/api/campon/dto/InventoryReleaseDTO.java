package com.xyy.saas.localserver.inventory.api.campon.dto;

import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 库存释放DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryReleaseDTO implements Serializable {

    private static final long serialVersionUID = -6188928904797260231L;

    /*租户id*/
    private Long tenantId;

    /* 业务单号 */
    private String billNo;

    /* 来源类型 */
    private Integer sourceType;

    /* 来源单号 */
    private String sourceNo;

    /* 选取策略 */
    @NotNull(message = "库存选取策略不能为空")
    private InventorySelectEnum selectStrategy;

    /* 库存批号DTO */
    @NotNull(message = "库存批号不能为空")
    private List<InventoryCampOnLotItemDTO> items;

}
