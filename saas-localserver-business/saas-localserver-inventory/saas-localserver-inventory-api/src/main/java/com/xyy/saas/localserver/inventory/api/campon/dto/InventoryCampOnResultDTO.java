package com.xyy.saas.localserver.inventory.api.campon.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 预占库存结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryCampOnResultDTO implements Serializable {

    private static final long serialVersionUID = -6316938686221574310L;

    private InventoryCampOnBillDTO inventoryCampOnBill;

    private List<InventoryCampOnBillDetailDTO> inventoryCampOnBillDetailList;

}
