package com.xyy.saas.localserver.inventory.api.campon.dto;

import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 库存预占DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryCampOnDTO implements Serializable {

    private static final long serialVersionUID = 1083451791821664300L;

    /*租户id*/
    private Long tenantId;

    /* 业务类型 */
    //todo 这里用枚举接收
    private Integer billType;

    /* 业务单号 */
    private String billNo;

    /* 是否允许选择部分库存，即是否允许部分预占 */
    private boolean allowSelectPartial;

    /* 选取策略-库存批号DTO MAP对象 */
    @NotEmpty(message = "库存批号列表不能为空")
    private Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap;

}
