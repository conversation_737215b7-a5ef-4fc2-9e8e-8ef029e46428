package com.xyy.saas.localserver.inventory.api.campon.dto;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预占单详情 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryCampOnBillDetailDTO implements Serializable {

    private static final long serialVersionUID = -8008683631595223638L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 预占单编号
     */
    private String billNo;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 货位guid
     */
    private String positionGuid;
    /**
     * 批次库存guid
     */
    private String batchGuid;
    /**
     * 追踪维度
     */
    private String tracePref;
    /**
     * 预占数量
     */
    private BigDecimal campOnNumber;
    /**
     * 释放数量
     */
    private BigDecimal releaseNumber;
    /**
     * 总预占数量
     */
    private BigDecimal totalCampOnNumber;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扩展信息
     */
    private String ext;

}