package com.xyy.saas.localserver.inventory.api.stock.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Desc 库存批次选取列表 DTO
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2025/4/22 下午8:25
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventorySelectBatchItemDTO extends InventorySelectItemDTO implements Serializable {

    private static final long serialVersionUID = 5794286888568618121L;

    /* 批次号id */
    private Long id;

    /* 批次号guid */
    private String guid;

    /* 库存数量 */
    private BigDecimal stockNumber;

    /* 预占数量 */
    private BigDecimal campOnNumber;

    /* 单据类型 */
    private Integer billType;

    /* 单据编号 */
    private String billNo;

    /* 总部guid */
    private String rootGuid;

    /* 入库时间 */
    private LocalDateTime inTime;

    /* 备注 */
    private String remark;

    /* 版本号 */
    private Long version;

}
