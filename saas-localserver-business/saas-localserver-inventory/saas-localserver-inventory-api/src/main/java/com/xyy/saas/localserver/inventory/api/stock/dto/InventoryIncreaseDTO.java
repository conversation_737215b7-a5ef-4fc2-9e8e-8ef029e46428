package com.xyy.saas.localserver.inventory.api.stock.dto;

import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 库存入库DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryIncreaseDTO implements Serializable {

    private static final long serialVersionUID = 3850811669183400677L;

    /*租户id*/
    private Long tenantId;

    /* 业务类型 */
    @NotNull(message = "业务类型不能为空")
    private Integer billType;

    /* 业务单号 */
    @NotNull(message = "业务单号不能为空")
    private String billNo;

    /* 入库时间 */
    private LocalDateTime inTime;

    /* 选取策略-库存批号DTO MAP对象 */
    @NotEmpty(message = "库存批号列表不能为空")
    private Map<InventorySelectEnum, List<InventorySelectItemDTO>> selectMap;

}
