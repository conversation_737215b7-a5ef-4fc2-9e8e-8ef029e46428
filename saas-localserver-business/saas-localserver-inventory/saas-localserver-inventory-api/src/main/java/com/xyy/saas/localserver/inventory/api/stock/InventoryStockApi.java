package com.xyy.saas.localserver.inventory.api.stock;

import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryIncreaseDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryMoveDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryReduceDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySwapDTO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 库存出入库服务Api，用于管理库存的增减、转移及不变操作。
 */
public interface InventoryStockApi {

    /**
     * 执行库存入库操作，增加指定仓库、批次、货品的库存数量。
     *
     * @param inventoryIncrease 入库参数 DTO，必须非空且通过校验
     * @return 返回生成的库存变动明细记录列表
     */
    List<InventoryChangeDetailDTO> increaseStock(@Valid InventoryIncreaseDTO inventoryIncrease);

    /**
     * 批量执行库存入库操作，增加指定仓库、批次、货品的库存数量。
     *
     * @param inventoryIncreaseList 入库参数 DTO 集合，必须非空且通过校验
     * @return 返回生成的库存变动明细记录列表
     */
    Map<String, List<InventoryChangeDetailDTO>> batchIncreaseStock(@Valid List<InventoryIncreaseDTO> inventoryIncreaseList);

    /**
     * 执行库存出库操作，减少指定仓库、批次、货品的库存数量。
     *
     * @param inventoryReduce 出库参数 DTO，必须非空且通过校验
     * @return 返回生成的库存变动明细记录列表
     */
    List<InventoryChangeDetailDTO> reduceStock(@Valid InventoryReduceDTO inventoryReduce);

    /**
     * 在不同仓库或库位之间移动库存。
     *
     * @param inventoryMove 移动参数 DTO，必须非空且通过校验
     * @return 操作是否成功
     */
    boolean moveStock(@Valid InventoryMoveDTO inventoryMove);

    /**
     * 执行一入一出操作，库存总量不变。
     *
     * @param inventorySwap 参数 DTO，必须非空且通过校验
     * @return 操作是否成功
     */
    boolean swapStock(@Valid InventorySwapDTO inventorySwap);

}
