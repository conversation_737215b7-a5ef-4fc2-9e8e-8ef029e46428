package com.xyy.saas.localserver.natives;

import cn.hutool.core.lang.ClassScanner;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.core.toolkit.support.SerializedLambda;
import org.graalvm.nativeimage.hosted.Feature;
import org.graalvm.nativeimage.hosted.RuntimeSerialization;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.core.type.filter.TypeFilter;
import org.springframework.util.ClassUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc 需要在native-maven-plugin.<configuration>.<buildArgs>
 * --features=cn.iocoder.yudao.server.natives.RuntimeRegistrationFeature
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/2/27 17:46
 */
public class RuntimeRegistrationFeature implements Feature {

    /**
     * 找到某个包下面指定的父类的所有子类
     *
     * @param packageName 包名
     *                    //     * @param superClass 父类
     * @return 子类集合
     */
    public static List<Class<?>> findClasses(String packageName, Class<?> superClass) {
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
        TypeFilter filter = new AssignableTypeFilter(superClass);
        scanner.addIncludeFilter(filter);

        List<Class<?>> classes = new ArrayList<>();
        String basePackage = ClassUtils.convertClassNameToResourcePath(packageName);
        for (BeanDefinition candidate : scanner.findCandidateComponents(basePackage)) {
            try {
                Class<?> clazz = Class.forName(candidate.getBeanClassName());
                classes.add(clazz);
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
                // 处理异常
            }
        }

        return classes;
    }

    @Override
    public void duringSetup(DuringSetupAccess access) {
//		扫描指定包下子类（实现类），然后全部注册到graalvm lamda 序列化中
        RuntimeSerialization.register(SerializedLambda.class, SFunction.class);
        RuntimeSerialization.registerLambdaCapturingClass(TableInfo.class);
        //需要native-maven-plugin.<configuration>.<buildArgs>新增
        //--initialize-at-build-time=相关类
//        ClassScanner.scanPackage("com.baomidou.mybatisplus.core")
//                .forEach(RuntimeSerialization::registerLambdaCapturingClass);
    }
}