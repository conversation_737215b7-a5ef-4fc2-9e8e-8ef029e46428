package com.xyy.saas.localserver.natives;

import cn.hutool.core.lang.ClassScanner;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.springframework.aot.hint.MemberCategory;
import org.springframework.aot.hint.RuntimeHints;
import org.springframework.aot.hint.RuntimeHintsRegistrar;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportRuntimeHints;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/2/20 20:17
 */
@Configuration(proxyBeanMethods = false)
@ImportRuntimeHints(VelocityNativeConfiguration.VelocityRuntimeHintsRegistrar.class)
public class VelocityNativeConfiguration {

    static class VelocityRuntimeHintsRegistrar implements RuntimeHintsRegistrar {
        @Override
        public void registerHints(RuntimeHints hints, ClassLoader classLoader) {
            String classPath = "org.apache.velocity.runtime";
            ClassScanner.scanPackage(classPath).forEach(x -> hints.reflection().registerType(x, MemberCategory.values()));
            ClassScanner.scanPackage("org.apache.velocity.util")
                    .forEach(x -> hints.reflection().registerType(x, MemberCategory.values()));
            hints.reflection().registerType(TableInfo.class, MemberCategory.values());
//            hints.reflection().registerType(JsonLongSetTypeHandler.class, MemberCategory.values());
            ClassScanner.scanPackage("cn.iocoder.yudao.framework.mybatis.core.type")
                    .forEach(x -> hints.reflection().registerType(x, MemberCategory.values()));
//            Stream.of(
//                    ContentResource.class,
//                    ResourceFactory.class,
//                    ResourceManagerImpl.class,
//                    ResourceCacheImpl.class,
//
//                    ClasspathResourceLoader.class,
//                    DataSourceResourceLoader.class,
//                    FileResourceLoader.class,
//                    JarHolder.class,
//                    JarResourceLoader.class,
//                    ResourceLoaderFactory.class,
//                    StringResourceLoader.class,
//                    URLResourceLoader.class,
//
//                    ParserConfiguration.class,
//                    ParserPoolImpl.class,
//                    LogContext.class,
//
//                    BlockMacro.class,
//                    Break.class,
//                    Define.class,
//                    Evaluate.class,
//                    Foreach.class,
//                    ForeachScope.class,
//                    Include.class,
//                    Macro.class,
//                    Parse.class,
//                    RuntimeMacro.class,
//                    Scope.class,
//                    Stop.class,
//                    StopCommand.class,
//                    VelocimacroProxy.class,
//
//                    SimpleNode.class
//                    ).forEach(x -> hints.reflection().registerType(x, MemberCategory.values()));
//            Stream.of(
//                    "org/apache/ibatis/builder/xml/*.dtd",
//                    "org/apache/ibatis/builder/xml/*.xsd"
//            ).forEach(hints.resources()::registerPattern);
        }
    }
}
