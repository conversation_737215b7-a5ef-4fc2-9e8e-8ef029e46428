<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <artifactId>saas-cloudserver-business</artifactId>
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>saas-cloudserver</artifactId>
        <groupId>com.xyy.saas</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <packaging>pom</packaging>

    <modules>
        <module>saas-cloudserver-purchase</module>
    </modules>

    <name>saas-cloudserver-business</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>saas-cloudserver-entity</artifactId>
            <version>${revision}</version>
            <exclusions>
                <exclusion>
                    <artifactId>yudao-module-system-api</artifactId>
                    <groupId>cn.iocoder.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-protection</artifactId>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement><!-- lock down plugins versions to avoid using Maven defaults (may be moved to parent pom) -->
        </pluginManagement>
    </build>
</project>
