spring:
  cloud:
    nacos:
      server-addr: ***********:8848
      discovery:
        namespace: ${spring.profiles.active}
        group: http
      config:
        namespace: ${spring.profiles.active}
        group: DEFAULT_GROUP
        server-addr: ***********:8848
        prefix: ${spring.application.name}
        file-extension: yaml
  config:
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.yaml

  datasource:
    dynamic:
      datasource:
        master:
          url: *************************************************************************************************************************************************************************************
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
        slave:
          lazy: true
          url: *************************************************************************************************************************************************************************************
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe

  data:
    redis:
      host: db01-medicare-test.redis.ybm100.top
      port: 30002
      password: JEf8CVrnY0G3RPEZ
      database: 15

rocketmq:
  name-server: ************:9876
  producer:
    group: saas_cloudserver_purchase_system

logging:
  level:
    com.baomidou.mybatisplus: info
