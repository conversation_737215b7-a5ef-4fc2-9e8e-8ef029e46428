package com.xyy.saas.cloudserver.purchase.server.service.bpm.handler;

import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.api.bpm.handler.BpmApprovalHandler;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 审核门店调拨单监听（门店审核/总部审核）
 */
@Slf4j
@Component
public class StoreAllocationApprovalHandler implements BpmApprovalHandler {


    @Override
    public void handleApproval(BpmBusinessRelationDto businessDto) {

        // 1. 判断审批是否通过，如果审批通过，则保存消息表，mqtt通知出库门店退货流程（调用调剂退货service）

    }

    @Override
    public BpmBusinessTypeEnum[] supportBusinessTypes() {

        //监听门店调剂单

        return new BpmBusinessTypeEnum[0];
    }
}