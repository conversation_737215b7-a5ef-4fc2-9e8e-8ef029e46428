package com.xyy.saas.cloudserver.purchase.server.service.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;

/**
 * 总部配送 Service 接口
 * <p>
 * 主要功能：
 * 1. 总部配送单基础操作（保存、删除、更新、查询）
 * 2. 总部配送单状态管理
 * 3. 总部配送单分页查询
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface HeadDistributionService {

    // ========== 1. 基础操作 ==========

    /**
     * 保存总部配送单并预占库存
     *
     * @param saveDTO 待保存的总部配送单信息
     */
    void saveDistributionBillAndCampOn(PurchaseBillDTO saveDTO);

    /**
     * 保存总部配送单并调用库存一入一出
     *
     * @param distributionBill 待保存的总部配送单信息
     * @param receiveBill      总部收货单信息
     */
    void saveDistributionBillAndTransferStock(PurchaseBillDTO distributionBill, ReceiveBillDTO receiveBill);

    /**
     * 删除总部配送单
     *
     * @param id 总部配送单编号
     */
    void deleteDistributionBill(Long id);

    /**
     * 更新总部配送单并刷新业务状态
     *
     * @param updateDTO 待更新的总部配送单信息
     */
    void updateDistributionBillWithStatusRefresh(PurchaseBillDTO updateDTO);

    // ========== 2. 查询操作 ==========

    /**
     * 获得总部配送单
     *
     * @param id 总部配送单编号
     * @return 总部配送单信息
     */
    PurchaseBillDTO getDistributionBill(Long id);

    /**
     * 获得总部配送单和详情
     *
     * @param billNo   总部配送单号
     * @param tenantId 租户ID
     * @return 总部配送单信息（包含详情）
     */
    PurchaseBillDTO getDistributionBillWithDetails(String billNo, Long tenantId);

    /**
     * 获得总部配送单分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 总部配送单分页结果
     */
    PageResult<PurchaseBillDTO> getDistributionBillPage(PurchaseBillPageReqDTO pageReqDTO);
}