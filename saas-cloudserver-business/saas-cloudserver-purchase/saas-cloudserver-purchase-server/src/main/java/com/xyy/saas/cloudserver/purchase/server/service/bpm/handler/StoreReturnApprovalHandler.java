package com.xyy.saas.cloudserver.purchase.server.service.bpm.handler;

import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.api.bpm.handler.BpmApprovalHandler;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 审核门店退货单监听
 */
@Slf4j
@Component
public class StoreReturnApprovalHandler implements BpmApprovalHandler {


    @Override
    public void handleApproval(BpmBusinessRelationDto businessDto) {

        // 1. 记录消息表，保存审核结果

        // 2. mqtt 通知本地端（门店端）修改要货单状态

    }

    @Override
    public BpmBusinessTypeEnum[] supportBusinessTypes() {

        //监听门店退货单

        return new BpmBusinessTypeEnum[0];
    }
}