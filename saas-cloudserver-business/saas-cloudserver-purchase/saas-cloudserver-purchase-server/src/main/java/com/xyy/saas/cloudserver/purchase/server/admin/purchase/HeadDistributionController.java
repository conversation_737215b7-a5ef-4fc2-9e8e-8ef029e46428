package com.xyy.saas.cloudserver.purchase.server.admin.purchase;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
import com.xyy.saas.cloudserver.purchase.server.service.purchase.HeadDistributionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 总部配送单 Controller
 * 处理总部配送单相关的业务操作，包括创建、更新、删除、查询等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - 总部配送单")
@RestController
@RequestMapping("/saas/purchase/head-distribution")
@Validated
public class HeadDistributionController {

    @Resource
    private HeadDistributionService headDistributionService;

    /**
     * 保存总部配送单
     * 处理流程：
     * 1. 校验收货信息
     * 2. 校验入库门店信息
     * 3. 对象转换：将VO对象转换为DTO对象
     * 4. 调用服务：保存配送单并关联业务
     *
     * @param saveReqVO 保存信息，包含收货和入库门店信息
     * @return 保存结果
     */
    @PostMapping("/save")
    @Operation(summary = "保存总部配送单")
    @PreAuthorize("@ss.hasPermission('saas:purchase:head-distribution:save')")
    public CommonResult<Boolean> save(@Valid @RequestBody PurchaseBillSaveReqVO saveReqVO) {
        // 1. 校验收货信息
        saveReqVO.validateReceiveInfo();

        // 2. 校验入库门店信息
        saveReqVO.validateInBoundStore();

        // 3. 对象转换：将VO对象转换为DTO对象
        PurchaseBillDTO distributionBill = PurchaseBillConvert.INSTANCE.convert2DistributionDTO(saveReqVO);

        // 4. 调用服务：保存配送单并关联业务
        headDistributionService.saveDistributionBillAndCampOn(distributionBill);

        return success(true);
    }

    /**
     * 确认收货并更新总部配送单
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：更新配送单并刷新业务状态
     *
     * @param saveReqVO 保存信息，包含收货确认信息
     * @return 更新结果
     */
    @PostMapping("/confirm-receive")
    @Operation(summary = "确认收货并更新总部配送单")
    @PreAuthorize("@ss.hasPermission('saas:purchase:head-distribution:confirm')")
    public CommonResult<Boolean> confirmDistributionReceive(@RequestBody PurchaseBillSaveReqVO saveReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseBillDTO distributionBill = PurchaseBillConvert.INSTANCE.convert2DTO(saveReqVO);

        // 2. 调用服务：更新配送单并刷新业务状态
        headDistributionService.updateDistributionBillWithStatusRefresh(distributionBill);

        return success(true);
    }

    /**
     * 删除总部配送单
     * 处理流程：
     * 1. 调用服务：执行删除操作
     *
     * @param id 配送单编号
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除总部配送单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:purchase:head-distribution:delete')")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        // 调用服务：执行删除操作
        headDistributionService.deleteDistributionBill(id);
        return success(true);
    }

    /**
     * 获取总部配送单信息
     * 处理流程：
     * 1. 调用服务：获取配送单信息
     * 2. 对象转换：将DTO对象转换为VO对象
     *
     * @param id 配送单编号
     * @return 配送单信息
     */
    @GetMapping("/get")
    @Operation(summary = "获得总部配送单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:purchase:head-distribution:query')")
    public CommonResult<PurchaseBillRespVO> get(@RequestParam("id") Long id) {
        // 1. 调用服务：获取配送单信息
        PurchaseBillDTO distributionBill = headDistributionService.getDistributionBill(id);
        // 2. 对象转换：将DTO对象转换为VO对象
        return success(PurchaseBillConvert.INSTANCE.convert2VO(distributionBill));
    }

    /**
     * 获取总部配送单分页信息
     * 处理流程：
     * 1. 处理分页查询参数
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：获取分页数据
     * 4. 对象转换：将DTO对象转换为VO对象
     *
     * @param pageReqVO 分页查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "获得总部配送单分页")
    @PreAuthorize("@ss.hasPermission('saas:purchase:head-distribution:query')")
    public CommonResult<PageResult<PurchaseBillRespVO>> getPage(@Valid PurchaseBillPageReqVO pageReqVO) {
        // 1. 处理分页查询参数
        PurchaseBillConvert.INSTANCE.processDistributionPageQueryParams(pageReqVO);
        // 2. 对象转换：将VO对象转换为DTO对象
        PurchaseBillPageReqDTO pageReqDTO = PurchaseBillConvert.INSTANCE.convert2DTO(pageReqVO);
        // 3. 调用服务：获取分页数据
        PageResult<PurchaseBillDTO> pageResult = headDistributionService.getDistributionBillPage(pageReqDTO);
        // 4. 对象转换：将DTO对象转换为VO对象
        return success(PurchaseBillConvert.INSTANCE.convert2VO(pageResult));
    }
}