package com.xyy.saas.cloudserver.purchase.server.service.bpm.handler;

import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.api.bpm.handler.BpmApprovalHandler;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 总部配送单审核
 */
@Slf4j
@Component
public class HeadDistributionApprovalHandler implements BpmApprovalHandler {

    @Override
    public void handleApproval(BpmBusinessRelationDto businessDto) {

        // 按照审核结果，修改单据状态为：已审核待发货/已驳回

    }

    @Override
    public BpmBusinessTypeEnum[] supportBusinessTypes() {

        //监听总部配送单

        return new BpmBusinessTypeEnum[0];
    }
}