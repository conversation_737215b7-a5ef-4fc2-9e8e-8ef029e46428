package com.xyy.saas.cloudserver.purchase.server.service.purchase.impl;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.cloudserver.purchase.server.service.purchase.HeadDistributionService;
import com.xyy.saas.localserver.inventory.api.campon.InventoryCampOnApi;
import com.xyy.saas.localserver.inventory.api.stock.InventoryStockApi;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySwapDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.server.convert.inventory.InventoryCampOnConvert;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseBillService;
import com.xyy.saas.localserver.purchase.server.service.receive.handler.stockin.StockIncreaseHandlerManager;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * 总部配送 Service 实现类
 * 处理总部配送单的创建、更新、删除和查询等业务操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class HeadDistributionServiceImpl implements HeadDistributionService {

    @Resource
    private PurchaseBillService purchaseBillService;

    @Resource
    private InventoryCampOnApi campOnApi;

    @Resource
    private InventoryStockApi stockApi;

    @Resource
    private StockIncreaseHandlerManager stockIncreaseHandlerManager;

    /**
     * 保存总部配送单并预占库存
     * 处理流程：
     * 1. 填充商品信息
     * 2. 填充配送内容
     * 3. 保存单据信息
     * 4. 如果提交，则预占库存
     *
     * @param saveDTO 总部配送单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDistributionBillAndCampOn(PurchaseBillDTO saveDTO) {
        // 1. 填充商品信息
        PurchaseBillDetailConvert.INSTANCE.fillProductInfo(saveDTO.getDetails());

        // 2. 填充配送内容
        PurchaseBillConvert.INSTANCE.fillPurchaseContent(saveDTO);

        // 3. 保存单据信息
        purchaseBillService.savePurchaseBills(List.of(saveDTO));

        // 4. 如果提交，处理后续逻辑
        if (saveDTO.getSubmitted()) {
            // 4.1. 预占库存
            campOnApi.campOn(
                    InventoryCampOnConvert.INSTANCE.convert2InventoryCampOn(saveDTO, saveDTO.getTenantId()));

            // TODO: 创建审批流
        }
    }

    /**
     * 保存总部配送单并转移库存
     * 处理流程：
     * 1. 填充商品信息
     * 2. 填充配送内容
     * 3. 保存单据信息
     * 4. 如果提交，则执行库存一入一出
     *
     * @param distributionBill 总部配送单信息
     * @param receiveBill      收货单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDistributionBillAndTransferStock(PurchaseBillDTO distributionBill, ReceiveBillDTO receiveBill) {
        // 1. 填充商品信息
        PurchaseBillDetailConvert.INSTANCE.fillProductInfo(distributionBill.getDetails());

        // 2. 填充配送内容
        PurchaseBillConvert.INSTANCE.fillPurchaseContent(distributionBill);

        // 3. 保存单据信息
        purchaseBillService.savePurchaseBills(List.of(distributionBill));

        // 4. 如果提交，处理后续逻辑
        if (distributionBill.getSubmitted()) {
            // 调用库存一入一出
            InventorySwapDTO swapDTO = stockIncreaseHandlerManager
                    .processInventorySwapParams(distributionBill, receiveBill);
            stockApi.swapStock(swapDTO);
        }
    }

    /**
     * 更新总部配送单并刷新业务状态
     * 处理流程：
     * 1. 重新计算状态和数量
     * 2. 执行带版本校验的更新
     *
     * @param updateDTO 包含最新业务数据的总部配送单对象
     * @throws ServiceException 当出现以下业务异常时抛出：
     *                          - 总部配送单版本过期 (错误码: PURCHASE_BILL_STALE_VERSION)
     *                          - 明细数据校验失败 (错误码: INVALID_BILL_DETAILS)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDistributionBillWithStatusRefresh(PurchaseBillDTO updateDTO) {
        // 1. 执行状态和数量的重新计算
        PurchaseBillDTO distributionBill = purchaseBillService.recomputePurchaseBillStatusWithQuantities(updateDTO);

        // 2. 执行带版本校验的更新
        purchaseBillService.updatePurchaseBill(distributionBill);
    }

    /**
     * 删除总部配送单
     *
     * @param id 总部配送单编号
     */
    @Override
    public void deleteDistributionBill(Long id) {
        purchaseBillService.deletePurchaseBill(id);
    }

    /**
     * 获得总部配送单
     *
     * @param id 总部配送单编号
     * @return 总部配送单信息
     */
    @Override
    public PurchaseBillDTO getDistributionBill(Long id) {
        return purchaseBillService.getPurchaseBill(id);
    }

    /**
     * 获得总部配送单及详情
     *
     * @param billNo   单号
     * @param tenantId 租户编号
     * @return 总部配送单信息
     */
    @Override
    public PurchaseBillDTO getDistributionBillWithDetails(String billNo, Long tenantId) {
        return purchaseBillService.getPurchaseBillWithDetails(billNo, tenantId);
    }

    /**
     * 获得总部配送单分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 总部配送单分页结果
     */
    @Override
    public PageResult<PurchaseBillDTO> getDistributionBillPage(PurchaseBillPageReqDTO pageReqDTO) {
        return purchaseBillService.getPurchaseBillPage(pageReqDTO);
    }
}