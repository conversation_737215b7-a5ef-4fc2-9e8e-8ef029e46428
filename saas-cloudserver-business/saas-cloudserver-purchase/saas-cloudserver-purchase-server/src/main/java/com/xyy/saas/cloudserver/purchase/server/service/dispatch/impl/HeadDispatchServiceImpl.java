package com.xyy.saas.cloudserver.purchase.server.service.dispatch.impl;

import com.xyy.saas.cloudserver.purchase.server.service.dispatch.HeadDispatchService;
import com.xyy.saas.cloudserver.purchase.server.service.purchase.HeadDistributionService;
import com.xyy.saas.localserver.entity.mqtt.MqttServer;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.receive.ReceiveBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.receive.ReceiveBillDetailConvert;
import com.xyy.saas.localserver.purchase.server.service.purchase.StoreAllocationService;
import com.xyy.saas.localserver.purchase.server.service.purchase.StoreRequisitionService;
import com.xyy.saas.localserver.purchase.server.service.receive.ReceiveBillService;
import com.xyy.saas.localserver.purchase.server.service.returned.ReturnBillService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 总部发货 Service 实现类
 * 处理总部对门店的调度业务，包括配送调度、要货调度和入库调度
 *
 * <AUTHOR>
 * @since 2020-07-06
 */
@Service
public class HeadDispatchServiceImpl implements HeadDispatchService {

    @Resource
    private ReceiveBillService receiveBillService;

    @Resource
    private HeadDistributionService headDistributionService;

    @Resource
    private ReturnBillService returnBillService;

    @Resource
    private StoreAllocationService storeAllocationService;

    @Resource
    private StoreRequisitionService storeRequisitionService;

    @Resource
    private MqttServer mqttServer;

    private final static String MESSAGE_TYPE_HEAD_DISPATCH = "HEAD_DISPATCH";

    /**
     * 执行调度操作
     * 处理流程：
     * 1. 释放库存预占
     * 2. 记录消息表
     * 3. 创建门店收货单
     * 4. 发送MQTT消息到门店
     *
     * @param receiveBill 收货单信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void dispatch(ReceiveBillDTO receiveBill) {
        // 1. 释放库存预占
        // TODO: 实现库存释放预占逻辑

        // 2. 记录消息表
        // TODO: 实现消息记录逻辑

        // 3. 创建门店收货单
        ReceiveBillDTO storeReceiveBill = ReceiveBillConvert.INSTANCE.generateStoreReceiveBillByAllocation(receiveBill);

        // 4. 获取设备ID
        Integer deviceId = null;
        if (ReceiveBillTypeEnum.REQUISITION_RECEIVE.getCode().equals(receiveBill.getBillType())) {
            PurchaseBillDTO requisitionBill = storeRequisitionService.getRequisitionBillWithDetails(
                    receiveBill.getSourceBillNo(),
                    receiveBill.getTenantId());
            deviceId = Integer.parseInt(requisitionBill.getId().toString().substring(7, 10));
        }

        // 5. 发送MQTT消息到门店
        if (deviceId != null) {
            // 发送到指定设备
            mqttServer.sendToDevice(
                    receiveBill.getTenantId(),
                    deviceId,
                    storeReceiveBill,
                    MESSAGE_TYPE_HEAD_DISPATCH);
        } else {
            // 发送到租户的所有设备(最多发送一次，只有一个设备消费)
            mqttServer.sendToTenant(
                    receiveBill.getTenantId(),
                    storeReceiveBill,
                    MESSAGE_TYPE_HEAD_DISPATCH);
        }
    }

    /**
     * 执行入库并调度操作
     * 处理流程：
     * 1. 查询源单据信息
     * 2. 填充商品信息
     * 3. 填充收货内容
     * 4. 创建门店收货信息
     * 5. 填充租户默认信息
     * 6. 保存收货单
     * 7. 创建总部配送单
     * 8. 保存总部配送单
     * 9. 发送MQTT消息到门店
     *
     * @param receiveBill 收货单信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void warehousingAndDispatch(ReceiveBillDTO receiveBill) {
        // 1. 查询源单据
        ReturnBillDTO storeReturnBill = returnBillService.getReturnBillByBillNoAndTenantId(
                receiveBill.getSourceBillNo(),
                receiveBill.getOutboundTenantId());
        PurchaseBillDTO storeAllocationBill = storeAllocationService.getAllocationBillWithDetails(
                storeReturnBill.getPurchaseBillNo(),
                storeReturnBill.getInboundTenantId());

        // 2. 填充商品信息
        ReceiveBillDetailConvert.INSTANCE.fillProductInfo(receiveBill.getDetails());

        // 3. 填充收货内容
        ReceiveBillConvert.INSTANCE.fillReceiveContent(receiveBill);

        // 4. 创建门店收货信息
        ReceiveBillDTO storeReceiveBill = ReceiveBillConvert.INSTANCE.generateStoreReceiveBillByAllocation(receiveBill);

        // 5. 填充租户默认收货、验收、入库员
        // TODO: 实现填充租户默认收货、验收、入库员的逻辑

        // 6. 填充默认合格货位
        // TODO: 实现填充默认合格货位的逻辑

        // 7. 保存收货单
        receiveBillService.saveReceiveBill(receiveBill);

        // 8. 创建总部配送单
        receiveBill.setAllocationBill(storeAllocationBill);
        PurchaseBillDTO headDistribution = ReceiveBillConvert.INSTANCE
                .generateHeadDistributionByAllocation(receiveBill);

        // 9. 填充总部配送内容
        PurchaseBillConvert.INSTANCE.fillPurchaseContent(headDistribution);

        // 10. 保存总部配送单
        headDistributionService.saveDistributionBillAndTransferStock(headDistribution, receiveBill);

        // 11. 发送MQTT消息到门店
        int deviceId = Integer.parseInt(storeAllocationBill.getId().toString().substring(7, 10));
        mqttServer.sendToDevice(
                receiveBill.getTenantId(),
                deviceId,
                storeReceiveBill,
                MESSAGE_TYPE_HEAD_DISPATCH);
    }
}