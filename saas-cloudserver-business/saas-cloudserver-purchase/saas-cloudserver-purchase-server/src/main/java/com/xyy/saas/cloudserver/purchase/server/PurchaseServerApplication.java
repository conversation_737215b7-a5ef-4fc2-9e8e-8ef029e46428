package com.xyy.saas.cloudserver.purchase.server;

import com.xyy.saas.localserver.entity.mqtt.MqttListener;
import com.xyy.saas.localserver.entity.mqtt.MqttSubscriber;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@EnableDubbo(scanBasePackages = { "com.xyy.saas.cloudserver","cn.iocoder.yudao.module.*" })
@SpringBootApplication
@EnableDiscoveryClient
@Slf4j
// @Import({YudaoWebAutoConfiguration.class})
@ComponentScan(basePackages = { "com.xyy.saas.cloudserver", "com.xyy.saas.localserver",
        "cn.iocoder.yudao.module.*" }, excludeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
                        com.xyy.saas.localserver.purchase.server.PurchaseServerApplication.class,
                        MqttSubscriber.class,
                        MqttListener.class
                })
        })
public class PurchaseServerApplication {

    public static void main(String[] args) {
        // 保证dubbo注册地址不受网卡顺序影响（docker虚拟网卡）
        System.setProperty("dubbo.network.interface.preferred", "eth0");
        // 设置skywalking服务名称
        System.setProperty("skywalking.agent.service_name", "saas-cloud-purchase-server");

        SpringApplication.run(PurchaseServerApplication.class, args);
    }
}
