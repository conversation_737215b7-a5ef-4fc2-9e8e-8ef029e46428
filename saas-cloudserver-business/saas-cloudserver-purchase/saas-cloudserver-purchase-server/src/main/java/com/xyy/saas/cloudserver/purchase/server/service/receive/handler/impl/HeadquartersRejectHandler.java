package com.xyy.saas.cloudserver.purchase.server.service.receive.handler.impl;

import cn.hutool.core.lang.Assert;
import com.xyy.saas.localserver.entity.mqtt.MqttServer;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDetailDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.receive.ReceiveBillConvert;
import com.xyy.saas.localserver.purchase.server.convert.returned.ReturnBillConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDO;
import com.xyy.saas.localserver.purchase.server.dal.mysql.returned.ReturnBillMapper;
import com.xyy.saas.localserver.purchase.server.service.receive.handler.RejectHandler;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import java.util.List;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;
import static com.xyy.saas.localserver.purchase.enums.receive.ReceiveBillTypeEnum.*;

/**
 * 总部拒收处理器
 * 处理门店的退货单拒收业务，生成门店收货单
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class HeadquartersRejectHandler implements RejectHandler {

    @Resource
    private ReturnBillMapper returnBillMapper;

    @Resource
    private MqttServer mqttServer;

    private final static String MESSAGE_TYPE_HEAD_REJECT_RECEIVE = "HEAD_REJECT_RECEIVE";

    /**
     * 获取支持的收货单类型
     * 仅支持退货收货单类型
     *
     * @return 支持的收货单类型列表
     */
    @Override
    public List<ReceiveBillTypeEnum> supportReceiveBillTypes() {
        return List.of(RETURN_RECEIVE);
    }

    /**
     * 处理总部租户拒收业务
     * 处理流程：
     * 1. 查找门店退货单
     * 2. 创建门店收货单
     * 3. 发送MQTT消息到门店
     *
     * @param receiveBill 收货单信息
     * @param details     拒收的详情列表
     * @throws IllegalStateException 当门店退货单不存在时抛出
     */
    @Override
    public void handle(ReceiveBillDTO receiveBill, List<ReceiveBillDetailDTO> details) {
        // 1. 查找门店退货单
        ReturnBillDO returnBill = returnBillMapper
                .selectReturnBillByBillNoAndHeadTenantId(receiveBill.getSourceBillNo(), receiveBill.getTenantId());
        Assert.notNull(returnBill, STORE_RETURN_BILL_NOT_EXISTS.getMsg());

        ReturnBillDTO returnBillDTO = ReturnBillConvert.INSTANCE.convert2DTO(returnBill);

        // 2. 创建门店收货单
        ReceiveBillSaveReqVO storeReceiveBill = ReceiveBillConvert.INSTANCE.generateHeadRejectWarehouseBill(
                receiveBill,
                returnBillDTO,
                details);

        // 3. 发送MQTT消息到门店
        int deviceId = Integer.parseInt(returnBill.getId().toString().substring(7, 10));
        mqttServer.sendToDevice(
                receiveBill.getTenantId(),
                deviceId,
                storeReceiveBill,
                MESSAGE_TYPE_HEAD_REJECT_RECEIVE);
    }
}