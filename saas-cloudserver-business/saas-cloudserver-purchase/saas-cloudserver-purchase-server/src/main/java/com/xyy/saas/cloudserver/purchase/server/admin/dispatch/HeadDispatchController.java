package com.xyy.saas.cloudserver.purchase.server.admin.dispatch;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.cloudserver.purchase.server.service.dispatch.HeadDispatchService;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.receive.ReceiveBillConvert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 总部发货 Controller
 * 处理总部发货相关的业务操作，包括要货单出库、主配单出库、收货并配送出库等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - 总部发货")
@RestController
@RequestMapping("/saas/purchase/head-dispatch")
@Validated
public class HeadDispatchController {

    @Resource
    private HeadDispatchService headDispatchService;

    /**
     * 要货单出库（总部发货）
     * 处理流程：
     * 1. 校验验收员信息
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：执行发货操作
     *
     * @param createReqVO 创建信息，包含验收员和发货相关信息
     * @return 发货结果
     */
    @PostMapping("/requisitionDispatch")
    @Operation(summary = "要货单出库（总部发货）")
    @PreAuthorize("@ss.hasPermission('saas:purchase:head-dispatch:requisition')")
    public CommonResult<Boolean> requisitionDispatch(@Valid @RequestBody ReceiveBillSaveReqVO createReqVO) {
        // 1. 校验验收员信息
        createReqVO.validateChecker();

        // 2. 对象转换：将VO对象转换为DTO对象
        ReceiveBillDTO receiveBill = ReceiveBillConvert.INSTANCE.convert2RequisitionDispatchDTO(createReqVO);

        // 3. 调用服务：执行发货操作
        headDispatchService.dispatch(receiveBill);

        return success(true);
    }

    /**
     * 主配单出库（总部发货）
     * 处理流程：
     * 1. 校验验收员信息
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：执行发货操作
     *
     * @param createReqVO 创建信息，包含验收员和发货相关信息
     * @return 发货结果
     */
    @PostMapping("/distributionDispatch")
    @Operation(summary = "主配单出库（总部发货）")
    @PreAuthorize("@ss.hasPermission('saas:purchase:head-dispatch:distribution')")
    public CommonResult<Boolean> distributionDispatch(@Valid @RequestBody ReceiveBillSaveReqVO createReqVO) {
        // 1. 校验验收员信息
        createReqVO.validateChecker();

        // 2. 对象转换：将VO对象转换为DTO对象
        ReceiveBillDTO receiveBill = ReceiveBillConvert.INSTANCE.convert2DistributionDispatchDTO(createReqVO);

        // 3. 调用服务：执行发货操作
        headDispatchService.dispatch(receiveBill);

        return success(true);
    }

    /**
     * 收货并配送出库（总部发货）
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行入库并发货操作
     *
     * @param createReqVO 创建信息，包含收货和发货相关信息
     * @return 操作结果
     */
    @PostMapping("/warehousingAndDispatch")
    @Operation(summary = "收货并配送出库（总部发货）")
    @PreAuthorize("@ss.hasPermission('saas:purchase:head-dispatch:warehousing')")
    public CommonResult<Boolean> receiveAndDispatch(@Valid @RequestBody ReceiveBillSaveReqVO createReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        ReceiveBillDTO receiveBill = ReceiveBillConvert.INSTANCE.convert2StoreAllocationHeadWarehousingDTO(createReqVO);

        // 2. 调用服务：执行入库并发货操作
        headDispatchService.warehousingAndDispatch(receiveBill);

        return success(true);
    }
}