package com.xyy.saas.cloudserver.purchase.server.service.dispatch;

import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;

/**
 * 收货单 Service 接口
 *
 * <AUTHOR>
 */
public interface HeadDispatchService {

    /**
     * 发货
     *
     * @param receiveBill 单据信息
     */
    void dispatch(ReceiveBillDTO receiveBill);

    /**
     * 入库并配送
     *
     * @param receiveBill 单据信息
     */
    void warehousingAndDispatch(ReceiveBillDTO receiveBill);

}