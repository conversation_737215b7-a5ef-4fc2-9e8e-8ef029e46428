<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-cloudserver-purchase</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>saas-cloudserver-purchase-server</artifactId>

  <dependencies>
    <!-- 项目内部依赖 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-cloudserver-purchase-api</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-api</artifactId>
      <version>${inquiry.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>yudao-module-system-api</artifactId>
          <groupId>cn.iocoder.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-product-api</artifactId>
      <version>${revision}</version>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>cn.iocoder.boot</groupId>-->
<!--      <artifactId>yudao-module-system-biz</artifactId>-->
<!--      <version>${yudao.version}</version>-->
<!--    </dependency>-->

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-localserver-purchase-server</artifactId>
      <version>${revision}</version>
      <exclusions>
        <exclusion>
          <artifactId>data-pipeline-sqlite</artifactId>
          <groupId>com.xyy.saas</groupId>
        </exclusion>
        <exclusion>
          <artifactId>yudao-module-system-api</artifactId>
          <groupId>cn.iocoder.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>biz-soa-starter</artifactId>
    </dependency>

  </dependencies>

  <!-- <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${maven-springboot-plugin.version}</version>
      </plugin>
    </plugins>
  </build> -->
</project>
