<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xyy.saas</groupId>
        <artifactId>saas-cloudserver-business</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>saas-cloudserver-purchase</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <description>saas-药店-采购服务</description>

    <modules>
        <module>saas-cloudserver-purchase-api</module>
        <module>saas-cloudserver-purchase-server</module>
    </modules>

    <properties>
        <java.version>21</java.version>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${maven-springboot-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

</project>
