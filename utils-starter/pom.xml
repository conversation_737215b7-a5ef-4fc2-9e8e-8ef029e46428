<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>local-server-cloud</artifactId>
        <groupId>com.xyy.saas</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>utils-starter</artifactId>
	<version>${revision}</version>
    <name>utils-starter</name>


    <dependencies>
		<dependency>
			<artifactId>lombok</artifactId>
			<groupId>org.projectlombok</groupId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<artifactId>slf4j-api</artifactId>
			<groupId>org.slf4j</groupId>
			<type>jar</type>
		</dependency>
		<dependency>
			<artifactId>guava</artifactId>
			<groupId>com.google.guava</groupId>
		</dependency>

		<dependency>
			<artifactId>jackson-databind</artifactId>
			<groupId>com.fasterxml.jackson.core</groupId>
			<version>2.17.0</version>
			<!--            <version>${jackson.version}</version>-->
		</dependency>
		<!--        <dependency>-->
		<!--            <groupId>org.springframework</groupId>-->
		<!--            <artifactId>spring-context</artifactId>-->
		<!--        </dependency>-->

		<dependency>
			<artifactId>spring-boot-starter</artifactId>
			<groupId>org.springframework.boot</groupId>
		</dependency>
		<dependency>
			<artifactId>spring-boot-autoconfigure</artifactId>
			<groupId>org.springframework.boot</groupId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<groupId>org.springframework.boot</groupId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<artifactId>dubbo-spring-boot-starter</artifactId>
			<groupId>org.apache.dubbo</groupId>
			<version>${dubbo.version}</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<artifactId>jakarta.annotation-api</artifactId>
			<groupId>jakarta.annotation</groupId>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<artifactId>hutool-all</artifactId>
			<groupId>cn.hutool</groupId>
		</dependency>
	</dependencies>


</project>
