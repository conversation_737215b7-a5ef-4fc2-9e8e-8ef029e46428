package com.xyy.saas.localserver.utils;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import jakarta.annotation.PostConstruct;


/**
 * @Desc spring bean工具
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 11:30
 */
public class SpringUtils implements ApplicationContextAware {

    private static SpringUtils instance;

    private ApplicationContext applicationContext;

    public SpringUtils() {
        super();
    }

    @PostConstruct
    public final void init() {
        instance = this;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 获取applicationContext
     *
     * @return
     */
    public ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 通过name获取 Bean.
     *
     * @param name
     * @return
     */
    public static Object getBean(String name) {
        return instance.getApplicationContext().getBean(name);
    }

    /**
     * 通过class获取Bean.
     *
     * @param clazz
     * @return
     */
    public static <T> T getBean(Class<T> clazz) {
        return instance.getApplicationContext().getBean(clazz);
    }

    /**
     * 通过name,以及Clazz返回指定的Bean
     *
     * @param name
     * @param clazz
     * @return
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        return instance.getApplicationContext().getBean(name, clazz);
    }
}
