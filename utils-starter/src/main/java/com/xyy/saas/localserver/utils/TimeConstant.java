package com.xyy.saas.localserver.utils;

/**
 * @Desc 时间常量
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/1/20 17:23
 */
public interface TimeConstant {

    /**
     * 一秒钟的毫秒数
     */
    int ONE_SECOND_MILLISECOND = 1000;

    /**
     * 半分钟的毫秒数
     */
    int HALF_MINUTE_MILLISECOND = 30 * ONE_SECOND_MILLISECOND;

    /**
     * 一分钟的毫秒数
     */
    int ONE_MINUTE_MILLISECOND = 60 * ONE_SECOND_MILLISECOND;

    /**
     * 半小时的毫秒数
     */
    int HALF_HOUR_MILLISECOND = 30 * ONE_MINUTE_MILLISECOND;

    /**
     * 一小时的毫秒数
     */
    int ONE_HOUR_MILLISECOND = 60 * ONE_MINUTE_MILLISECOND;

    /**
     * 半天的毫秒数
     */
    int HALF_DAY_MILLISECOND = 12 * ONE_HOUR_MILLISECOND;

    /**
     * 一天的毫秒数
     */
    int ONE_DAY_MILLISECOND = 24 * ONE_HOUR_MILLISECOND;

    /**
     * 一分钟的秒数
     */
    int ONE_MINUTE_SECOND = (int) (ONE_MINUTE_MILLISECOND / ONE_SECOND_MILLISECOND);

    /**
     * 1小时的秒数
     */
    int ONE_HOUR_SECOND = (int) (ONE_HOUR_MILLISECOND / ONE_SECOND_MILLISECOND);

    /**
     * 半小时的秒数
     */
    int HALF_HOUR_SECOND = (int) (HALF_HOUR_MILLISECOND / ONE_SECOND_MILLISECOND);

    /**
     * 1天的秒数
     */
    int ONE_DAY_SECOND = (int) (ONE_DAY_MILLISECOND / ONE_SECOND_MILLISECOND);

    /**
     * 半天的秒数
     */
    int HALF_DAY_SECOND = (int) (HALF_DAY_MILLISECOND / ONE_SECOND_MILLISECOND);

    /** 10分钟的毫秒数*/
    long TEN_MINUTE_MILLISECOND = 10 * ONE_MINUTE_MILLISECOND;

    /**
     *
     */
    float ONE_SECOND_MILLISECOND_MULTI = 0.001f;


}
