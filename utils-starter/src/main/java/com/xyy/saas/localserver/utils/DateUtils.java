package com.xyy.saas.localserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.LinkedTransferQueue;


import static java.util.Calendar.*;

/**
 * @Desc 时间工具类
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/1/20 17:18
 */
@Slf4j
public class DateUtils {

    /**
     * 日期时间格式:年-月-日 时:分:秒[2011-5-5 20:00:00]
     */
    public static final String PATTERN_DATE_TIME = "yyyy-MM-dd HH:mm:ss";
    /**
     * 日期格式:年-月-日[2011-05-05]
     */
    public static final String PATTERN_DATE = "yyyy-MM-dd";

    /**
     * 月份格式:年-月[2011-05]
     */
    public static final String PATTERN_MONTH = "yyyy-MM";
    /**
     * 时间格式:时:分:秒[20:00:00]
     */
    public static final String PATTERN_TIME = "HH:mm:ss";
    /**
     * 短时间格式:时:分[20:00]
     */
    public static final String PATTERN_SHORT_TIME = "HH:mm";

    public static final String PATTERN_DATE_SHORT_TIME = "yyyy-MM-dd HH:mm";

    public static final String PATTERN_DATES = "yyyyMMdd";

    public static final String PATTERN_SHORT_DATE_TIME = "yyMMddHHmmss";

    public static final long MILLIS_PER_SECOND = 1000;

    public static final long MILLIS_PER_MINUTE = 60 * MILLIS_PER_SECOND;

    public static final long MILLIS_PER_HOUR = 60 * MILLIS_PER_MINUTE;

    public static final long MILLIS_PER_DAY = 24 * MILLIS_PER_HOUR;

    // public static final SimpleDateFormat DATE_FORMATTER = new
    // SimpleDateFormat(PATTERN_DATE);
    //
    // public static final SimpleDateFormat DATE_TIME_FORMATTER = new
    // SimpleDateFormat(PATTERN_DATE_TIME);
    //
    // // private static final SimpleDateFormat SHORT_TIME_FORMATTER = new
    // // SimpleDateFormat(
    // // PATTERN_SHORT_TIME);
    //
    // public static final SimpleDateFormat DATE_SHORT_TIME_FORMATTER = new
    // SimpleDateFormat(PATTERN_DATE_SHORT_TIME);

    /**
     * 缓存dateformat的map
     */
    private static final ConcurrentMap<String, BlockingQueue<SimpleDateFormat>> formatMap = new ConcurrentHashMap<String, BlockingQueue<SimpleDateFormat>>();

    /**
     * 一个很久很久以前的时间(格林威治的起始时间. 1970-01-01 00:00:00)
     */
    public static final Date LONG_BEFORE_TIME = string2Date("1970-01-01 00:00:00", PATTERN_DATE_TIME);
    /**
     * 一个很久很久以后的时间(该框架可能被遗弃的时间. 2048-01-01 00:00:00)
     */
    public static final Date LONG_AFTER_TIME = string2Date("2048-01-01 00:00:00", PATTERN_DATE_TIME);

    public static byte DAY_START_HOUR = 0;

    public static boolean isNowExpire(Date expireDate) {
        return new Date().after(expireDate);
    }

    public static boolean isNowExpire(Integer timestamp) {
        if (timestamp == null) {
            return true;
        }
        return getCurrentSecond() > timestamp;
    }

//    public static void main(String[] args) {
//    	Date addDays = addDays(new Date(), 180);
//    	System.err.println(addDays);

    // String nowString = DateUtils.nowString("yyyy-MM-dd HH:mm:ss");
    //
    // Date date = string2Date("2017-10-15 09:18:35", "yyyy-MM-dd
    // HH:mm:ss");
    //
    // Date date0 = add(date, 10, 0, 0);
    // System.err.println(isNowExpire(date0));

//        String formatDefault = formatDefault(1667059200);
//        System.err.println(formatDefault);
//        Date nextDayFirstTime = getNextDayFirstTime(new Date());
//        System.out.println(format(nextDayFirstTime, PATTERN_DATE_TIME));
//        Date date = string2Date("2019-03-17 23:59:59", PATTERN_DATE_TIME);
//        Date date1 = new Date();
//        Date date0AM = getDate0AM(date1);
//        String present0AM = getDateString(date0AM, PATTERN_DATE_TIME);
//        System.out.println("当天的0点是：：" + present0AM);
//        Date nextMondayDate = getNextMonday0AMDate(date1);
//        Date nextDay0AM = getNextDay0AM(nextMondayDate);
//        String dateString = getDateString(nextMondayDate, PATTERN_DATE_TIME);
//        System.out.println("下周一的零点：：" + dateString);
//        String tuesdayString = getDateString(nextDay0AM, PATTERN_DATE_TIME);
//        System.out.println("下周二的零点::" + tuesdayString);
//        int i = date.compareTo(nextMondayDate);
//        System.out.println(i);
//    }

    /**
     * 检查当前时间和指定时间是否同一周
     *
     * @param year           年
     * @param week           周
     * @param firstDayOfWeek 周的第一天设置值，{@link Calendar#DAY_OF_WEEK}
     * @return
     */
    public static boolean isSameWeek(int year, int week, int firstDayOfWeek) {
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(firstDayOfWeek);
        return year == cal.get(YEAR) && week == cal.get(WEEK_OF_YEAR);
    }

    /**
     * 检查当前时间和指定时间是否同一周
     *
     * @param time           被检查的时间
     * @param firstDayOfWeek 周的第一天设置值，{@link Calendar#DAY_OF_WEEK}
     * @return {@link Boolean} 是否同一周. true-是, false-不是
     */
    public static boolean isSameWeek(Date time, int firstDayOfWeek) {
        if (time == null) {
            return false;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.setFirstDayOfWeek(firstDayOfWeek);
        return isSameWeek(cal.get(YEAR), cal.get(WEEK_OF_YEAR), firstDayOfWeek);
    }

    /**
     * 获取周的第一天
     *
     * @param firstDayOfWeek 周的第一天设置值，{@link Calendar#DAY_OF_WEEK}
     * @param time           指定时间，为 null 代表当前时间
     * @return {@link Date} 周的第一天
     */
    public static Date firstTimeOfWeek(int firstDayOfWeek, Date time) {
        Calendar cal = Calendar.getInstance();
        if (time != null) {
            cal.setTime(time);
        }

        cal.setFirstDayOfWeek(firstDayOfWeek);
        int day = cal.get(DAY_OF_WEEK);
        if (day == firstDayOfWeek) {
            day = 0;
        } else if (day < firstDayOfWeek) {
            day = day + (7 - firstDayOfWeek);
        } else if (day > firstDayOfWeek) {
            day = day - firstDayOfWeek;
        }

        cal.set(HOUR_OF_DAY, 0);
        cal.set(MINUTE, 0);
        cal.set(SECOND, 0);
        cal.set(MILLISECOND, 0);

        cal.add(DATE, -day);
        return cal.getTime();
    }

    public static boolean isInAday(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            throw new IllegalArgumentException("the date must not be null");
        }

        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);

        if ((cal1.get(Calendar.ERA) == cal2.get(Calendar.ERA) && cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR))) {
            int dayDist = cal1.get(Calendar.DAY_OF_YEAR) - cal2.get(Calendar.DAY_OF_YEAR);
            if (dayDist == 0) {
                // 同一天的 5:00 ~ 24:00
                return cal1.get(Calendar.HOUR_OF_DAY) >= DAY_START_HOUR
                        && cal2.get(Calendar.HOUR_OF_DAY) >= DAY_START_HOUR;
            } else if (dayDist == 1) {
                // 前一天的 5:00 ~ 后一天的 4:59
                return cal1.get(Calendar.HOUR_OF_DAY) >= DAY_START_HOUR
                        && cal2.get(Calendar.HOUR_OF_DAY) < DAY_START_HOUR;
            }
        }
        return false;
    }

    /**
     * 检查指定的日期与系统当前时间是否在指定的一个24小时内,用于系统时间重置功能
     *
     * @param date 被检查的日期
     * @return
     */
    public static boolean isInAday(Date date) {
        return isInAday(new Date(), date);
    }

    public static boolean isInAday(long timestamp) {
        return isInAday(new Date(), new Date(timestamp));
    }

    /**
     * 日期转换成字符串格式
     *
     * @param date    待转换的日期
     * @param pattern 日期格式
     * @return {@link String} 日期字符串
     */
    public static String date2String(Date date, String pattern) {
        SimpleDateFormat dateFormat = getDateFormat(pattern);
        try {
            return dateFormat.format(date);
        } finally {
            returnDateFormat(dateFormat);
        }
    }

    /**
     * @param pattern
     * @return
     */
    public static SimpleDateFormat getDateFormat(String pattern) {
        BlockingQueue<SimpleDateFormat> list = formatMap.get(pattern);
        if (list == null) {
            list = new LinkedTransferQueue<SimpleDateFormat>();
            BlockingQueue<SimpleDateFormat> exist = formatMap.putIfAbsent(pattern, list);
            list = exist == null ? list : exist;
        }
        SimpleDateFormat simpleDateFormat = list.poll();
        if (simpleDateFormat == null) {
            simpleDateFormat = new SimpleDateFormat(pattern);
        }
        return simpleDateFormat;
    }

    /**
     * 返回StringBuilder
     *
     * @param simpleDateFormat
     */
    public static void returnDateFormat(SimpleDateFormat simpleDateFormat) {
        String pattern = simpleDateFormat.toPattern();
        BlockingQueue<SimpleDateFormat> list = formatMap.get(pattern);
        if (list == null || list.isEmpty()) {
            return;
        }
        int POOL_SIZE = Runtime.getRuntime().availableProcessors();
        if (list.size() >= POOL_SIZE) {
            return;
        }
        list.add(simpleDateFormat);
    }

    /**
     * 字符串转换成日期格式
     *
     * @param string  待转换的日期字符串
     * @param pattern 日期格式
     * @return {@link Date} 转换后的日期
     */
    public static Date string2Date(String string, String pattern) {
        SimpleDateFormat dateFormat = getDateFormat(pattern);
        try {
            return dateFormat.parse(string);
        } catch (ParseException e) {
            returnDateFormat(dateFormat);
            throw new IllegalArgumentException("无法将字符串[" + string + "]按格式[" + pattern + "]转换为日期", e);
        }
    }

    /**
     * 获取字符串时间的0点
     *
     * @param string  待转换的日期字符串
     * @param pattern 日期格式
     * @return {@link Date} 转换后的日期
     */
    public static Date string2Date0Am(String string, String pattern) {
        Date date = string2Date(string, pattern);
        return getDate0AM(date);
    }

    /**
     * 获取字符串时间的下月1号的0点
     *
     * @param string  待转换的日期字符串
     * @param pattern 日期格式
     * @return {@link Date} 转换后的日期
     */
    public static Date string2NextMonthFirstDate0Am(String string, String pattern) {
        Date date = string2Date(string, pattern);
        return date2NextMonthFirstDate0Am(date);
    }

    /**
     * 获取时间的下月1号的0点
     *
     * @param date  待转换的日期字符串
     * @return {@link Date} 转换后的日期
     */
    public static Date date2NextMonthFirstDate0Am(Date date) {
        Calendar instance = getInstance();
        instance.setTime(date);
        instance.set(DAY_OF_MONTH, 1);
        instance.add(MONTH, 1);
        return getDate0AM(instance.getTime());
    }

    /**
     * 获取时间的下月1号的0点
     *
     * @param date  待转换的日期字符串
     * @return {@link Date} 转换后的日期
     */
    public static Date date2NextYearFirstDate0Am(Date date) {
        Calendar instance = getInstance();
        instance.setTime(date);
        instance.set(DAY_OF_YEAR, 1);
        instance.add(YEAR, 1);
        return getDate0AM(instance.getTime());
    }

    /**
     * 获取时间年份的最后一天
     *
     * @return {@link Date} 转换后的日期
     */
    public static Date getLastDateOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int lastDayOfYear = calendar.getActualMaximum(DAY_OF_YEAR);
        calendar.set(DAY_OF_YEAR, lastDayOfYear);
        return calendar.getTime();
    }

    /**
     * 获取时间年份的最后一天
     *
     * @return {@link Date} 转换后的日期
     */
    public static Date getFirstDateOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int firstDayOfYear = calendar.getActualMinimum(DAY_OF_YEAR);
        calendar.set(DAY_OF_YEAR, firstDayOfYear);
        return calendar.getTime();
    }

    /**
     * 获取时间月份的最后一天
     *
     * @return {@link Date} 转换后的日期
     */
    public static Date getLastDateOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int lastDayOfMonth = calendar.getActualMaximum(DAY_OF_MONTH);
        calendar.set(DAY_OF_MONTH, lastDayOfMonth);
        return calendar.getTime();
    }

    public static Date getLastMonthFirstDate0Am(Date date) {
        Calendar instance = getInstance();
        instance.setTime(date);
        instance.set(DAY_OF_MONTH, 1);
        instance.add(MONTH, -1);
        return getDate0AM(instance.getTime());
    }

    public static Date getCurrentMonthFirstDate0Am(Date date) {
        Calendar instance = getInstance();
        instance.setTime(date);
        instance.set(DAY_OF_MONTH, 1);
        return getDate0AM(instance.getTime());
    }

    /**
     * 对一个具体的时间增加时间
     *
     * @param source  需要修改的时间
     * @param hours   需要增加或者减少的小时
     * @param minutes 需要增加或者减少的分
     * @param second  需要增加或者减少的秒
     * @return {@link Date} 返回修改过的时间
     */
    public static Date addTime(Date source, int day, int hours, int minutes, int second) {
        if (source == null) {
            return null;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(source);
        cal.add(DATE, day);
        cal.add(Calendar.HOUR_OF_DAY, hours);
        cal.add(Calendar.MINUTE, minutes);
        cal.add(Calendar.SECOND, second);
        return cal.getTime();
    }

    /**
     * 获取某日的开始时间，即获得某一时间的0点
     *
     * @param date 需要计算的时间
     */
    public static Date getFirstTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获得指定时间的下一个0点
     *
     * @param date 需要计算的时间
     */
    public static Date getNextDayFirstTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(date.getTime() + MILLIS_PER_DAY);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 计算2个时间相差的天数,这个方法算的是2个零点时间的绝对时间(天数)
     *
     * @param startDate 起始时间
     * @param endDate   结束时间
     * @return 相差的天数
     */
    public static int calcIntervalDays(Date startDate, Date endDate) {
        int value = 0;
        if (startDate != null && endDate != null) {
            Date startDate0AM = getFirstTime(startDate);
            Date endDate0AM = getFirstTime(endDate);
            long subValue = startDate0AM.getTime() - endDate0AM.getTime();
            value = Math.abs((int) MathUtils.divideAndRoundUp(subValue, MILLIS_PER_DAY, 0));
        }
        return value;
    }

    /**
     * 计算两个时间的隔多少分钟 当有一个时间为空时返回-1 当结束时间在开始时间之前时返回-1
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static int calcBetweenMinute(Date startDate, Date endDate) {
        if (startDate != null && endDate != null) {
            long startTime = startDate.getTime();
            long endTime = endDate.getTime();
            long millisecond = endTime - startTime;
            if (millisecond < 0) {
                return -1;
            }
            return (int) (toSecond(millisecond) / 60);
        }
        return -1;
    }

    /**
     * 获取指定CRON表达式的下一个时间点
     *
     * @param cron CRON表达式
     * @param now  基准时间点
     * @return 下一个时间点
     */
//    public static Date getNextTime(String cron, Date now) {
//        CronSequenceGenerator gen = new CronSequenceGenerator(cron, TimeZone.getDefault());
//        Date time = gen.next(now);
//        return time;
//    }

    /**
     * 判断时间是否是周末(周六或周日)
     *
     * @param date 日期
     * @return
     */
    public static boolean isWeekend(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        int week = calendar.get(Calendar.DAY_OF_WEEK);
        return week == Calendar.SATURDAY || week == Calendar.SUNDAY;
    }

    /**
     * 计算两个时间之间的差多少分钟
     *
     * @param start
     * @param end
     * @return
     */
    public static int getBetweenDayMinute(Date start, Date end) {
        long dayNumber = 0;
        // 1分钟=60秒=60000毫秒
        long mins = 60L * 1000L;
        SimpleDateFormat DATE_TIME_FORMATTER = getDateFormat(PATTERN_DATE_TIME);
        try {
            Date d1 = DATE_TIME_FORMATTER.parse(DATE_TIME_FORMATTER.format(start));
            Date d2 = DATE_TIME_FORMATTER.parse(DATE_TIME_FORMATTER.format(end));
            dayNumber = Math.abs((d2.getTime() - d1.getTime()) / mins);
        } catch (Exception e) {
            returnDateFormat(DATE_TIME_FORMATTER);
            log.error("", e);
        }
        return (int) dayNumber;
    }

    /**
     * 计算两个时间之间的差多少秒
     *
     * @param start
     * @param end
     * @return
     */
    public static int getBetweenSecond(Date start, Date end) {

        long result = Integer.MAX_VALUE;
        // 1秒=1000毫秒
        long sceds = 1000L;

        try {
            // Date d1 =
            // DATE_TIME_FORMATTER.parse(DATE_TIME_FORMATTER.format(start));
            // Date d2 =
            // DATE_TIME_FORMATTER.parse(DATE_TIME_FORMATTER.format(end));
            result = Math.abs((end.getTime() - start.getTime()) / sceds);
        } catch (Exception e) {
            log.error("", e);
        }
        return (int) result;
    }

    public static Integer getExpireSecond(Integer timestamp) {
        int currentSecond = getCurrentSecond();
        return Math.max(timestamp - currentSecond, 0);
    }

    public static Date fiveMinuteTime(Date time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, (calendar.get(Calendar.MINUTE) / 5) * 5);
        return calendar.getTime();
    }

    /**
     * 判断是否在一个时间段内 <br/>
     * 该时间段仅有时分，类似08:00-12:00
     *
     * @param date
     * @param startTime
     * @param endTime
     * @return
     * @throws ParseException
     */
    public static boolean isBetweenTime(Date date, String startTime, String endTime) throws ParseException {
        // SimpleDateFormat DATE_FORMATTER = buildDateFormat(PATTERN_DATE);
        // String dateStr = DATE_FORMATTER.format(date);
        // SimpleDateFormat DATE_SHORT_TIME_FORMATTER =
        // buildDateFormat(PATTERN_DATE_SHORT_TIME);
        // Date startDateTime = DATE_SHORT_TIME_FORMATTER.parse(dateStr + " " +
        // startTime);
        // Date endDateTime = DATE_SHORT_TIME_FORMATTER.parse(dateStr + " " +
        // endTime);
        // return date.after(startDateTime) && date.before(endDateTime);
        SimpleDateFormat DATE_SHORT_TIME_FORMATTER = getDateFormat(PATTERN_DATE_SHORT_TIME);
        String shortTime = DATE_SHORT_TIME_FORMATTER.format(date);
        returnDateFormat(DATE_SHORT_TIME_FORMATTER);
        return shortTime.compareTo(startTime) >= 0 && shortTime.compareTo(endTime) <= 0;
    }

    /**
     * 获得时间的日期部分
     *
     * @param date
     * @return 2014-12-08
     */
    public static String getDateStr(Date date) {
        SimpleDateFormat DATE_FORMATTER = getDateFormat(PATTERN_DATE);
        try {
            return DATE_FORMATTER.format(date);
        } finally {
            returnDateFormat(DATE_FORMATTER);
        }
    }

    /**
     * 获得时间的日期部分
     *
     * @param date
     * @return 2014-12-08
     */
    public static String getDateStr(Date date, String pattern) {
        SimpleDateFormat DATE_FORMATTER = getDateFormat(pattern);
        try {
            return DATE_FORMATTER.format(date);
        } finally {
            returnDateFormat(DATE_FORMATTER);
        }
    }

    /**
     * @param dateTimeStr
     * @return
     * @throws ParseException
     */
    public static Date parseDateTime(String dateTimeStr) throws ParseException {
        SimpleDateFormat DATE_TIME_FORMATTER = getDateFormat(PATTERN_DATE_TIME);
        try {
            return DATE_TIME_FORMATTER.parse(dateTimeStr);
        } finally {
            returnDateFormat(DATE_TIME_FORMATTER);
        }
    }

    /**
     * 把秒数转换成把毫秒数
     *
     * @param seconds 秒数的数组
     * @return {@link Long} 毫秒数
     */
    public static long toMillisSecond(long... seconds) {
        long millis = 0L;
        if (seconds != null && seconds.length > 0) {
            for (long time : seconds) {
                millis += (time * 1000);
            }
        }
        return millis;
    }

    /**
     * 把毫秒数转换成把秒数
     *
     * @param millis 毫秒数的数组
     * @return {@link Long} 毫秒数
     */
    public static long toSecond(long... millis) {
        long second = 0L;
        if (millis != null && millis.length > 0) {
            for (long time : millis) {
                second += (time / TimeConstant.ONE_SECOND_MILLISECOND);
            }
        }
        return second;
    }

    public static int toSecond(Date date) {
        return (int) (date.getTime() / TimeConstant.ONE_SECOND_MILLISECOND);
    }

    /**
     * 修改日期
     *
     * @param theDate 待修改的日期
     * @param addDays 加减的天数
     * @param hour    设置的小时
     * @param minute  设置的分
     * @param second  设置的秒
     * @return 修改后的日期
     */
    public static Date changeDateTime(Date theDate, int addDays, int hour, int minute, int second) {
        if (theDate == null) {
            return null;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(theDate);

        cal.add(DAY_OF_MONTH, addDays);

        if (hour >= 0 && hour <= 24) {
            cal.set(HOUR_OF_DAY, hour);
        }
        if (minute >= 0 && minute <= 60) {
            cal.set(MINUTE, minute);
        }
        if (second >= 0 && second <= 60) {
            cal.set(SECOND, second);
        }
        cal.set(MILLISECOND, 0);

        return cal.getTime();
    }

    public static Date add(Date theDate, int addHours, int addMinutes, int addSecond) {
        if (theDate == null) {
            return null;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(theDate);

        cal.add(HOUR_OF_DAY, addHours);
        cal.add(MINUTE, addMinutes);
        cal.add(SECOND, addSecond);

        return cal.getTime();
    }

    /**
     * 取得星期几
     *
     * @param theDate
     * @return
     */
    public static int dayOfWeek(Date theDate) {
        if (theDate == null) {
            return -1;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(theDate);

        return cal.get(DAY_OF_WEEK);
    }

    /**
     * 获得某一时间的0点
     *
     * @param theDate 需要计算的时间
     */
    public static Date getDate0AM(Date theDate) {
        if (theDate == null) {
            return null;
        }

        // Calendar cal = Calendar.getInstance();
        // cal.setTime(theDate);
        // return new GregorianCalendar(cal.get(YEAR), cal.get(MONTH),
        // cal.get(DAY_OF_MONTH)).getTime();
        return changeDateTime(theDate, 0, 0, 0, 0);
    }

    /**
     * 获得某一时间的下一个0点
     *
     * @param theDate 需要计算的时间
     */
    public static Date getNextDay0AM(Date theDate) {
        if (theDate == null) {
            return null;
        }
        return changeDateTime(theDate, 1, 0, 0, 0);
    }

    /**
     * 获得指定日期的23点59分59秒的时间
     *
     * @param theDate 需要计算的时间
     */
    public static Date getThisDay2359PM(Date theDate) {
        if (theDate == null) {
            return null;
        }
        return changeDateTime(theDate, 0, 23, 59, 59);
    }

//    /**
//     * 计算2个时间相差的天数,这个方法算的是2个零点时间的绝对时间(天数)
//     *
//     * @param startDate 起始时间
//     * @param endDate   结束时间
//     */
//    public static int calc2DateTDOADays(Date startDate, Date endDate) {
//        if (startDate == null || endDate == null) {
//            return 0;
//        }
//        Date startDate0AM = getDate0AM(startDate);
//        Date endDate0AM = getDate0AM(endDate);
//        long v1 = startDate0AM.getTime() - endDate0AM.getTime();
//        return Math.abs((int) Tools.divideAndRoundUp(v1, TimeConstant.ONE_DAY_MILLISECOND, 0));
//    }

    /**
     * 获得指定时间的下个周一的00:00:00的时间
     *
     * @param date 指定的时间
     * @return {@link Date} 周一的00:00:00的时间
     */
    public static Date getNextMonday(Date date) {
        if (date == null) {
            return null;
        }

        // 本周周一
        // Calendar cal = Calendar.getInstance();
        // cal.setTime(DateUtil.getDate0AM(date));
        // cal.set(DAY_OF_WEEK, MONDAY);
        //
        // Calendar nextMondayCal = Calendar.getInstance();
        // nextMondayCal.setTimeInMillis(cal.getTimeInMillis() +
        // ONE_DAY_MILLISECOND * 7);
        // return nextMondayCal.getTime();
        return changeDateTime(date, 7, 0, 0, 0);
    }

    /**
     * 获得获得改变后的时间
     *
     * @param addDay 增加的天数(减少天数, 则传负数)
     * @param to0AM  是否取0点时间
     * @return
     */
    public static Date add(int addDay, boolean to0AM) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(DATE, addDay);
        Date time = calendar.getTime();
        return to0AM ? getDate0AM(time) : time;
    }


//    /**
//     * 根据cron表达式判断当前是否到达刷新时间
//     */
//    public static boolean needRefreshByCron(String cron, Date lastRefreshDate, boolean refreshDiffDate) {
//        if (lastRefreshDate == null) {
//            return true;
//        }
//        Date now = new Date();
//        if (refreshDiffDate && !DateUtils.isInAday(lastRefreshDate, now)) {
//            return true;
//        }
//        Calendar calendar = Calendar.getInstance();
//        // 根据上次刷新时间获得的下次刷新的时间
//        Date nextTimebyLast = DateUtils.getNextTime(cron, lastRefreshDate);
//        calendar.setTime(nextTimebyLast);
//        int nextHourByLast = calendar.get(Calendar.HOUR_OF_DAY);
//
//        // 根据当前时间获得下次刷新的时间
//        Date nextTimebyNow = DateUtils.getNextTime(cron, now);
//        calendar.setTime(nextTimebyNow);
//        int nextHourByNow = calendar.get(Calendar.HOUR_OF_DAY);
//
//        // 两者时间不一样，刷新
//        if (nextHourByLast != nextHourByNow) {
//            return true;
//        }
//        return false;
//    }

    public static long getNowTimeStamp() {
        return System.currentTimeMillis();
    }

    public static Date getNowDate() {
        return new Date();
    }

    public static String getNowFormatDate() {
        return format(new Date(), PATTERN_DATE_TIME);
    }


    /**
     * 获得当前时间的秒
     *
     * @return {@link Long} 当前时间的秒
     */
    public static int getCurrentSecond() {
        return (int) toSecond(System.currentTimeMillis());
    }

    /**
     * 按格式获取现在的时间
     *
     * @param pattern
     * @return
     */
    public static String nowString(String pattern) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        Date currDate = cal.getTime();
        return format(currDate, pattern);
    }

    /**
     * 获取间隔时间天数的时间
     *
     * @Param: [pattern]
     * @return: java.lang.String
     * @Date: 2019/8/21
     */
    public static String intervalString(int intervalDay) {
        Date interval = add(intervalDay, true);
        return format(interval, PATTERN_DATE);
    }

    public static String getDateString(Date date, String pattern) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        Date currDate = calendar.getTime();
        return format(currDate, pattern);
    }

    public static String format(Date d, String pattern) {
        if (d == null) {
            return null;
        }
        SimpleDateFormat dateFromat = getDateFormat(pattern);
        return dateFromat.format(d);
    }


    public static String formatDefault(Date date) {
        return format(date, PATTERN_DATE_TIME);
    }

    public static String formatDefault(int second) {
        long timestamp = (long) second * TimeConstant.ONE_SECOND_MILLISECOND;
        return format(new Date(timestamp), PATTERN_DATE_TIME);
    }

    public static String formatDefault(Long timestamp) {
        return format(new Date(timestamp), PATTERN_DATE_TIME);
    }

    /**
     * 转换为时间（天,时:分:秒.毫秒）
     *
     * @param timeMillis
     * @return
     */
    public static String formatDateTime(long timeMillis) {
        long day = timeMillis / (24 * 60 * 60 * 1000);
        long hour = (timeMillis / (60 * 60 * 1000) - day * 24);
        long min = ((timeMillis / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (timeMillis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        long sss = (timeMillis - day * 24 * 60 * 60 * 1000 - hour * 60 * 60 * 1000 - min * 60 * 1000 - s * 1000);
        return (day > 0 ? day + "," : "") + hour + ":" + min + ":" + s + "." + sss;
    }

    /**
     * 计算time2距离time1时间天数
     *
     * @param time1
     * @param time2
     * @return 差距
     */
    public static long getQuot(String time1, String time2) {
        long quot = 0;
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date1 = ft.parse(time1);
            Date date2 = ft.parse(time2);
            quot = date1.getTime() - date2.getTime();
            quot = quot / TimeConstant.ONE_DAY_MILLISECOND; // 1000 / 60 / 60 /
            // 24; 一天 的毫秒数
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return quot;
    }

    public static Integer getTimeStamp() {
        long timeStamp = System.currentTimeMillis() / 1000;
        return (int) timeStamp;
    }

    public static String getStringTimeStamp() {
        return String.valueOf(System.currentTimeMillis() / 1000);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
//    public static String formatDate(Date date, Object... pattern) {
//        String formatDate = null;
//        if (pattern != null && pattern.length > 0) {
//            formatDate = DateFormatUtils.format(date, pattern[0].toString());
//        } else {
//            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
//        }
//        return formatDate;
//    }

    public static String getYMD() {
        SimpleDateFormat DATE_FORMATTER = getDateFormat(PATTERN_DATES);
        try {
            return DATE_FORMATTER.format(new Date());
        } finally {
            returnDateFormat(DATE_FORMATTER);
        }
    }

    public static void main(String[] args) {
        String paymentDateStr = getNowFormatDate();
        System.err.println(paymentDateStr);
    }

//    public static String getStrToDate(String strDate) {
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//        ParsePosition pos = new ParsePosition(0);
//        Date strtodate = formatter.parse(strDate, pos);
//        String date = formatDate(strtodate, "yyyyMMdd");
//        return date;
//    }

    public static boolean isNow(Date date) {
        // 当前时间
        Date now = new Date();
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        // 获取今天的日期
        String nowDay = sf.format(now);

        // 对比的时间
        String day = sf.format(date);

        return day.equals(nowDay);

    }

    /**
     * 获取当前时间的下个周一日期
     *
     * @Param: [date]
     * @return: java.util.Date
     * @Date: 2019/3/15
     */
    public static Date getNextMonday0AMDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(getThisWeekMonday0AMDate(date));
        calendar.add(Calendar.DATE, 7);
        return calendar.getTime();
    }

    /**
     * 获取当前时间的上个周一日期
     *
     * @Param: [date]
     * @return: java.util.Date
     * @Date: 2019/3/15
     */
    public static Date getLastMonday0AMDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(getThisWeekMonday0AMDate(date));
        calendar.add(Calendar.DATE, -7);
        return calendar.getTime();
    }

    public static Date getThisWeekMonday0AMDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayWeek = calendar.get(Calendar.DAY_OF_WEEK);//获得当前日期是一个星期的第几天
        if (1 == dayWeek) {
            calendar.add(Calendar.DAY_OF_MONTH, -1);
        }
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        int day = calendar.get(Calendar.DAY_OF_WEEK);//获得当前日期是一个星期的第几天
        calendar.add(Calendar.DATE, calendar.getFirstDayOfWeek() - day);//根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        Date nextMonday = calendar.getTime();
        return getDate0AM(nextMonday);
    }


    /**
     * T+1 到账下，校验提现是否到下周一
     *
     * @Param: [date]
     * @return: boolean
     * @Date: 2019/3/15
     */
    public static boolean isTradePresentWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int week = calendar.get(Calendar.DAY_OF_WEEK);
        return week == Calendar.SATURDAY || week == Calendar.SUNDAY || week == Calendar.FRIDAY;
    }

    /**
     * 获取订单支付金额转到余额的计划启动时间
     *
     * @Param: [date]
     * @return: java.util.Date
     * @Date: 2019/12/20
     */
    public static Date getFreezeMoneyActiveTime(Date date) {
        boolean weekendTrade = isTradePresentWeek(date);
        if (weekendTrade) {
            return getNextMonday0AMDate(date);
        }
        return getNextDay0AM(date);
    }


}
