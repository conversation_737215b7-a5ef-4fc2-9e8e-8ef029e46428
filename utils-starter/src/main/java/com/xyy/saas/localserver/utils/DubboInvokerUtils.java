package com.xyy.saas.localserver.utils;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import org.apache.dubbo.common.config.ReferenceCache;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;
import org.apache.dubbo.config.spring.util.DubboBeanUtils;
import org.apache.dubbo.config.utils.SimpleReferenceCache;
import org.apache.dubbo.rpc.service.GenericService;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.util.StringUtils;

/**
 * Dubbo客户端
 *
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/04/12 15:56
 */
public class DubboInvokerUtils implements BeanFactoryAware {

	/**
	 * serviceName:methodName    GenericService
	 */
	private static final Map<String, GenericService> stubCache = Maps.newConcurrentMap();


	private static BeanFactory beanFactory;


	public static <T> List<T> invokers(String interfaceName, String methodName, String group,
		boolean merger, String[] parameterTypes, Object[] args, Class<T> clazz) {
		Object resultObject = invokerRpc(interfaceName, methodName, group, merger, parameterTypes,
			args);
		// 泛化调用返参为Object类型，这里做一个参数转换
		return JSON.parseArray(JSON.toJSONString(resultObject), clazz);
	}

	public static <T> T invoker(String interfaceName, String methodName, String group,
		boolean merger, String[] parameterTypes, Object[] args, Class<T> clazz) {
		Object resultObject = invokerRpc(interfaceName, methodName, group, merger, parameterTypes,
			args);
		// 泛化调用返参为Object类型，这里做一个参数转换
		return JSON.parseObject(JSON.toJSONString(resultObject), clazz);
	}

	public static Object invokerRpc(String interfaceName, String methodName, String group,
		boolean merger, String[] parameterTypes, Object[] args) {
		if (!StringUtils.hasText(interfaceName)) {
			throw new RuntimeException("DubboInvokerUtils invoker serviceName is null");
		}
		if (!StringUtils.hasText(methodName)) {
			throw new RuntimeException("DubboInvokerUtils invoker methodName is null");
		}
		ApplicationConfig applicationConfig = DubboBeanUtils.getApplicationModel(beanFactory)
			.getApplicationConfigManager().getApplication().orElseThrow();
//		RegistryConfig registryConfig = applicationConfig.getRegistry();
//		try {
		// 先尝试从缓存中拿GenericService 设置缓存的原因：ReferenceConfig实例很重，封装了与注册中心的连接以及与provider的连接，需要缓存，否则重复生成ReferenceConfig可能造成性能问题并且会有内存和连接泄漏
		String key = interfaceName + ":" + methodName + group;
		GenericService genericService = stubCache.get(key);
		// 未取到则准备初始化当前GenericService
		if (genericService == null) {
			ReferenceConfig<GenericService> reference = new ReferenceConfig<>();
			// 连接相关的配置，包括：zk地址、应用名、协议等
			reference.setApplication(applicationConfig);
//			reference.setScopeModel(applicationConfig.getScopeModel());
//			reference.setRegistry(registryConfig);
			// 调用相关的配置，包括：声明为泛化接口、不检查状态、负载均衡方式、超时时间等
			reference.setGeneric(true);
			reference.setLoadbalance("roundrobin");
			reference.setInterface(interfaceName);
			reference.setCheck(false);
			reference.setGroup(group);
//			reference.setServices(serviceName);
			reference.setMerger(merger ? "true" : "false");
			reference.setTimeout(6000);
			// 调用目标provider相关的配置，包括：接口、group、version、
//			reference.setInterface(serviceName);
//				reference.setServices();
//				reference.setVersion(StringUtils.isBlank(version) ? AuditConstants.AUDIT_CALLBACK_DEFALUT_VERSION : version);
			// 这里优先使用dubbo内置的简单缓存工具类进行缓存，若没有则放入自己定义的缓存stubCache中
//			ReferenceCache cache = DubboBootstrap.getInstance().getCache();
//			genericService = cache.get(reference);
			ReferenceCache cache = SimpleReferenceCache.getCache();
			genericService = cache.get(reference);
			if (genericService != null) {
				stubCache.putIfAbsent(key, genericService);
			}
			genericService = stubCache.get(key);
		}
		// 至此，拿到了dubbo接口的genericService
		if (genericService == null) {
			throw new IllegalStateException(
				"No provider available: " + key + "serviceName:" + interfaceName);
		}
		// 泛化调用的参数：方法名、方法参数类型全路径、方法参数
		return genericService.$invoke(methodName, parameterTypes, args);
	}


	/**
	 *
	 */
	@Override
	public void setBeanFactory(BeanFactory beanFactory) {
		DubboInvokerUtils.beanFactory = beanFactory;
	}
}
