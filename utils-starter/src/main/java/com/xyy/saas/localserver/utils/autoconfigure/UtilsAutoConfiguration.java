package com.xyy.saas.localserver.utils.autoconfigure;

import com.xyy.saas.localserver.utils.DubboInvokerUtils;
import com.xyy.saas.localserver.utils.EventPusher;
import com.xyy.saas.localserver.utils.SpringUtils;
import org.apache.dubbo.config.ReferenceConfigBase;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 11:34
 */
@Configuration
public class UtilsAutoConfiguration  {

	@Bean
	public SpringUtils getSpringBeanUtils() {
		return new SpringUtils();
	}

	@Bean
	public EventPusher getEventPusher() {
		return new EventPusher();
	}

	@Bean
	@ConditionalOnClass(ReferenceConfigBase.class)
	public DubboInvokerUtils getDubboInvokerUtils() {
		return new DubboInvokerUtils();
	}
}
