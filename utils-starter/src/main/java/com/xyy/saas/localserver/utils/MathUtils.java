package com.xyy.saas.localserver.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Desc 计算工具类
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/1/20 17:33
 */
public class MathUtils {

    /**
     * 提供精确的小数位四舍五入处理。
     *
     * @param value
     *            需要四舍五入的数字
     * @param scale
     *            小数点后保留几位
     * @return {@link Double} 四舍五入后的结果
     */
    public static double round(double value, int scale) {
        BigDecimal b = new BigDecimal(Double.toString(value));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one, scale, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 提供精确的小数位向下取整处理。
     *
     * @param v
     *            需要向下取证的数字
     * @param scale
     *            精度(小数点后保留几位)
     * @return {@link Double} 向下取证后的数值
     */
    public static double roundDown(double v, int scale) {
        BigDecimal b = new BigDecimal(Double.toString(v));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one, scale, RoundingMode.DOWN).doubleValue();
    }

    /**
     * 提供精确的小数位向上取整处理。
     *
     * @param value
     *            需要向上取整的数字
     * @param scale
     *            精度(小数点后保留几位)
     * @return {@link Double} 向上取证后的数值
     */
    public static double roundUp(double value, int scale) {
        BigDecimal b = new BigDecimal(Double.toString(value));
        BigDecimal one = new BigDecimal("1");
        return b.divide(one, scale, RoundingMode.UP).doubleValue();
    }

    /**
     * 提供精确相除向上取整方法
     *
     * @param value1
     *            被除数
     * @param value2
     *            除数(不能为0)
     * @param scale
     *            精度(小数点后保留几位)
     * @return {@link Double} 向上取证后的数值
     */
    public static double divideAndRoundUp(double value1, double value2, int scale) {
        BigDecimal bd1 = new BigDecimal(value1);
        BigDecimal bd2 = new BigDecimal(value2);
        return bd1.divide(bd2, scale, RoundingMode.UP).doubleValue();
    }

    /**
     * 提供精确相除向下取整方法
     *
     * @param value1
     *            被除数
     * @param value2
     *            除数(不能为0)
     * @param scale
     *            精度(小数点后保留几位)
     * @return {@link Double} 向上取证后的数值
     */
    public static double divideAndRoundDown(double value1, double value2, int scale) {
        BigDecimal bd1 = new BigDecimal(value1);
        BigDecimal bd2 = new BigDecimal(value2);
        return bd1.divide(bd2, scale, RoundingMode.DOWN).doubleValue();
    }

    /**
     * 保留n位小数
     * @param a  原始数据
     * @param n 保留几位
     * @return 返回保留n位小数
     */
    public static double splitAndRound(double a, int n) {
        a = a * Math.pow(10, n);
        return (Math.round(a)) / (Math.pow(10, n));
    }
}
