package com.xyy.saas.localserver.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationEvent;

import jakarta.annotation.PostConstruct;

/**
 * @Desc 事件推送器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 11:38
 */
@Slf4j
public class EventPusher implements ApplicationContextAware {

    protected static EventPusher instance;

    private ApplicationContext applicationContext;

    @PostConstruct
    public void init() {
        instance = this;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 推送同步事件
     *
     * @param event
     */
    public static <T extends ApplicationEvent> void syncPost(T event) {
		log.debug("source [{}] syncPost event[{}]=[{}]", event.getSource(), event.getClass().getName());
        instance.applicationContext.publishEvent(event);
    }

}
