-- pro 表
CREATE TABLE pro (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY, -- H2 自增主键语法
    code VARCHAR(32) UNIQUE NOT NULL,
    `value` VARCHAR(255) DEFAULT '' NOT NULL
);
COMMENT ON COLUMN pro.code IS '编码';  -- H2 列注释需单独声明
COMMENT ON COLUMN pro.`value` IS '值';
-- data_sync_box 表
CREATE TABLE data_sync_box (
    id INT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id VARCHAR NOT NULL,
    table_name VARCHAR NOT NULL,
    dml_type VARCHAR NOT NULL,
    source_id VARCHAR NOT NULL,
    data VARCHAR NOT NULL,
    sync_count INT DEFAULT 0 NOT NULL,
    base_version INT DEFAULT 1 NOT NULL,
    direction INT DEFAULT 0 NOT NULL,
    "desc" VARCHAR NULL,                -- 转义关键字 desc
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    last_sync_time TIMESTAMP NULL       -- H2 允许显式声明 NULL
);
-- 添加列注释（H2 不支持内联注释）
COMMENT ON COLUMN data_sync_box.user_id IS '机构id';
COMMENT ON COLUMN data_sync_box.table_name IS '表名';
COMMENT ON COLUMN data_sync_box.dml_type IS 'dml操作类型: INSERT, UPDATE, DELETE';
COMMENT ON COLUMN data_sync_box.source_id IS '源id';
COMMENT ON COLUMN data_sync_box.data IS '数据';
COMMENT ON COLUMN data_sync_box.sync_count IS '同步次数';
COMMENT ON COLUMN data_sync_box.base_version IS '版本号';
COMMENT ON COLUMN data_sync_box.direction IS '同步方向,1:云向端同步,2:端向云同步';
COMMENT ON COLUMN data_sync_box."desc" IS '描述';
