package com.xyy.saas.datasync.h2.test;

import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.h2.XyySaaSLocalServerTest;
import com.xyy.saas.datasync.h2.test.service.RegisterRecordService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-12 9:10
 */


@EnableDataSyncScan
@MapperScan({"com.xyy.saas.datasync.h2.test.mapper", "com.xyy.saas.datasync.client.jdbc",
    "com.xyy.saas.datasync.client.dao", "com.xyy.saas.datasync.client.db.table"})
@ComponentScan("com.xyy.saas.datasync")
@Transactional(rollbackFor = Throwable.class)
@Rollback(false)
@Slf4j
@DirtiesContext
@ExtendWith(SpringExtension.class)
@XyySaaSLocalServerTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DatasyncStatementProxyH2Test {

    @Autowired
    private RegisterRecordService registerRecordService;

    boolean exception = false;

    @BeforeEach
    public void setUp() {
        DataContextHolder.setTenantId("ZHL00043499");
        exception = true;
    }

    @Test
    public void registerRecordMapperTest() {
        registerRecordService.dataSyncTest(exception);
    }
}
