package com.xyy.saas.datasync.h2;

import com.xyy.saas.datasync.client.EnableDataSyncScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

import java.util.TimeZone;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-03 21:30
 */
@EnableDataSyncScan
@SpringBootApplication
public class DatasyncClientTestApplication {

    public static void main(String[] args) throws InterruptedException {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
		SpringApplication app = new SpringApplication(DatasyncClientTestApplication.class);
		app.setBannerMode(Banner.Mode.OFF);
		app.run(args);
		Thread.currentThread().join();
	}

}
