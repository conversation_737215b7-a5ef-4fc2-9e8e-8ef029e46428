server:
  port: 17080
spring:
  application:
    name: data-sync-server #@artifactId@
#  datasource:
#    url: *******************************************************************************************************************************************************************************************************************************
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    username: root
#    password: zff123456
dubbo: #dubbo与nacos的通信配置
  application:
    name: data-sync-server #@artifactId@ #provider在Nacos中的应用id
    qos-enable: false
    serialize-check-status: WARN
  registry:
    #    address: zookeeper://127.0.0.1:2181
    address: nacos://127.0.0.1:8848
    check: false
  config-center:
    address: nacos://127.0.0.1:8848
  metadata-report:
    address: nacos://127.0.0.1:8848
  protocol:
    name: dubbo #通信协议名
    port: -1 #配置通信端口，默认为20880
  scan:
    base-packages: com.xyy.saas.datasync.server
  consumer:
    check: false
  provider:
    prefer-serialization: java
