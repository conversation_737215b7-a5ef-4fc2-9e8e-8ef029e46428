package com.xyy.saas.datasync.server.mqtt;

import com.xyy.saas.datasync.client.protocol.mqtt.MQTTPublisher;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.SmartLifecycle;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/13 上午9:56
 */
@Slf4j
public class DelayMQTTPublisherImpl implements MQTTPublisher, SmartLifecycle {

	/**
	 * key = tenantId:ClientId , value = Map<tableName, 放进去的毫秒数>
	 */
	@Getter
	private final ConcurrentHashMap<String, ConcurrentHashMap<String, Long>> syncTableCache = new ConcurrentHashMap<>();

	private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

	public DelayMQTTPublisherImpl() {
	}

	/**
	 * 获取这个设备需要更新的表
	 *
	 * @param tenantId
	 * @param clientId
	 * @return
	 */
	@Override
	public List<String> getSyncTableCache(String tenantId, String clientId) {
		String key = generateKey(tenantId, clientId);
		ConcurrentHashMap<String, Long> tableMap = syncTableCache.get(key);
		if (tableMap != null) {
			return new ArrayList<>(tableMap.keySet());
		}
		return Collections.emptyList();
	}

	public Set<String> getOnlineDeviceIdByTenant(String tenantId) {
		// 默认只有一个设备，这里返回一个tenant字符串
		Set<String> deviceIds = new HashSet<>();
		deviceIds.add(tenantId);
		return deviceIds;
	}

	@Override
	public void publish(String tenantId, String tableName) {
		Set<String> deviceIds = getOnlineDeviceIdByTenant(tenantId);
		if (CollectionUtils.isEmpty(deviceIds)) {
			return;
		}
		long currentTimeMillis = System.currentTimeMillis();
		deviceIds.forEach(deviceId -> {
			String deviceKey = generateKey(tenantId, deviceId);
			syncTableCache.computeIfAbsent(deviceKey, k -> new ConcurrentHashMap<>()).put(tableName, currentTimeMillis);
		});
	}

	@Override
	public void publishDevice(String tenantId, String clientId, String topic, String tableName) {
		String key = generateKey(tenantId, clientId);
		syncTableCache.computeIfAbsent(key, k -> new ConcurrentHashMap<>()).put(tableName, System.currentTimeMillis());
	}

	@Override
	public void broadcast(String topic, String data) {
		// 广播给所有设备，示例中直接输出
		System.out.println("Broadcasting to topic: " + topic + " -> " + data);
		//TODO broadcast to all devices
	}

	/**
	 * 每天凌晨1点 清理超过24小时的缓存, 防止内存泄漏
	 */
	private void startDailyCleanup() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 1);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);

		long initialDelay = calendar.getTimeInMillis() - System.currentTimeMillis();
		if (initialDelay < 0) {
			initialDelay += TimeUnit.DAYS.toMillis(1);
		}

		scheduler.scheduleAtFixedRate(() -> {
			log.info("-----------Daily cleanup started-------------");
			long currentTimeMillis = System.currentTimeMillis();
			syncTableCache.forEach((key, tableMap) -> {
				tableMap.entrySet().removeIf(entry -> currentTimeMillis - entry.getValue() > TimeUnit.HOURS.toMillis(24));
				if (tableMap.isEmpty()) {
					syncTableCache.remove(key);
				}
			});
			log.info("-----------Daily cleanup finished-------------");
		}, initialDelay, TimeUnit.DAYS.toMillis(1), TimeUnit.MILLISECONDS);
	}

	private String generateKey(String tenantId, String clientId) {
		return tenantId + ":" + clientId;
	}

	/**
	 *
	 */
	@Override
	public void start() {
		startDailyCleanup();
	}

	/**
	 *
	 */
	@Override
	public void stop() {
		// 安全停止
		scheduler.shutdown();
	}

	@Override
	public boolean isRunning() {
		return !scheduler.isShutdown();
	}
}
