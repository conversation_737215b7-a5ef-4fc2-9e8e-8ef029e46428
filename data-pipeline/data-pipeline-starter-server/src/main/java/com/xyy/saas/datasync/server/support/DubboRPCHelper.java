//package com.xyy.saas.datasync.server.support;
//
//import com.alibaba.fastjson2.JSON;
//import com.google.common.collect.Maps;
//import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;
//import java.util.Collections;
//import java.util.List;
//import java.util.Map;
//import org.apache.dubbo.common.config.ReferenceCache;
//import org.apache.dubbo.config.ApplicationConfig;
//import org.apache.dubbo.config.ReferenceConfig;
//import org.apache.dubbo.config.RegistryConfig;
//import org.apache.dubbo.config.annotation.DubboService;
//import org.apache.dubbo.config.bootstrap.DubboBootstrap;
//import org.apache.dubbo.config.spring.util.DubboBeanUtils;
//import org.apache.dubbo.rpc.service.GenericService;
//import org.springframework.beans.factory.BeanFactory;
//import org.springframework.beans.factory.BeanFactoryAware;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * @Desc
// * <AUTHOR>  <EMAIL>
// * @Date Created in 2023/04/03 17:50
// */
//@Component
//@DubboService
//public class DubboRPCHelper implements BeanFactoryAware {
//
//	/**
//	 * Biz:tableName    GenericService 目前业务线Biz是空,实际是<tableName    GenericService>
//	 */
//	private static final Map<String, GenericService> stubCache = Maps.newConcurrentMap();
//
//	@Autowired
//	private DataSyncService dataSyncService;
//
//	private BeanFactory beanFactory;
//
//	public List invoker(String cid, String biz, String tableName, Long baseVersion) {
//		ApplicationConfig applicationConfig = DubboBeanUtils.getApplicationModel(beanFactory)
//			.getApplicationConfigManager().getApplication().orElseThrow();
//		RegistryConfig registryConfig = applicationConfig.getRegistry();
////		try {
//		// 先尝试从缓存中拿GenericService 设置缓存的原因：ReferenceConfig实例很重，封装了与注册中心的连接以及与provider的连接，需要缓存，否则重复生成ReferenceConfig可能造成性能问题并且会有内存和连接泄漏
//		String key = biz == null ? tableName : biz + ":" + tableName;
//		String serviceName = dataSyncService.getServiceName(biz, tableName);
//		GenericService genericService = stubCache.get(key);
//		// 未取到则准备初始化当前GenericService
//		if (genericService == null) {
//			ReferenceConfig<GenericService> reference = new ReferenceConfig<>();
//			// 连接相关的配置，包括：zk地址、应用名、协议等
////			reference.setApplication(applicationConfig);
//			reference.setScopeModel(applicationConfig.getScopeModel());
//			reference.setRegistry(registryConfig);
//			// 调用相关的配置，包括：声明为泛化接口、不检查状态、负载均衡方式、超时时间等
//			reference.setGeneric(true);
//			reference.setLoadbalance("roundrobin");
//			reference.setCheck(false);
//			reference.setGroup(serviceName);
//			reference.setTimeout(6000);
//			// 调用目标provider相关的配置，包括：接口、group、version、
////			reference.setInterface(serviceName);
////				reference.setServices();
////				reference.setVersion(StringUtils.isBlank(version) ? AuditConstants.AUDIT_CALLBACK_DEFALUT_VERSION : version);
//			// 这里优先使用dubbo内置的简单缓存工具类进行缓存，若没有则放入自己定义的缓存stubCache中
//			ReferenceCache cache = DubboBootstrap.getInstance().getCache();
//			genericService = cache.get(reference);
//			if (genericService != null) {
//				stubCache.putIfAbsent(key, genericService);
//			}
//			genericService = stubCache.get(key);
//		}
//		// 至此，拿到了dubbo接口的genericService
//		if (genericService == null) {
//			throw new IllegalStateException(
//				"No provider available: " + key + "serviceName:" + serviceName);
//		}
//		// 泛化调用的参数：方法名、方法参数类型全路径、方法参数
//		String[] parameterTypes = new String[]{"java.lang.String", "java.lang.String",
//			"java.lang.Long"};
//		Object[] args = new Object[]{cid, tableName, baseVersion};
//		Object result = genericService.$invoke("invokerLookup", parameterTypes, args);
//		if (result == null) {
//			return Collections.EMPTY_LIST;
//		}
//		// 泛化调用返参为Object类型，这里做一个参数转换
//		List<DataPullVO> dataPullVOs = JSON.parseArray(JSON.toJSONString(result),
//			DataPullVO.class);
//		return dataPullVOs;
//	}
//
//
//	/**
//	 *
//	 */
//	@Override
//	public void setBeanFactory(BeanFactory beanFactory) {
//		this.beanFactory = beanFactory;
//	}
//}
