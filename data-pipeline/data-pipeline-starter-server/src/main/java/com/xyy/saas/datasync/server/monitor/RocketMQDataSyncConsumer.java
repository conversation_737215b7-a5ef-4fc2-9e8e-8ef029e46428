package com.xyy.saas.datasync.server.monitor;

import com.xyy.saas.datasync.client.worker.DataSyncMonitor;

/**
 * @Desc RocketMQ消费变更, 一般适用于跨团队canal变更，Canal好处是数据库有变更也可以马上同步，包括是线上数据库直接操作
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-21 17:07
 */
public class RocketMQDataSyncConsumer implements DataSyncMonitor {

    /**
     * 消费变更
     */
    @Override
    public void consumerAlteration() {

    }
}
