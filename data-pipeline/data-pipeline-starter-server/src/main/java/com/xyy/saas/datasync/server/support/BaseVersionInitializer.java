package com.xyy.saas.datasync.server.support;

import cn.hutool.extra.spring.SpringUtil;
import com.xyy.saas.datasync.client.constants.DataSyncUtil;
import com.xyy.saas.datasync.client.db.DataTableDmlTransformer;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.SmartLifecycle;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/5/10 上午10:50
 */
@Slf4j
public class BaseVersionInitializer implements SmartLifecycle {

	private JdbcTemplate jdbcTemplate;

	private boolean isCheckBaseVersion = true;

	private AtomicBoolean running = new AtomicBoolean(false);

	public BaseVersionInitializer(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	/**
	 *
	 */
	@Override
	public void start() {
		if (!isCheckBaseVersion) {
			return;
		}
		if (running.compareAndSet(false, true)) {
			checkAllTable();
			running.set(false);
		}

	}

	/**
	 *
	 */
	@Override
	public void stop() {
	}

	/**
	 * @return
	 */
	@Override
	public boolean isRunning() {
		return running.get();
	}

	public void checkAllTable() {
		Map<String, DataSyncEntityDefinition> tableEntityMapMap = DataSyncEntityDispatcher.DataSyncEntityContext.getTableEntityMap();
		tableEntityMapMap.forEach((tableName, dataSyncEntityDefinition) -> {
			boolean haveBaseVersion = SpringUtil.getBean(DataTableDmlTransformer.class)
				.checkTableColumnExist(tableName, DataSyncUtil.BASE_VERSION);
			if (!haveBaseVersion) {
				initTableBaseVerion(tableName);
			}
		});

	}

	/**
	 * 初始化BaseVerion信息
	 *
	 * @param tableName
	 */
	public void initTableBaseVerion(String tableName) {
		alterColumn(tableName);
		// 检查表是否存在 tenant_id 字段
		boolean hasTenantId = SpringUtil.getBean(DataTableDmlTransformer.class)
			.checkTableColumnExist(tableName, DataSyncUtil.TENANT_ID);
		String updateSql = getUpdateBaseVersionSQL(tableName, hasTenantId);
		jdbcTemplate.update(updateSql);
	}

	/**
	 * 获取刷新base_version的sql，目前只支持mysql8的窗口函数,如果不一致窗口函数
	 *
	 * @param tableName
	 * @param hasTenantId
	 * @return
	 */
	private String getUpdateBaseVersionSQL(String tableName, boolean hasTenantId) {
		String hasTenantIdStr = !hasTenantId ? "" : " PARTITION BY tenant_id ";
		String updateSql = "UPDATE " + tableName + " t " +
			"JOIN ( " +
			"   SELECT id, " +
			"          ROW_NUMBER() OVER (" + hasTenantIdStr + "ORDER BY id) + MAX(base_version) OVER (" + hasTenantIdStr + ") AS row_num " +
			"   FROM " + tableName +
			") AS rankedRecords ON t.id = rankedRecords.id " +
			"SET t.base_version = rankedRecords.row_num";
		return updateSql;
	}

	/**
	 * 表新增base_version 列
	 *
	 * @param tableName
	 */
	public void alterColumn(String tableName) {
		String addColumnSql = "ALTER TABLE " + tableName + " ADD COLUMN " + DataSyncUtil.BASE_VERSION + " BIGINT DEFAULT 0";
		jdbcTemplate.execute(addColumnSql);
	}


}
