package com.xyy.saas.datasync.server.support;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;
import com.xyy.saas.datasync.client.protocol.vo.DataPushVO;
import com.xyy.saas.datasync.client.worker.DataSyncLookup;
import com.xyy.saas.datasync.client.worker.DataSyncRegister;
import com.xyy.saas.localserver.utils.DubboInvokerUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Desc 数据同步服务
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-21 16:05
 */

@Slf4j
@DubboService
public class DataSyncService implements ApplicationContextAware, DataSyncRegister, ApplicationRunner {


	private static ApplicationContext applicationContext;
	/**
	 * dubbo的分组聚合merger配置的group为特殊字符*或者,的时候nacos注册元信息会报错，NacosException：dataId invalid 需要dubbo解决
	 */
//	@DubboReference(group = "*", merger = "true")
//	private DataSyncLookup dataSyncLookup;

//	@DubboReference(group = "medicare-product")
//	private DataSyncLookup productSyncLookup;
//
//	@DubboReference(group = "medicare-merchant")
//	private DataSyncLookup merchantSyncLookup;

	@Autowired
	private ObjectProvider<DataSyncLookup> dataSyncLookupProvider;

	@Autowired
	private List<DataSyncLookup> dataSyncLookups = new ArrayList<>();

	/**
	 * biz:表名  ,  服务名
	 */
	private static Map<String, String> tableNameServceMap = Maps.newConcurrentMap();
	//	@DubboReference(group = "*", merger = "true", check = false)
//	@Autowired
//	private DataSyncLookup dataSyncLookup;

	public String getServiceName(String biz, String tableName) {
		return tableNameServceMap.get(biz + ":" + tableName);
	}

	public void registerSyncTables(String biz, String serviceName, List<String> tableNames) {
		if (!StringUtils.hasText(serviceName)) {
			throw new RuntimeException("DubboInvokerUtils invoker serviceName is null");
		}
		if (CollectionUtils.isEmpty(tableNames)) {
			throw new RuntimeException("DubboInvokerUtils invoker tableNames is null");
		}
		for (String tableName : tableNames) {
			String key = biz == null ? tableName : biz + ":" + tableName;
			tableNameServceMap.putIfAbsent(key, serviceName);
		}
	}


	/**
	 * 拉取所有服务的同步表对应关系,使用dubbo的分组聚合
	 */
	private void pullSyncTables() {
		for (DataSyncLookup dataSyncLookup : dataSyncLookups) {
			try {
				Map<String, List<String>> serverTableNames = dataSyncLookup.getServerTableNames();
				if (MapUtil.isEmpty(serverTableNames)) {
					log.error("pullSyncTables pullSyncTables isEmpty");
					return;
				}
				log.error("pullSyncTables 获取到结果 ", serverTableNames);
				serverTableNames.forEach((serverName, tableNames) -> {
					tableNames.forEach(tableName -> {
						tableNameServceMap.put(tableName, serverName);
					});
				});
			} catch (RpcException e) {
				log.error("dataSyncLookup No provider");
			}

		}
	}

	/**
	 * Callback used to run the bean.
	 *
	 * @param args incoming application arguments
	 * @throws Exception on error
	 */
	@Override
	public void run(ApplicationArguments args) throws Exception {
//		dataSyncLookups.add(productSyncLookup);
//		dataSyncLookups.add(merchantSyncLookup);
		pullSyncTables();
	}

	/**
	 * 调用实际的提供者（rpc 或者 方法调用）
	 *
	 * @param biz
	 * @param tenantId
	 * @param tableName
	 * @param baseVersion
	 * @return
	 */
	public DataPullVO pull(String biz, String tenantId, String tableName, Long baseVersion) {
		//TODO 流控代码
		String key = biz == null ? tableName : biz + ":" + tableName;
		if (applicationContext.containsBean("dubboAnnotationBeanPostProcessor")) {
			// Dubbo环境调用
			// 泛化调用的参数：方法名、方法参数类型全路径、方法参数
			String group = tableNameServceMap.get(key);
			String[] parameterTypes = new String[]{"java.lang.String", "java.lang.String", "java.lang.String",
				"java.lang.Long"};
			Object[] args = new Object[]{tenantId, tableName, baseVersion};

			return DubboInvokerUtils.invoker(
				DataSyncLookup.class.getName(), "invokerLookup",
				group, false, parameterTypes, args, DataPullVO.class);
		}

		DataSyncLookup dataSyncLookup = dataSyncLookupProvider.getIfAvailable();
		if (dataSyncLookup != null) {
			return dataSyncLookup.invokerLookup(biz, tenantId, tableName, baseVersion);
		}

		return null;
	}

	public List<Long> push(String tenantId, DataPushVO dataPushVO) {
		if (!dataPushVO.checkData()) {
			return Collections.emptyList();
		}
		//TODO 调用实际的提供者（rpc 或者 方法调用）
		DataSyncLookup dataSyncLookup = dataSyncLookupProvider.getIfAvailable();
		if (dataSyncLookup != null) {
			return dataSyncLookup.invokerPush(tenantId, dataPushVO);
		}
		return null;
	}

	/**
	 * @param applicationContext the ApplicationContext object to be used by this object
	 * @throws BeansException
	 */
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		DataSyncService.applicationContext = applicationContext;
	}

	public Map<String, List<TableColumn>> getTableInfo(List<String> tableNameList) {
		//TODO 调用实际的提供者（rpc 或者 方法调用）
		DataSyncLookup dataSyncLookup = dataSyncLookupProvider.getIfAvailable();
		if (dataSyncLookup != null) {
			return dataSyncLookup.getTableInfo(tableNameList);
		}
		return null;
	}


}
