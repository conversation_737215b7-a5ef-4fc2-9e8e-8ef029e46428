package com.xyy.saas.datasync.server;

import java.util.TimeZone;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * @Desc 数据同步服务
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/03/14 19:27
 */
//@EnableDubbo
@EnableDataSyncServer
@EnableDiscoveryClient
//@ImportResource("classpath:spring/dubbo-consumer.xml")
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
public class DataSyncServerApplication {

    public static void main(String[] args) {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
		SpringApplication app = new SpringApplication(DataSyncServerApplication.class);
		app.setBannerMode(Banner.Mode.OFF);
//		app.setWebApplicationType(WebApplicationType.NONE);
		app.run(args);
	}

}
