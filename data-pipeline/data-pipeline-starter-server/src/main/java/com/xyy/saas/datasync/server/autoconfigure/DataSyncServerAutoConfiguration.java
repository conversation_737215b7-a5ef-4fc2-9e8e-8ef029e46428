package com.xyy.saas.datasync.server.autoconfigure;

import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.datasync.client.autoconfigure.ConditionalOnSyncType;
import com.xyy.saas.datasync.client.filter.TenantHeaderFilter;
import com.xyy.saas.datasync.client.protocol.mqtt.MQTTPublisher;
import com.xyy.saas.datasync.server.mqtt.DelayMQTTPublisherImpl;
import com.xyy.saas.datasync.server.support.BaseVersionInitializer;
import com.xyy.saas.datasync.server.support.DataSyncService;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * @Desc 数据同步服务端自动配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-21 19:12
 */
@Configuration
public class DataSyncServerAutoConfiguration {


	@Bean
	public DataSyncService getDataSyncService() {
		return new DataSyncService();
	}

	@Bean
	@ConditionalOnSyncType(EnableDataSyncScan.SyncType.server)
	public MQTTPublisher getMQTTPublisher() {
		return new DelayMQTTPublisherImpl();
	}

	@Bean
	public BaseVersionInitializer baseVersionInitializer(JdbcTemplate jdbcTemplate) {
		return new BaseVersionInitializer(jdbcTemplate);
	}

	@Bean
	public FilterRegistrationBean<TenantHeaderFilter> tenantHeaderFilterRegistrationBean() {
		FilterRegistrationBean<TenantHeaderFilter> registration = new FilterRegistrationBean<>();
		registration.setFilter(new TenantHeaderFilter());
		// 根据需要设定过滤的URL模式
		registration.addUrlPatterns("/*");
		// 设置执行顺序，确保在Spring Security过滤器之前执行
		registration.setOrder(0);
		return registration;
	}
}
