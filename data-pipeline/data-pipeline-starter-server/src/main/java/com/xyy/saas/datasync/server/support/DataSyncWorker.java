//package com.xyy.saas.datasync.server.support;
//
//import org.springframework.context.SmartLifecycle;
//
///**
// * @Desc 数据同步初始化器
// * <AUTHOR>  <EMAIL>
// * @Date Created in 2021-07-21 15:43
// */
//public class DataSyncWorker implements SmartLifecycle {
//
//    @Override
//    public boolean isAutoStartup() {
//        return false;
//    }
//
//    @Override
//    public void stop(Runnable runnable) {
//
//    }
//
//    @Override
//    public void start() {
//
//    }
//
//    @Override
//    public void stop() {
//
//    }
//
//    @Override
//    public boolean isRunning() {
//        return false;
//    }
//
//    @Override
//    public int getPhase() {
//        return 0;
//    }
//}
