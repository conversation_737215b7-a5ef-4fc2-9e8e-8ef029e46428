package com.xyy.saas.datasync.server.controller;

import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import com.xyy.saas.datasync.client.protocol.CommonResult;
import com.xyy.saas.datasync.client.protocol.mqtt.MQTTPublisher;
import com.xyy.saas.datasync.client.protocol.vo.DataPullAck;
import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;
import com.xyy.saas.datasync.client.protocol.vo.DataPushVO;
import com.xyy.saas.datasync.server.support.DataSyncService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/4/30 下午4:56
 */
@RestController
@RequestMapping(value = "/datasync")
@Validated
@Slf4j
public class DataSyncLookupImplController {


	@Autowired
	private DataSyncService dataSyncService;

	@Autowired
	private MQTTPublisher mqttPublisher;

	/**
	 * 新增获取表列信息接口 (无需拦截登录)
	 *
	 * @param tableNameList
	 * @return
	 */
	@PostMapping("/getTableInfo")
	@ResponseBody
	public CommonResult<Map<String, List<TableColumn>>> getTableInfo(@RequestBody List<String> tableNameList) {
		return CommonResult.success(dataSyncService.getTableInfo(tableNameList));
	}

	@PostMapping("/checkTableUpdate")
	@ResponseBody
	public CommonResult<List<String>> checkTableUpdate(@RequestParam String tenantId, @RequestParam String clientId) {
		return CommonResult.success(mqttPublisher.getSyncTableCache(tenantId, clientId));
	}


	/**
	 * 本地拉取云端数据
	 *
	 * @param tenantId
	 * @param biz
	 * @param tableName
	 * @param baseVersion
	 * @return
	 */
	@PostMapping("/pull")
	@ResponseBody
	public CommonResult<DataPullVO> pull(@RequestParam String tenantId, @RequestParam String biz,
										 @RequestParam String tableName, @RequestParam Long baseVersion) {
		DataPullVO result = dataSyncService.pull(biz, tenantId, tableName, baseVersion);
		log.info("pull tenantId:{}, tableName:{},baseVersion:{},获取到数据:{} 条数据",
			tenantId, tableName, baseVersion, result.getData().size());
		return CommonResult.success(result);
	}

	/**
	 * 本地拉取云端数据后ack
	 *
	 * @param dataPullAck
	 * @return
	 */
	@PostMapping("/ack")
	@ResponseBody
	public CommonResult<Boolean> ack(@RequestBody DataPullAck dataPullAck) {
		if (dataPullAck == null) {
			return CommonResult.error("ACK数据为空");
		}
		// TODO ack
		log.info("ACK tenantId:{}, ids: {}", dataPullAck.getOrganId(), dataPullAck.getIds());
		return CommonResult.success(true);
	}


	/**
	 * 本地推送数据到云端
	 *
	 * @param tenantId
	 * @param dataPushVO
	 * @return 返回成功的主键列表
	 */
	@PostMapping("/push")
	@ResponseBody
	public CommonResult<List<Long>> push(@Valid @RequestParam @NotBlank String tenantId,
										 @Valid @RequestBody DataPushVO dataPushVO) {
		List<Long> result = dataSyncService.push(tenantId, dataPushVO);
		log.info("push tenantId:{}, tableName:{}, 推送数据:{} 条数据",
				 tenantId, dataPushVO.getTableName(), result == null ? 0 : result.size());
		return CommonResult.success(result);
	}


}
