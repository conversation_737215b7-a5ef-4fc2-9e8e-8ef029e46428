<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>data-pipeline</artifactId>
        <groupId>com.xyy.saas</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>data-pipeline-starter-server</artifactId>
	<version>${revision}</version>


    <dependencies>
		<dependency>
			<artifactId>spring-boot-starter-web</artifactId>
			<groupId>org.springframework.boot</groupId>
		</dependency>
		<dependency>
			<artifactId>data-pipeline-starter-client</artifactId>
			<groupId>com.xyy.saas</groupId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<artifactId>biz-soa-starter</artifactId>
			<groupId>com.xyy.saas</groupId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<artifactId>utils-starter</artifactId>
			<groupId>com.xyy.saas</groupId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>21</source>
					<target>21</target>
					<parameters>true</parameters>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
