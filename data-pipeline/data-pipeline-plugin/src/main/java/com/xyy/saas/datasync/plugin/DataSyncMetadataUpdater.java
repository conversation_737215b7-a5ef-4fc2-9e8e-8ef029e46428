package com.xyy.saas.datasync.plugin;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher.DataSyncEntityContext;
import com.xyy.saas.datasync.client.worker.DataSyncRegister;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.env.Environment;
import org.springframework.util.CollectionUtils;

/**
 * @Desc 数据同步元数据更新器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/04/03 21:08
 */

@Slf4j
public class DataSyncMetadataUpdater implements ApplicationRunner {

	private Environment environment;
//	@DubboReference
//	private DataSyncRegister dataSyncRegister;

	public DataSyncMetadataUpdater(Environment environment) {
		this.environment = environment;
	}

	/**
	 * 向SyncServer注册同步表名
	 *
	 * @param args incoming application arguments
	 * @throws Exception on error
	 */
	@Override
	public void run(ApplicationArguments args) throws Exception {
		String applicationName = environment.getProperty("spring.application.name");
		String biz = null;
		List<String> tableNames = DataSyncEntityContext.getTableNames();
		log.error(
			"DataSyncMetadataUpdater  applicationName:{} SyncServer 开始注册,biz:{}, tableNames:{}",
			applicationName, biz, JSONUtil.toJsonStr(tableNames));
		if (CollectionUtils.isEmpty(tableNames)) {
			return;
		}
		try {
			SpringUtil.getBean(DataSyncRegister.class).registerSyncTables(biz, applicationName, tableNames);
			log.error(
				"DataSyncMetadataUpdater  applicationName:{} SyncServer 开始成功,biz:{}, tableNames:{}",
				applicationName, biz, JSONUtil.toJsonStr(tableNames));
		} catch (RpcException e) {
			log.error("DataSyncRegister No Provider");
		}
	}
}
