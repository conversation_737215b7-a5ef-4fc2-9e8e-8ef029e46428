<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>data-pipeline</artifactId>
        <groupId>com.xyy.saas</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>data-pipeline-sqlite</artifactId>
	<version>${revision}</version>

    <dependencies>
        <!--<dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>utils-starter</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>data-pipeline-starter-client</artifactId>
			<version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <!-- spring-boot 自带sqlite-jdbc依赖 scope: test-->
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
			<version>********</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <artifactId>spring-boot-test</artifactId>
            <groupId>org.springframework.boot</groupId>
        </dependency>
    </dependencies>

</project>
