package com.xyy.saas.datasync.sqlite.support;

import org.springframework.boot.jdbc.DataSourceBuilder;
import org.sqlite.JDBC;

import javax.sql.DataSource;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 15:29
 */
public class SqliteFileBuilder {

    private String filePath;

    private String url;

    public static SqliteFileBuilder create(){
        return new SqliteFileBuilder();
    }

    public SqliteFileBuilder filePath(String filePath){
        this.filePath = filePath;
        return this;
    }

    public SqliteFileBuilder url(String url) {
        this.url = url;
        return this;
    }

    public DataSource build(){
        if (url != null && url != ""){
            return DataSourceBuilder.create().
                    url(url).driverClassName(JDBC.class.getName()).build();
        }
        if(filePath != null && filePath != ""){
            StringBuilder stringBuilder =  new StringBuilder();
            stringBuilder.append("jdbc:sqlite:").append(filePath);
            return DataSourceBuilder.create().
                    url(stringBuilder.toString()).driverClassName(JDBC.class.getName()).build();
        }
        return DataSourceBuilder.create().build();
    }
}
