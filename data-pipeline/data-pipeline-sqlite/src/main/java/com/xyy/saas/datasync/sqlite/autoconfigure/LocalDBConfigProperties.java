package com.xyy.saas.datasync.sqlite.autoconfigure;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/03/16 11:18
 */
@Data
@ConfigurationProperties(prefix = "localserver")
public class LocalDBConfigProperties {

    /** 禁止写入db的阈值，MB*/
    private int forbidWriteThreshold = 100;

    /** 达到提醒db可写的阈值，MB*/
    private int remindThreshold = 500;

    /** DB文件路径*/
    private String dbPath;

	/* DB名字*/
	private String dbName;

}
