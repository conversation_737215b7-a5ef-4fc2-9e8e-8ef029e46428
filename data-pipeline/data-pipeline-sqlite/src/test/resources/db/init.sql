create table pro
(
    id    INTEGER PRIMARY KEY AUTOINCREMENT,
    code  varchar(32) unique not null,     -- 编码
    value varchar(255) default '' not null -- 值
);

create table data_sync_box
(
    id             INTEGER primary key AUTOINCREMENT,
    user_id        VARCHAR                not null, -- 机构id
    table_name     VA<PERSON>HAR                not null, -- 表名
    dml_type       VARCHAR                not null, -- dml操作类型: INSERT, UPDATE, DELETE
    source_id      VARCHAR                not null, -- 源id
    data           VARCHAR                not null, -- 数据
    sync_count     INTEGER   default 0 not null,    -- 同步次数
    base_version   INTEGER   default 1 not null,    -- 版本号
    direction      INTEGER   default 0 not null,    -- 同步方向,1:云向端同步,2:端向云同步
    desc           VARCHAR                null,     -- 描述
    create_time    TIMESTAMP DEFAULT CURRENT_TIMESTAMP not null,
    last_sync_time TIMESTAMP DEFAULT NULL null
);