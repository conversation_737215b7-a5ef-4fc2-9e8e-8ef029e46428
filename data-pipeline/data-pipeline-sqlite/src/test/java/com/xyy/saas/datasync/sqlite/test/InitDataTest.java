package com.xyy.saas.datasync.sqlite.test;

import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher.DataSyncEntityContext;
import com.xyy.saas.datasync.sqlite.XyySaaSLocalServerTest;
import com.xyy.saas.datasync.client.autoconfigure.DataSyncClientAutoConfiguration;

import com.zaxxer.hikari.HikariDataSource;
import java.io.File;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashSet;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;

/**
 * @Desc 数据初始化操作
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-09-15 18:45
 */

//待整个测试类的所有测试执行结束后，该测试的application context会被关闭，同时缓存会清除。@DirtiesContext分为method-level和class-level
//@DirtiesContext，可以保证每个test class的执行上下文的独立性、隔离性，但是也会有让测试运行速度变慢的副作用
@DirtiesContext
@Slf4j
@EnableDataSyncScan({"com.xyy.saas.datasync.sqlite.entity"})
@ExtendWith(SpringExtension.class)
@XyySaaSLocalServerTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class InitDataTest {

    @Autowired
    private ServletWebServerApplicationContext context;

    @Autowired
    private DataSource dataSource;

    @BeforeEach
    public void before() {
        if (context.getBean(DataSyncClientAutoConfiguration.class) != null) {
            return;
        }
        context.registerBean(DataSyncClientAutoConfiguration.class);
    }


    /**
     * 测试DB文件是否创建
     */
    @Test
    public void initDBFile() {
        HikariDataSource HikariDataSource = (HikariDataSource) dataSource;
        String jdbcUrl = HikariDataSource.getJdbcUrl();
        Assertions.assertThat(jdbcUrl).isNotEmpty();
        String dbFilePath = jdbcUrl.replace("jdbc:sqlite:", "");
        File file = new File(dbFilePath);
        Assertions.assertThat(file).exists();
    }

    /**
     * 测试DB文件表初始化，测试新增entity列的时候，新增列,打开DB文件观察表的列是否创建
     */
    @Test
    public void initCreateDBTables() {
        Map<String, DataSyncEntityDefinition> dataSyncEntityMap = DataSyncEntityContext.getDataSyncEntityMap();
        try {
            Connection connection = dataSource.getConnection();
            Statement statement = connection.createStatement();
            for (Entry<String, DataSyncEntityDefinition> entry : dataSyncEntityMap.entrySet()) {
                DataSyncEntityDefinition definition = entry.getValue();
                String tableName = definition.getTableName();
                String detectionTableSql = "PRAGMA table_info ('" + tableName + "')";
                ResultSet resultSet = null;
                resultSet = statement.executeQuery(detectionTableSql);
                //数据库的列名
                Set<String> tableFields = new HashSet();
                while (resultSet.next()) {
                    //获取全部的列
                    String name = resultSet.getString("name");
                    tableFields.add(name);
                }
                //没有一列,说明表不存在,需要新建表
                Assertions.assertThat(tableFields).isNotEmpty();
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

}

