package com.xyy.saas.datasync.sqlite.test.mapper;

import com.xyy.saas.datasync.sqlite.test.mapper.po.RegisterRecord;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * <p>
 */
@Mapper
public interface RegisterRecordMapper {
    String primaryKey = "id";
    String tableName = "register_record";
    String columnNames = "`name`, age, email";
    String columnValues = "#{name}, #{age}, #{email}";
    String columnValuesForBatch = "#{item.name}, #{item.age}, #{item.email}";

    @Insert({"INSERT INTO ", tableName, " (", columnNames, ") VALUES (", columnValues, ")"})
    @Options(useGeneratedKeys = true, keyColumn = primaryKey, keyProperty = primaryKey)
    int insert(RegisterRecord po);

    // 多行插入 只返回最后一个主键 (sqlite驱动不支持)
    // 批量插入
    @Insert({"<script>",
            "insert into ", tableName, " (",  columnNames, ") VALUES ",
            "<foreach collection=\"pos\" index=\"index\" item=\"item\" separator=\",\">",
            " (", columnValuesForBatch, ") ",
            "</foreach>",
            "</script>"})
    @Options(useGeneratedKeys = true, keyColumn = primaryKey, keyProperty = "pos.id")
    int insertBatch(@Param("pos") List<RegisterRecord> pos);

    @Update({"UPDATE ", tableName, " SET email = #{email} where name = #{name}"})
    int updateEmailByName(RegisterRecord po);
    @Update({"UPDATE ", tableName, " SET email = #{email} where id = #{id}"})
    int updateEmailById(RegisterRecord po);

    @Update({"<script>",
        "update ", tableName, " set email = #{email} where 1=1",
        "<if test = \"ids != null and ids.size() > 0\">",
        " AND id IN ",
        "</if>",
        "<foreach collection=\"ids\" index=\"index\" item=\"id\" open=\"(\" separator=\",\" close=\")\">",
        "#{id}",
        "</foreach>",
        "</script>"})
    int updateEmailByIds(@Param("ids") List<Integer> ids, @Param("email") String email);

    @Update({"UPDATE ", tableName, " SET email = #{email}"})
    int updateAll();

    @Update({"DELETE FROM ", tableName, " WHERE name = #{name}"})
    int deleteByName(@Param("name") String name);

    @Update({"DELETE FROM ", tableName, " WHERE id = #{id}"})
    int deleteById(@Param("id") Integer id);

    @Select({"SELECT * from ", tableName, " WHERE id = #{id}"})
    RegisterRecord getById(@Param("id") Integer id);

    @Select({"<script>",
        "SELECT * from ", tableName, " WHERE id IN ",
        "<foreach collection=\"ids\" index=\"index\" item=\"id\" open=\"(\" separator=\",\" close=\")\">",
        "#{id}",
        "</foreach>",
        "</script>"})
    List<RegisterRecord> listByIds(@Param("ids") List<Integer> ids);
}
