package com.xyy.saas.datasync.client.worker;

import com.xyy.saas.datasync.client.autoconfigure.DataSyncClientProperties;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.constants.SyncState;
import com.xyy.saas.datasync.client.event.DataSyncingEvent;
import com.xyy.saas.datasync.client.worker.pull.PullSubscriberExecutor;
import com.xyy.saas.datasync.client.worker.push.PublisherExecutor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.web.context.WebServerGracefulShutdownLifecycle;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.util.StringUtils;

import java.util.concurrent.*;

/**
 * @Desc 数据同步客户端worker
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-22 15:17
 */
@Slf4j
public class DataSyncClientWorker implements SmartLifecycle {

	private DataSyncClientProperties dataSyncClientProperties;

	/* 数据pull任务 */
	@Getter
	private static DataSyncTask dataPullTask;
	/* 数据push任务 */
	@Getter
	private static DataSyncTask dataPushTask;


	/* 单线程的、循环拉取的pull执行器 */
	private PullSubscriberExecutor pullSubscriberExecutor;

	/* 单线程的、循环拉取的push执行器 */
	private PublisherExecutor publisherExecutor;

	/**
	 *
	 * @param dataSyncClientProperties 配置
	 * @param pullSubscriberExecutor pull 执行器
	 * @param publisherExecutor push 执行器
	 */
	public DataSyncClientWorker(DataSyncClientProperties dataSyncClientProperties,
								PullSubscriberExecutor pullSubscriberExecutor, PublisherExecutor publisherExecutor) {
		this.dataSyncClientProperties = dataSyncClientProperties;
		this.pullSubscriberExecutor = pullSubscriberExecutor;
		this.publisherExecutor = publisherExecutor;
		initScheduledTasks();
	}


	public void initScheduledTasks() {
		this.dataPullTask = new DataSyncTask(
			dataSyncClientProperties.getFirstPullInterval(),
			dataSyncClientProperties.getCyclePullInterval(),
			dataSyncClientProperties.getPullMaxFactorial(),
			"pull-task",
			() -> {
				this.pullSubscriberExecutor.pullInvoker();
				return true;
			}
		);
		this.dataPushTask = new DataSyncTask(
			dataSyncClientProperties.getFirstPushInterval(),
			dataSyncClientProperties.getCyclePushInterval(),
			dataSyncClientProperties.getPushMaxFactorial(),
			"push-task",
			() -> {
				this.publisherExecutor.pushInvoker();
				return true;
			}
		);
	}

	@Override
	public int getPhase() {
		return WebServerGracefulShutdownLifecycle.SMART_LIFECYCLE_PHASE - 1024;
	}

	/**
	 * 判断是否可以执行任务
	 * @return
	 */
	private static boolean unableStartTask(boolean isPull) {
		if (StringUtils.isEmpty(DataContextHolder.getTenantId())) {
			log.error("数据同步,start调度任务因同步机构号为空未开启, syncState:[{}]",
					  DataContextHolder.syncState.get());
			return true;
		}
		//TODO  查询云端是否可以上传,默认可以上传
		boolean cloudSwitch = true;
		if (!cloudSwitch) {
			return true;
		}
		log.debug("数据同步开启定时调度任务");
		if (isPull && !DataContextHolder.syncState.compareAndSet(SyncState.NONE, SyncState.STARTING)) {
			log.error("数据同步,start调度任务因同步状态错误未开启, syncState:[{}]",
					  DataContextHolder.syncState.get());
			return true;
		}
		return false;
	}

	@Order(1)
	@EventListener({ApplicationReadyEvent.class})
	public void handleInitializationAfterPulled() {
		if (StringUtils.isEmpty(DataContextHolder.getTenantId())) {
			log.error("pull数据同步,start调度任务因同步机构号为空未开启, syncState:[{}]",
					  DataContextHolder.syncState.get());
			return;
		}
		//TODO  查询云端是否可以上传,默认可以上传
		boolean cloudSwitch = true;
		if (!cloudSwitch) {
			return;
		}
		log.debug("pull数据同步开启定时调度任务");
		if (!DataContextHolder.syncState.compareAndSet(SyncState.NONE, SyncState.STARTING)) {
			log.error("pull数据同步,start调度任务因同步状态错误未开启, syncState:[{}]",
					  DataContextHolder.syncState.get());
			return;
		}
		ScheduledFuture<?> scheduledFuture = dataPullTask.start();
		try {
			scheduledFuture.get();
		} catch (InterruptedException | ExecutionException e) {
			throw new RuntimeException(e);
		}
	}


	//TODO  首次无ClientId,未Start, 需要在登录后Start
	@Override
	public void start() {
		if (StringUtils.isEmpty(DataContextHolder.getTenantId())) {
			log.error("push数据同步,start调度任务因同步机构号为空未开启, syncState:[{}]",
					  DataContextHolder.syncState.get());
			return;
		}
		//TODO  查询云端是否可以上传,默认可以上传
		boolean cloudSwitch = true;
		if (!cloudSwitch) {
			return;
		}
		log.debug("push数据同步开启定时调度任务");
		dataPushTask.start();
	}

	@Override
	public void stop() {
		dataPullTask.stop();
		DataContextHolder.syncState.set(SyncState.SHUTDOWN);
		log.error("数据同步关闭, syncState:[{}]", DataContextHolder.syncState.get());
	}

	/**
	 * 触发全部同步
	 *
	 * @param dataSyncingEvent
	 */

	@EventListener
	public void event(DataSyncingEvent dataSyncingEvent) {
		this.pullSubscriberExecutor.pullInvoker();
	}

	@Override
	public boolean isRunning() {
		return DataContextHolder.syncState.get() == SyncState.RUNNING;
	}

}
