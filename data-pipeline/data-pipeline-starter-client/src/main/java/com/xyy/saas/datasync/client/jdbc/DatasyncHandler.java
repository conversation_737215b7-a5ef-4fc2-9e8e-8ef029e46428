//package com.xyy.saas.datasync.client.jdbc;
//
//import com.xyy.saas.datasync.client.constants.DataSyncDirection;
//import com.xyy.saas.datasync.client.db.table.DataSyncPush.DataSyncDao;
//import com.xyy.saas.datasync.client.db.table.DataSyncTable;
//import com.xyy.saas.datasync.client.db.table.DataSyncTableDao;
//import com.xyy.saas.datasync.client.entity.DataSyncException;
//import com.xyy.saas.localserver.utils.JsonUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.mapping.SqlCommandType;
//
//import org.springframework.jdbc.core.JdbcTemplate;
//
//import java.util.Map;
//import java.util.List;
//import java.util.ArrayList;
//
///**
// * <AUTHOR>
// * <p>
// */
//@Slf4j
////@Component
//public class DatasyncHandler extends DatasyncDelegate {
//
//	//    @Resource
//	private DataSyncTableDao dataSyncTableDao;
//	//    @Resource
//	private DataSyncDao basicService;
//
//	public DatasyncHandler(JdbcTemplate jdbcTemplate) {
//		super();
//		basicService = new DataSyncDao(jdbcTemplate);
//	}
//
//    /**
//	 * 解析SQL语句(只解析update, delete语句)，根据Where条件内容，重新查库获取主键列表
//	 *
//	 * @return 受影响(update, delete)的主键列表
//	 */
//	@Override
//	protected Object[] execBefore(SQLDatasyncContext sqlDatasyncContext, MappedStatement ms, BoundSql boundSql) {
//		final String primaryKeyColumn = sqlDatasyncContext.primaryKeyName();
//		final String sql = sqlDatasyncContext.getConvertSql();
//
//		final SqlCommandType sqlCommandType = ms.getSqlCommandType();
//		if (sqlCommandType == SqlCommandType.DELETE || sqlCommandType == SqlCommandType.UPDATE) {
//			// 分割或者 where 条件后的语句
//			String[] sqlSegments = sql.split(" [wW][hH][eE][rR][eE] ");
//
//			if (sqlSegments.length < 2) {
//				// 不带where条件
//				throw new DataSyncException("[数据同步] SQL 语句不支持不带where条件: " + sql);
//			}
//			String where = sqlSegments[sqlSegments.length - 1];
//
//			// List一张表, Map<列名,值>
//			List<Map<String, Object>> primaryKeyValues = basicService.queryList(sqlDatasyncContext.getTable(), primaryKeyColumn, where);
//			log.debug("[数据同步] {}执行前的数据: {}", sqlDatasyncContext.getType().name(), JsonUtils.object2String(primaryKeyValues));
//			if (primaryKeyValues != null) {
//				int size = primaryKeyValues.size();
//				Object[] primaryKeys = new Object[size];
//				for (int i = 0; i < size; i++) {
//					primaryKeys[i] = primaryKeyValues.get(i).get(primaryKeyColumn);
//				}
//				return primaryKeys;
//			}
//		}
//        return new Object[0];
//	}
//
//	/**
//	 * INSERT, UPDATE 语句执行后
//	 * 再去查询需要同步的数据
//	 */
//	@Override
//	protected void execAfter(SQLDatasyncContext SQLDatasyncContext, MappedStatement ms, BoundSql boundSql) {
//		String primaryKey = SQLDatasyncContext.primaryKeyName();
//		String sql = SQLDatasyncContext.getConvertSql();
//		SqlCommandType sqlCommandType = ms.getSqlCommandType();
//
//		Object[] primaryKeys = null;
//
//		if (sqlCommandType == SqlCommandType.DELETE) {
//			primaryKeys = SQLDatasyncContext.getPrimaryKeys();
//			batchInsertDataSyncTable(SQLDatasyncContext, primaryKeys);
//			return;
//		}
//		if (sqlCommandType == SqlCommandType.INSERT) {
//            // INSERT 自增主键回写, 提取插入后回写的主键
//			final List<String> pkValueList = SQLDatasyncUtil.convertPrimaryKey(ms.getConfiguration(), boundSql, primaryKey);
//            if (pkValueList == null || pkValueList.isEmpty()) {
//                throw new DataSyncException("[数据同步] SQL 插入主键未回写: " + sql);
//            }
//            primaryKeys = pkValueList.toArray();
//        }
//        if (sqlCommandType == SqlCommandType.UPDATE) {
//            // UPDATE 在执行前记录命中记录的主键列表, 执行后查询修改后的数据
//			primaryKeys = SQLDatasyncContext.getPrimaryKeys();
//            if (primaryKeys == null || primaryKeys.length == 0) {
//                log.warn("[数据同步] SQL 未命中记录: " + sql);
//                return;
//            }
//        }
//        if (primaryKeys == null) {
//            return;
//        }
//        // 主键查询条件  id IN (...)
//		String wherePk = primaryKey + SQLDatasyncUtil.joinIn(primaryKeys);
//
//		List<Map<String, Object>> dataMap = basicService.queryList(SQLDatasyncContext.getTable(), "*", wherePk);
//        log.debug("[数据同步] {}执行后的数据: {}", sqlCommandType.name(), JsonUtils.object2String(dataMap));
//
//		batchInsertDataSyncTable(SQLDatasyncContext, dataMap);
//	}
//
//	private void batchInsertDataSyncTable(SQLDatasyncContext SQLDatasyncContext, List<Map<String, Object>> dataMap) {
//		if (dataMap == null || dataMap.isEmpty()) {
//			return;
//		}
//		List<String> sourceIdList = new ArrayList<>();
//		DataSyncDirection direction = SQLDatasyncContext.getEntityDefinition().getAnnotationAttributes().getEnum("direction");
//		String primaryKeyName = SQLDatasyncContext.primaryKeyName();
//		for (Map<String, Object> objectMap : dataMap) {
//			if (!objectMap.containsKey(primaryKeyName)) {
//				throw new DataSyncException("[数据同步] 同步数据异常, 缺少主键数据: " + primaryKeyName + ", dataMap: " + dataMap);
//			}
//			Object sourceId = objectMap.get(primaryKeyName);
//			sourceIdList.add(SQLDatasyncUtil.getParameterValue(sourceId));
//		}
//
//		DataSyncTable dataSyncTable = DataSyncTable.build(SQLDatasyncContext, direction, sourceIdList, dataMap);
//		dataSyncTableDao.insert(dataSyncTable);
//	}
//
//	private void batchInsertDataSyncTable(SQLDatasyncContext SQLDatasyncContext, Object[] primaryKeys) {
//		if (primaryKeys == null || primaryKeys.length == 0) {
//			return;
//		}
//
//		List<String> sourceIdList = new ArrayList<>();
//		DataSyncDirection direction = SQLDatasyncContext.getEntityDefinition().getAnnotationAttributes().getEnum("direction");
//		for (Object primaryKey : primaryKeys) {
//			sourceIdList.add(SQLDatasyncUtil.getParameterValue(primaryKey));
//		}
//		DataSyncTable dataSyncTable = DataSyncTable.build(SQLDatasyncContext, direction, sourceIdList);
//        dataSyncTableDao.insert(dataSyncTable);
//    }
//}
