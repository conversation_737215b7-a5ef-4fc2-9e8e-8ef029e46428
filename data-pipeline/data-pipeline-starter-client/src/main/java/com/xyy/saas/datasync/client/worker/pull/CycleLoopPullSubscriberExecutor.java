package com.xyy.saas.datasync.client.worker.pull;

import cn.hutool.extra.spring.SpringUtil;
import com.xyy.saas.datasync.client.autoconfigure.DataSyncClientProperties;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.db.DataSyncPersistencer;
import com.xyy.saas.datasync.client.db.DataTableDmlTransformer;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher;
import com.xyy.saas.datasync.client.event.PullDataFinishedEvent;
import com.xyy.saas.datasync.client.event.PullDataStartedEvent;
import com.xyy.saas.datasync.client.protocol.DataSubscriber;
import com.xyy.saas.datasync.client.protocol.vo.DataPullAck;
import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;
import com.xyy.saas.localserver.utils.EventPusher;
import com.xyy.saas.localserver.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/14 下午4:12
 */
@Slf4j
public class CycleLoopPullSubscriberExecutor implements PullSubscriberExecutor {

	private DataSyncClientProperties dataSyncClientProperties;

	/* 远程调用数据订阅器 */
	private DataSubscriber dataSubscriber;

	/* 数据持久化器 */
	private DataSyncPersistencer dataSyncPersistencer;

	public CycleLoopPullSubscriberExecutor(DataSyncClientProperties dataSyncClientProperties,
										   DataSubscriber dataSubscriber,
										   DataSyncPersistencer dataSyncPersistencer) {
		this.dataSyncClientProperties = dataSyncClientProperties;
		this.dataSubscriber = dataSubscriber;
		this.dataSyncPersistencer = dataSyncPersistencer;
	}

	/**
	 * 拉取全部表的同步数据
	 */
	@Override
	public Future<?> pullInvoker() {
		cycleLoopPullInvoker();
		return null;
	}

	/**
	 * @param tableName
	 */
	@Override
	public Future<?> pullInvoker(String tableName) {
		int count = 0;
		int items = 0;
		log.debug("数据同步,机构号:[{}],开始同步表名:[{}]", DataContextHolder.getTenantId(), tableName);
		while (count++ < dataSyncClientProperties.getPullCount()) {
			log.debug("数据同步,开始从云端pull数据, 机构号:[{}], 表名:[{}], pull次数:[{}]",
				DataContextHolder.getTenantId(), tableName, count);
			DataPullVO dataPullVO = dataSubscriber.pull(DataContextHolder.getTenantId(), tableName);
			if (dataPullVO == null) {
				log.error("数据同步,从云端pull数据失败, 机构号:[{}], 表名:[{}], pull次数:[{}], 总计条数:[{}]",
					DataContextHolder.getTenantId(), tableName, count, items);
				//TODO 数据同步失败报警
				break;
			}
			List<Map<String, Object>> pullRecordList = dataPullVO.getData();
			if (CollectionUtils.isEmpty(pullRecordList)) {
				log.debug("数据同步,从云端pull数据结束, 机构号:[{}], 表名:[{}], pull次数:[{}], 总计条数:[{}]",
					DataContextHolder.getTenantId(), tableName, count, items);
				break;
			}
			items = items + pullRecordList.size();
			log.debug("数据同步,从云端pull数据, 机构号:[{}], 表名:[{}], pull次数:[{}], 总计条数:[{}], 当前条数:[{}], data:[{}]",
				DataContextHolder.getTenantId(), tableName, count, items, pullRecordList.size(), JsonUtils.object2String(pullRecordList));

			List<Long> ids = dataSyncPersistencer.pullToPersistence(tableName, pullRecordList,
				DataSyncEntityDispatcher.DataSyncEntityContext.getTableEntityMap().get(tableName).getClazz());
			log.debug("数据同步,持久化到本地, 机构号:[{}], 表名:[{}], pull次数:[{}], 总计条数:[{}], 当前条数:[{}], 持久化ids:[{}]",
				DataContextHolder.getTenantId(), tableName, count, items, pullRecordList.size(), JsonUtils.object2String(ids));

			dataSubscriber.confirm(DataPullAck.builder().organId(DataContextHolder.getTenantId()).ids(ids).build());
			log.debug("数据同步,向云端确认同步, 机构号:[{}], 表名:[{}], pull次数:[{}], 总计条数:[{}], 当前条数:[{}], 持久化ids:[{}]",
				DataContextHolder.getTenantId(), tableName, count, items, pullRecordList.size(), JsonUtils.object2String(ids));

		}
		Long maxBaseVersion = SpringUtil.getBean(DataTableDmlTransformer.class)
			.getMaxBaseVersion(tableName);
		log.debug("数据同步,表数据同步完成, 机构号:[{}], 表名:[{}], pull次数:[{}], 总计条数:[{}], 最大base版本:[{}]", DataContextHolder.getTenantId(), tableName, count, items, maxBaseVersion);
		return null;
	}

	/**
	 * 循环同步数据，直到云端的数据同步拉取完结为止
	 */
	private void cycleLoopPullInvoker() {
		EventPusher.syncPost(PullDataStartedEvent.builder().source(this).build());
		List<String> tableNames = DataSyncEntityDispatcher.DataSyncEntityContext.getTableNames();
		int tableCount = tableNames.size();
		log.info("数据同步,开始从云端pull数据,待同步表数量:[{}], 机构号:[{}]", tableCount, DataContextHolder.getTenantId());
		for (int i = 0; i < tableCount; i++) {
			String tableName = tableNames.get(i);
			pullInvoker(tableName);
			if (i == tableCount - 1) {
				log.info("数据同步,全部表数据同步完成, 机构号:[{}], 最后一个表名:[{}], 总同步表次数:[{}]", DataContextHolder.getTenantId(), tableName, i + 1);
				break;
			}
		}
		EventPusher.syncPost(PullDataFinishedEvent.builder().source(this).build());
	}
}
