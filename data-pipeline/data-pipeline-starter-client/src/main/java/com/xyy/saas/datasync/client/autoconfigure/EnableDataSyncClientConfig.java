package com.xyy.saas.datasync.client.autoconfigure;

import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * @Desc 激活数据同步客户端配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-21 17:54
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
@Import(DataSyncClientAutoConfigurationSelector.class)
public @interface EnableDataSyncClientConfig {

}
