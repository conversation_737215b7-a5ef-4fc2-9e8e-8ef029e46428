//package com.xyy.saas.datasync.client.jdbc;
//
//import com.xyy.saas.datasync.client.constants.DmlType;
//import lombok.experimental.UtilityClass;
//import lombok.extern.slf4j.Slf4j;
//import net.sf.jsqlparser.JSQLParserException;
//import net.sf.jsqlparser.parser.CCJSqlParserUtil;
//import net.sf.jsqlparser.statement.Statement;
//import net.sf.jsqlparser.statement.delete.Delete;
//import net.sf.jsqlparser.statement.insert.Insert;
//import net.sf.jsqlparser.statement.select.Select;
//import net.sf.jsqlparser.statement.update.Update;
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.mapping.ParameterMapping;
//import org.apache.ibatis.reflection.MetaObject;
//import org.apache.ibatis.session.Configuration;
//import org.apache.ibatis.type.TypeHandlerRegistry;
//
//import java.text.DateFormat;
//import java.util.*;
//
///**
// * <AUTHOR>
// */
//@Slf4j
//@UtilityClass
//public class SQLDatasyncUtil {
//
////	/**
////	 * sql 表名前的关键字
////	 */
////	final List<String> SQL_PREFIX_SET = Arrays.asList("INSERT", "INTO", "UPDATE", "DELETE", "FROM");
//	/**
//	 * 左括号
//	 */
//	final String LEFT_BRACKET = "(";
//	/**
//	 * mysql转义引号 select `where`;
//	 */
//	final String ESCAPE_QUOTE = "`";
//	/**
//	 * mysql单引号
//	 */
//	final String SINGLE_QUOTE = "'";
//	/**
//	 * mysql逗号
//	 */
//	final String COMMA = ",";
//	/**
//	 * mysql null
//	 */
//	final String NULL = "null";
//	/**
//	 * 空格匹配
//	 */
//	final String BLANK_SPACE = "[\\s]+";
//	/**
//	 * ? 占位符匹配
//	 */
//	final String PLACEHOLDER = "\\?";
//
//	/**
//	 * sql为 增删改语句 单表不带别名
//	 * eg: insert into 表名(...) ...
//	 * update 表名 set ...
//	 * delete from 表名 where ...
//	 *
//	 */
//	public SQLDatasyncContext getDatasyncContext(String sql) {
//		// 拆分sql词组
////        String[] strings = sql.trim().split(" ");
////
////        String tableName = "";
////		DmlType type = null;
////        for (String s : strings) {
////            // 跳过表名之前的关键词
////            if (s.length() == 0 || SQL_PREFIX_SET.contains(s.toUpperCase())) {
////                continue;
////            }
////            type = DmlType.getDml(strings[0].toUpperCase());
////            // insert语句表名后可能会接 (字段列表)
////            if (s.contains(LEFT_BRACKET)) {
////                s = s.substring(0, s.indexOf(LEFT_BRACKET));
////            }
////			tableName = s.replaceAll(ESCAPE_QUOTE, "");
////            break;
////        }
//		String tableName = "";
//		DmlType type = null;
//		try {
//			Statement statement = CCJSqlParserUtil.parse(sql);
//			if (statement instanceof Select) {
//				tableName = parseSelectTableName((Select) statement);
//				type = DmlType.SELECT;
//			} else if (statement instanceof Insert) {
//				tableName = parseInsertTableName((Insert) statement);
//				type = DmlType.INSERT;
//			} else if (statement instanceof Update) {
//				tableName = parseUpdateTableName((Update) statement);
//				type = DmlType.UPDATE;
//			} else if (statement instanceof Delete) {
//				tableName = parseDeleteTableName((Delete) statement);
//				type = DmlType.DELETE;
//			}
//		} catch (JSQLParserException jsqlParserException) {
//			log.error("数据同步解析SQL:{}失败", sql);
//			return null;
//		}
//		return new SQLDatasyncContext(type, tableName, sql);
//	}
////
////	public static String parseTableNameFromSql(String sql) throws JSQLParserException {
////		Statement statement = CCJSqlParserUtil.parse(sql);
////
////		if (statement instanceof Select) {
////			return parseSelectTableName((Select) statement);
////		} else if (statement instanceof Insert) {
////			return parseInsertTableName((Insert) statement);
////		} else if (statement instanceof Update) {
////			return parseUpdateTableName((Update) statement);
////		} else if (statement instanceof Delete) {
////			return parseDeleteTableName((Delete) statement);
////		}
////
////		return null;
////	}
//
//	private static String parseSelectTableName(Select select) {
//		return parseTableNameFromTable(select.getSelectBody().toString());
//	}
//
//	private static String parseInsertTableName(Insert insert) {
//		return insert.getTable().getName();
//	}
//
//	private static String parseUpdateTableName(Update update) {
//		return parseTableNameFromTable(update.getTable().toString());
//	}
//
//	private static String parseDeleteTableName(Delete delete) {
//		return parseTableNameFromTable(delete.getTable().toString());
//	}
//
//	private static String parseTableNameFromTable(String tableString) {
//		return tableString.replaceAll("[\"`]", ""); // 去除引号，如果有的话
//	}
//
//	/**
//	 * sql 替换占位符
//	 *
//	 * @param configuration
//	 * @param boundSql
//	 * @return
//	 */
//	public String convertSql(Configuration configuration, BoundSql boundSql) {
//		Object parameterObject = boundSql.getParameterObject();
//		List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
//		//替换空格、换行、tab缩进等
//		String sql = boundSql.getSql().replaceAll(BLANK_SPACE, " ");
//		if (parameterMappings.size() > 0 && parameterObject != null) {
//			TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
//			if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
//				sql = sql.replaceFirst(PLACEHOLDER, getParameterValue(parameterObject));
//			} else {
//				MetaObject metaObject = configuration.newMetaObject(parameterObject);
//				for (ParameterMapping parameterMapping : parameterMappings) {
//					String propertyName = parameterMapping.getProperty();
//					if (metaObject.hasGetter(propertyName)) {
//						Object obj = metaObject.getValue(propertyName);
//						sql = sql.replaceFirst(PLACEHOLDER, getParameterValue(obj));
//					} else if (boundSql.hasAdditionalParameter(propertyName)) {
//						Object obj = boundSql.getAdditionalParameter(propertyName);
//						sql = sql.replaceFirst(PLACEHOLDER, getParameterValue(obj));
//					}
//				}
//			}
//		}
//		return sql;
//	}
//
//	/**
//	 * 注意INSERT 如果是批量插入, 第一个参数必须保证是插入实体类的集合
//	 *
//	 * @param configuration
//	 * @param boundSql
//	 * @param pkColumn
//	 * @return
//	 */
//	public List<String> convertPrimaryKey(Configuration configuration, BoundSql boundSql, String pkColumn) {
//		List<String> pkValueList = new ArrayList<>();
//
//		Object parameterObject = boundSql.getParameterObject();
//		final List<String> primaryKeyList = getPrimaryKey(configuration, parameterObject, pkColumn);
//		if (!primaryKeyList.isEmpty()) {
//			return primaryKeyList;
//		}
//		MetaObject metaObject = configuration.newMetaObject(parameterObject);
//		if (boundSql.hasAdditionalParameter(pkColumn)) {
//			Object obj = boundSql.getAdditionalParameter(pkColumn);
//			pkValueList.add(getParameterValue(obj));
//		} else if (metaObject.hasGetter("param1")) {
//			//批量的insertBatch的时候自动插入param1
//			Object obj = metaObject.getValue("param1");
//			if (obj instanceof Collection) {
//				Collection<Object> list = (Collection<Object>) obj;
//				for (Object o : list) {
//					final List<String> li = getPrimaryKey(configuration, o, pkColumn);
//					pkValueList.addAll(li);
//				}
//			}
//		}
//		return pkValueList;
//	}
//
//	private List<String> getPrimaryKey(Configuration configuration, Object parameterObject, String pkColumn) {
//		List<String> pkValueList = new ArrayList<>();
//		TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
//
//		if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
//			pkValueList.add(getParameterValue(parameterObject));
//			return pkValueList;
//		}
//		MetaObject metaObject = configuration.newMetaObject(parameterObject);
//		if (metaObject.hasGetter(pkColumn)) {
//			Object obj = metaObject.getValue(pkColumn);
//			pkValueList.add(getParameterValue(obj));
//		}
//		return pkValueList;
//	}
//
//	/**
//	 * 对象值按照类型 转为sql值
//	 *
//	 * @param obj
//	 * @return
//	 */
//	public String getParameterValue(Object obj) {
//		return parseValue(obj).replace("$", "\\$");
//	}
//
//	private String parseValue(Object obj) {
//		if (obj == null) {
//			return NULL;
//		}
//		if (obj instanceof Number) {
//			return obj.toString();
//		}
//		String value = obj.toString();
//		if (obj instanceof Date) {
//			DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
//			value = formatter.format((Date) obj);
//		}
//		return SINGLE_QUOTE + value + SINGLE_QUOTE;
//	}
//
//	public String joinIn(Object[] arr) {
//		if (arr.length == 0) {
//			return "";
//		}
//		StringBuilder builder = new StringBuilder();
//		for (Object s : arr) {
//			if (s instanceof Number) {
//				builder.append(s).append(COMMA);
//			} else {
//				builder.append(SINGLE_QUOTE).append(s).append(SINGLE_QUOTE).append(COMMA);
//			}
//		}
//		return " IN (" + builder.substring(0, builder.lastIndexOf(COMMA)) + ")";
//	}
//
//	void logSql(String sql, String sqlId, long cost) {
//		StringBuilder sb = new StringBuilder()
//			.append("Time：").append(cost)
//			.append(" ms - ID：").append(sqlId)
//			.append("  Execute SQL：")
//			.append(sql);
//		log.debug(sb.toString());
//	}
//}
