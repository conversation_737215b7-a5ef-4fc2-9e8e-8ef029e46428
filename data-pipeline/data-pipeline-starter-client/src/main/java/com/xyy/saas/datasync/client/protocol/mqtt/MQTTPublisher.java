package com.xyy.saas.datasync.client.protocol.mqtt;

import java.util.List;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/13 下午1:19
 */
public interface MQTTPublisher {

	/* 广播 topic ,推送给所有设备 */
	String BROADCAST_TOPIC = "saas-broadcast";

	/* 数据同步 topic ,推送给所有设备 */
	String DATASYNC_TOPIC = "saas-datasync";

	/**
	 * 发布消息推送给租户下所有设备
	 *
	 * @param tenantId
	 * @param tableName
	 */
	void publish(String tenantId, String tableName);

	/**
	 * 发布消息推送给租户下指定设备
	 *
	 * @param tenantId
	 * @param clientId
	 * @param topic
	 * @param tableName
	 */
	void publishDevice(String tenantId, String clientId, String topic, String tableName);

	/**
	 * 广播消息
	 *
	 * @param topic
	 * @param data
	 */
	void broadcast(String topic, String data);

	List<String> getSyncTableCache(String tenantId, String clientId);
}
