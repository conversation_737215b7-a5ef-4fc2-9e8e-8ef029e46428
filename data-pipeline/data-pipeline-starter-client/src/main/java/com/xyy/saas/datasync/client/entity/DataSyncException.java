package com.xyy.saas.datasync.client.entity;


/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-04 11:48
 */
public class DataSyncException extends RuntimeException{

    private String datasyncEntity;

    public DataSyncException(String resourceDescription, String datasyncEntity, String msg, Throwable cause) {
        super("Invalid Datasync definition with name '" + datasyncEntity + "' defined in " + resourceDescription + ": " + msg,
                cause);
        this.datasyncEntity = datasyncEntity;
    }

    public DataSyncException(String message) {
        super(message);
    }

    public DataSyncException(String message, Throwable cause) {
        super(message, cause);
    }

    public String getDatasyncEntity() {
        return datasyncEntity;
    }
}
