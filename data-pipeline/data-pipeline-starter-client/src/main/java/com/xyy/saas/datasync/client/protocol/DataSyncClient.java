package com.xyy.saas.datasync.client.protocol;

import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import com.xyy.saas.datasync.client.protocol.vo.DataPullAck;
import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;
import com.xyy.saas.datasync.client.protocol.vo.DataPushVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.List;
import java.util.Map;

/**
 * @Desc 数据同步http客户端
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/03/31 18:42
 */

@HttpExchange(accept = "application/json", contentType = "application/json")
public interface DataSyncClient {

	@PostExchange("datasync/getTableInfo")
	CommonResult<Map<String, List<TableColumn>>> getTableInfo(@RequestBody List<String> tableNameList);

	@PostExchange("datasync/checkTableUpdate")
	CommonResult<List<String>> checkTableUpdate(@RequestParam String tenantId, @RequestParam String clientId);

	@PostExchange("datasync/pull")
	CommonResult<DataPullVO> pull(@RequestParam("tenantId") String tenantId, @RequestParam String biz, @RequestParam String tableName, @RequestParam Long baseVersion);

	@PostExchange("datasync/ack")
	CommonResult<Boolean> ack(@RequestBody DataPullAck dataPullAck);

	@PostExchange("datasync/push")
	CommonResult<List<Long>> push(@RequestParam String tenantId, @RequestBody DataPushVO dataPushVO);

}
