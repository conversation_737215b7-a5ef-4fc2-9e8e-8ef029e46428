package com.xyy.saas.datasync.client.db.table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * @Desc 数据同步表
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-21 16:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataSyncPush {

	private Long id;

	/* 机构id */
	private String tenantId;

	/* 表名 */
	private String tableName;

	/* 数据库 dml操作类型*/
	private String dmlType;

	/* 源id */
	private Long sourceId;

	/* 创建时间 */
	@Builder.Default
	private LocalDateTime createTime = LocalDateTime.now();

	/* 最新同步时间 */
	private LocalDateTime lastSyncTime;

	/* 同步次数,拉取一次同步就记录一次 */
	@Builder.Default
	private Integer syncCount = 0;


	/* 数据 */
	private Map<String, Object> data;
}
