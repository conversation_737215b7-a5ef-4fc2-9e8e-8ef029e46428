//package com.xyy.saas.datasync.client.jdbc;
//
//
//import net.sf.jsqlparser.JSQLParserException;
//import net.sf.jsqlparser.parser.CCJSqlParserUtil;
//import net.sf.jsqlparser.schema.Table;
//import net.sf.jsqlparser.statement.Statement;
//import net.sf.jsqlparser.statement.delete.Delete;
//import net.sf.jsqlparser.statement.insert.Insert;
//import net.sf.jsqlparser.statement.select.Select;
//import net.sf.jsqlparser.statement.update.Update;
//
//import java.util.HashSet;
//import java.util.Set;
//
///**
// * @Desc
// * <AUTHOR>  <EMAIL>
// * @Date Created in 2023/07/19 15:18
// */
//public class SQLParserUtil {
//
//	private static final Set<String> SQL_PREFIX_SET = new HashSet<>();
//
//	static {
//		// 定义常见的 SQL 命令关键词
//		SQL_PREFIX_SET.add("SELECT");
//		SQL_PREFIX_SET.add("INSERT");
//		SQL_PREFIX_SET.add("UPDATE");
//		SQL_PREFIX_SET.add("DELETE");
//	}
//
//	public static String parseTableNameFromSql(String sql) throws JSQLParserException {
//		Statement statement = CCJSqlParserUtil.parse(sql);
//
//		if (statement instanceof Select) {
//			return parseSelectTableName((Select) statement);
//		} else if (statement instanceof Insert) {
//			return parseInsertTableName((Insert) statement);
//		} else if (statement instanceof Update) {
//			return parseUpdateTableName((Update) statement);
//		} else if (statement instanceof Delete) {
//			return parseDeleteTableName((Delete) statement);
//		}
//
//		return null;
//	}
//
//	private static String parseSelectTableName(Select select) {
//		return parseTableNameFromTable(select.getSelectBody().toString());
//	}
//
//	private static String parseInsertTableName(Insert insert) {
//		return insert.getTable().getName();
//	}
//
//	private static String parseUpdateTableName(Update update) {
//		return parseTableNameFromTable(update.getTable().toString());
//	}
//
//	private static String parseDeleteTableName(Delete delete) {
//		return parseTableNameFromTable(delete.getTable().toString());
//	}
//
//	private static String parseTableNameFromTable(String tableString) {
//		return tableString.replaceAll("[\"`]", ""); // 去除引号，如果有的话
//	}
//}
//
