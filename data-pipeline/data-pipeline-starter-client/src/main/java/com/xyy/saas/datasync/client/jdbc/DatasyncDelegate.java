//package com.xyy.saas.datasync.client.jdbc;
//
//import com.xyy.saas.datasync.client.constants.DmlType;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.plugin.Invocation;
//
//import java.lang.reflect.InvocationTargetException;
//
///**
// * <AUTHOR>
// * <p>
// */
//@Slf4j
//public abstract class DatasyncDelegate {
//
//    /**
//	 * 有性能损耗 (涉及到1-2次事务内查询)
//	 * 注意: sqlite 批量insert获取id有问题
//	 *
//	 * @param sqlDatasyncContext
//	 * @param ms
//	 * @return
//	 * @throws InvocationTargetException
//	 * @throws IllegalAccessException
//	 */
//	public final Object exec(SQLDatasyncContext sqlDatasyncContext, Invocation invocation, MappedStatement ms, BoundSql boundSql) throws InvocationTargetException, IllegalAccessException {
//		long start = System.currentTimeMillis();
//
//		// sql替换占位符·······
//		String convertSql = SQLDatasyncUtil.convertSql(ms.getConfiguration(), boundSql);
//		sqlDatasyncContext.setConvertSql(convertSql);
//
//		// 执行前，查询主键列表
//		Object[] primaryKeys = execBefore(sqlDatasyncContext, ms, boundSql);
//		sqlDatasyncContext.setPrimaryKeys(primaryKeys);
//		//修改执行的sql,在update语句中添加base_version字段的值
//
//		if (sqlDatasyncContext.getType() == DmlType.UPDATE) {
////			String baseVersionSql = SQLDatasyncUtil.getBaseVersionSql(ms.getConfiguration(), boundSql);
////			sqlDatasyncContext.setBaseVersionSql(baseVersionSql);
//
//
//		}
//		//TODO
//
//		// 执行sql
//		long begin = System.currentTimeMillis();
//		Object result = invocation.proceed();
//		SQLDatasyncUtil.logSql(convertSql, ms.getId(), System.currentTimeMillis() - begin);
//
//		// 执行后
//		execAfter(sqlDatasyncContext, ms, boundSql);
//		long cost = System.currentTimeMillis() - start;
//		if (cost > 30000) {
//            log.error("[数据同步] 总耗时: {} ms", cost);
//        } else {
//            log.debug("[数据同步] 总耗时: {} ms", cost);
//        }
//        return result;
//    }
//
//	/**
//	 * UPDATE, DELETE 语句执行前, 获取受影响的主键列表
//	 * 受影响(update, delete)的主键列表
//	 *
//	 * @return 主键值列表
//	 */
//	protected abstract Object[] execBefore(SQLDatasyncContext SQLDatasyncContext, MappedStatement ms, BoundSql boundSql);
//
//	/**
//	 * INSERT, UPDATE, DELETE 语句执行后
//	 */
//	protected abstract void execAfter(SQLDatasyncContext SQLDatasyncContext, MappedStatement ms, BoundSql boundSql);
//
//}
