package com.xyy.saas.datasync.client.serialize;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class MapLocalDateTimeSerializer extends StdSerializer<Map<?, ?>> {
	private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

	public MapLocalDateTimeSerializer() {
		this(null);
	}

	protected MapLocalDateTimeSerializer(Class<Map<?, ?>> t) {
		super(t);
	}

	@Override
	public void serialize(Map<?,?> map, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
		jsonGenerator.writeStartObject();
		for (Map.Entry<?, ?> entry : map.entrySet()) {
			Object key = entry.getKey();
			Object value = entry.getValue();

			if (value instanceof LocalDateTime) {
				jsonGenerator.writeObjectField(key.toString(), ((LocalDateTime) value).format(FORMATTER));
			} else {
				// Serialize non-LocalDateTime values using Jackson's default mechanism
				jsonGenerator.writeObjectField(key.toString(), value);
			}
		}
		jsonGenerator.writeEndObject();
	}
}
