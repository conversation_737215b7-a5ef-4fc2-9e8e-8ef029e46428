package com.xyy.saas.datasync.client.protocol;

import com.xyy.saas.datasync.client.protocol.vo.DataPullAck;
import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;

import java.util.List;

/**
 * @Desc 数据订阅者
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-26 17:41
 */
public interface DataSubscriber {

	/**
	 * 向云端拉取数据
	 *
	 * @param tenantId 机构号
	 * @param tableName 表名
	 */
	DataPullVO pull(String tenantId, String tableName);


	/**
	 * @param tenantId
	 * @param tableName
	 * @return
	 */
	DataPullVO pull(String tenantId, String tableName, Long baseVersion);


	/**
	 * 向云端确认数据拉取成功
	 *
	 * @param dataPullAck
	 */
	boolean confirm(DataPullAck dataPullAck);
}
