package com.xyy.saas.datasync.client.worker.pull;

import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.worker.DataSyncClientWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/14 下午4:11
 */
@Slf4j
public class SingleThreadPullSubscriberExecutor implements PullSubscriberExecutor {

	/**
	 * 单线程拉取任务
	 */
	private final static ExecutorService EXECUTOR_SERVICE = Executors.newSingleThreadExecutor((r) -> {    // 创建单线程的线程池);
		Thread thread = new Thread(r);
		thread.setName("single-thread-pull-subscriber-executor");
		return thread;
	});

	private PullSubscriberExecutor pullDecorator;

	public SingleThreadPullSubscriberExecutor(PullSubscriberExecutor pullDecorator) {
		this.pullDecorator = pullDecorator;
	}

	@Override
	public Future<?> pullInvoker() {
		return EXECUTOR_SERVICE.submit(() -> {
			StopWatch stopWatch = new StopWatch();
			stopWatch.start();
			log.info("数据同步,单线程拉取任务开始, 机构号:[{}].", DataContextHolder.getTenantId());
			pullDecorator.pullInvoker();
			stopWatch.stop();
			DataSyncClientWorker.getDataPullTask().getCountDownLatch().countDown();
			log.info("数据同步,单线程拉取任务结束,释放信号量, 机构号:[{}], 耗时:[{}]ms.", DataContextHolder.getTenantId(), stopWatch.getTotalTimeMillis());
		});
	}

	/**
	 * @param tableName 同步的表名
	 */
	@Override
	public Future<?> pullInvoker(String tableName) {
		return EXECUTOR_SERVICE.submit(() -> {
			StopWatch stopWatch = new StopWatch();
			stopWatch.start();
			log.info("数据同步,单线程拉取任务开始, 机构号:[{}]", DataContextHolder.getTenantId());
			pullDecorator.pullInvoker(tableName);
			stopWatch.stop();
//				DataSyncClientWorker.getDataPullTask().getCountDownLatch().countDown();
			log.info("数据同步,单线程拉取任务结束,释放信号量, 机构号:[{}], 耗时:[{}]ms", DataContextHolder.getTenantId(), stopWatch.getTotalTimeMillis());
		});
	}
}
