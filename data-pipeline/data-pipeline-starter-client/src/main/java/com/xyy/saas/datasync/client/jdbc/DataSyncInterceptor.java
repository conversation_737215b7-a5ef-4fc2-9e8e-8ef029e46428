package com.xyy.saas.datasync.client.jdbc;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.MybatisParameterHandler;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.parser.JsqlParserSupport;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.google.common.collect.Lists;
import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.datasync.client.db.DataTableDmlTransformer;
import com.xyy.saas.datasync.client.db.table.DataSyncPushDao;
import com.xyy.saas.datasync.client.db.table.DataSyncPush;
import com.xyy.saas.datasync.client.protocol.mqtt.MQTTPublisher;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.worker.push.PublisherExecutor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.ExpressionVisitor;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.update.Update;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.select.Values;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.util.StringUtils;

import java.sql.Connection;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;

/**
 * @Desc BaseVersion自动新增器
 */
@Slf4j
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class DataSyncInterceptor extends JsqlParserSupport implements InnerInterceptor, Interceptor {

	private final DataTableDmlTransformer dataTableDmlTransformer;

	private ObjectProvider<PublisherExecutor> publisherExecutorProvider;

//	private final DataSyncPushDao dataSyncDao;

	private ObjectProvider<DataSyncPushDao> dataSyncDaoProvider;

	public DataSyncInterceptor(DataTableDmlTransformer dataTableDmlTransformer, ObjectProvider<DataSyncPushDao> dataSyncDaoProvider, ObjectProvider<PublisherExecutor> publisherExecutorProvider) {
		this.dataTableDmlTransformer = dataTableDmlTransformer;
		this.dataSyncDaoProvider = dataSyncDaoProvider;
		this.publisherExecutorProvider = publisherExecutorProvider;
	}

	@Override
	public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
		PluginUtils.MPStatementHandler mpSh = PluginUtils.mpStatementHandler(sh);
//		MappedStatement mappedStatement = mpSh.mappedStatement();
		BoundSql boundSql = mpSh.boundSql();
		String originalSql = boundSql.getSql();
		try {
			Statement statement = CCJSqlParserUtil.parse(originalSql);
			String newSql = originalSql;
			if (statement instanceof Insert) {
				newSql = processInsert((Insert) statement);
			} else if (statement instanceof Update) {
				newSql = processUpdate((Update) statement);
			}
			MetaObject metaObject = SystemMetaObject.forObject(boundSql);
			metaObject.setValue("sql", newSql);
		} catch (Exception e) {
			log.error("Error parsing SQL statement {} : {}", originalSql, e.getMessage(), e);
		}
	}

	/**
	 * @param properties 属性
	 */
	@Override
	public void setProperties(Properties properties) {
		InnerInterceptor.super.setProperties(properties);
	}

	@Override
	public boolean willDoUpdate(Executor executor, MappedStatement ms, Object parameter) throws SQLException {
		return InnerInterceptor.super.willDoUpdate(executor, ms, parameter);
	}

	private String processInsert(Insert insert) {
//		// 添加 base_version 列
//		insert.getColumns().add(new net.sf.jsqlparser.schema.Column("base_version"));
//
//		String tableName = insert.getTable().getName();
//		Long maxBaseVersion = dataTableDmlTransformer.getMaxBaseVersion(tableName);
//		// 添加 base_version 对应的值
//		Expression baseVersionValue = new LongValue(maxBaseVersion + 1);  // 示例中直接使用 1，实际应用中应获取当前版本
//		if (insert.getItemsList() instanceof net.sf.jsqlparser.expression.operators.relational.ExpressionList) {
//			((net.sf.jsqlparser.expression.operators.relational.ExpressionList) insert.getItemsList()).getExpressions().add(baseVersionValue);
//		} else if (insert.getItemsList() instanceof net.sf.jsqlparser.expression.operators.relational.MultiExpressionList) {
//			((net.sf.jsqlparser.expression.operators.relational.MultiExpressionList) insert.getItemsList()).getExpressionLists()
//				.forEach(el -> el.getExpressions().add(baseVersionValue));
//		}


		insert.addColumns(new Column("base_version"));  // 使用已有的addColumns方法，更简洁且避免直接修改内部状态

		String tableName = insert.getTable().getName();
		Long maxBaseVersion = dataTableDmlTransformer.getMaxBaseVersion(tableName);

		// 添加 base_version 对应的值
		Expression baseVersionValue = new LongValue(maxBaseVersion + 1);

		// 使用更安全的类型转换和处理逻辑
		if (insert.getSelect() instanceof Values) {
			Values values = (Values) insert.getSelect();
			ExpressionList<?> expressionList = values.getExpressions();
			List<Expression> expressionsToAdd = Lists.newArrayList(baseVersionValue);
			((ExpressionList<Expression>) expressionList).addAll(expressionsToAdd);
//			expressionList.add(baseVersionValue);  // 直接向表达式列表中添加新的表达式
		} else if (insert.getSelect() instanceof Select) {
			throw new UnsupportedOperationException("Complex SELECT not supported in INSERT statement.");
		} else {
			// 如果没有SELECT子句，则可能是VALUES列表
			if (insert.getValues() != null) {
				insert.getValues().addExpressions(baseVersionValue);
			}
		}

		return insert.toString();
	}

	private String processUpdate(Update update) {
		// 添加 base_version = maxBaseVersion + 1
		update.getExpressions().add(new net.sf.jsqlparser.expression.BinaryExpression() {


			{
				setLeftExpression(new net.sf.jsqlparser.schema.Column("base_version"));
				String tableName = update.getTable().getName();
				Long maxBaseVersion = dataTableDmlTransformer.getMaxBaseVersion(tableName);
				setRightExpression(new LongValue(maxBaseVersion + 1));
			}

			@Override
			public String getStringExpression() {
				return "=";
			}

			@Override
			public <T, S> T accept(ExpressionVisitor<T> expressionVisitor, S context) {
//				expressionVisitor.visit(this);
				return null;
			}

//			@Override
//			public void accept(ExpressionVisitor expressionVisitor) {
////				expressionVisitor.visit(this);
//			}
		});

		return update.toString();
	}


	/**
	 * @param invocation
	 * @return
	 * @throws Throwable
	 */
	@Override
	public Object intercept(Invocation invocation) throws Throwable {
		return invocation.proceed();
	}

	/**
	 * @param target
	 * @return
	 */
	@Override
	public Object plugin(Object target) {
		if (target instanceof MybatisParameterHandler) {
			MybatisParameterHandler mybatisParameterHandler = (MybatisParameterHandler) target;

			SqlCommandType sqlCommandType = (SqlCommandType) ReflectUtil.getFieldValue(mybatisParameterHandler, "sqlCommandType");

			if (sqlCommandType != SqlCommandType.INSERT
				&& sqlCommandType != SqlCommandType.UPDATE
				&& sqlCommandType != SqlCommandType.DELETE) {
				return Plugin.wrap(target, this);
			}
			Object parameterObject = mybatisParameterHandler.getParameterObject();
			Class<?> tableClass = Optional.ofNullable(parameterObject).map(Object::getClass).orElse(null);
			// 反射获取parameterObject的id属性
			Long id = (Long) ReflectUtil.getFieldValue(parameterObject, "id");
			// 兼容lambada表达式
			if (id == null && parameterObject instanceof Map<?,?>) {
				parameterObject = ((Map<?,?>) parameterObject).get("param1");
				id = (Long) ReflectUtil.getFieldValue(parameterObject, "id");
				tableClass = Optional.ofNullable(parameterObject).map(Object::getClass).orElse(null);
			}

			// 兼容deleteById 参数只有id
			if (id == null && parameterObject instanceof Long) {
				id = (Long) parameterObject;
				MappedStatement ms = (MappedStatement) ReflectUtil.getFieldValue(mybatisParameterHandler, "mappedStatement");
				tableClass = ms.getParameterMap().getType();
			}
			if (id != null && tableClass != null) {
				//id不为空，说明是新增数据或者修改数据，需要新增同步表数据
				TableInfo tableInfo = TableInfoHelper.getTableInfo(tableClass);
				String tableName = tableInfo.getTableName();
				log.info("新增同步表数据，tableName:{},sqlCommandType:{},id:{}", tableName, sqlCommandType, id);
				DataSyncPush dataSyncPush = DataSyncPush.builder()
					.tenantId(DataContextHolder.getTenantId())
					.tableName(tableName)
					.dmlType(sqlCommandType.name())
					.sourceId(id)
					.createTime(LocalDateTime.now())
					.lastSyncTime(LocalDateTime.now())
					.syncCount(0)
					.build();
				syncOperations(dataSyncPush);
			}
		}
		if (target instanceof Executor || target instanceof StatementHandler) {
			return Plugin.wrap(target, this);
		}
		return Interceptor.super.plugin(target);
	}

	/**
	 * 同步操作：
	 * 客户端模式，保存到同步表;
	 * 服务端模式，推送MQTT消息;
	 *
	 * @param dataSyncPush
	 */
	private void syncOperations(DataSyncPush dataSyncPush) {
		//如果是客户端模式，必须有租户id，则需要同步数据，写入同步表
		if (DataContextHolder.getSyncType() == EnableDataSyncScan.SyncType.client &&
			StringUtils.hasText(DataContextHolder.getTenantId())) {
			DataSyncPushDao dataSyncDao = dataSyncDaoProvider.getIfAvailable();
			dataSyncDao.insertDataSyncTable(dataSyncPush);
			PublisherExecutor publisherExecutor = publisherExecutorProvider.getIfAvailable();
			//这里是异步的，不影响主业务，和事务问题
			publisherExecutor.pushInvoker(dataSyncPush.getTableName());
		}
		if (DataContextHolder.getSyncType() == EnableDataSyncScan.SyncType.server) {
			MQTTPublisher mqttPublisher = SpringUtil.getBean(MQTTPublisher.class);
			if (mqttPublisher == null) {
				return;
			}
			//如果是服务端模式，有租户id，则需要推送MQTT消息到这个租户的所有设备
			if (StringUtils.hasText(DataContextHolder.getTenantId())) {
				mqttPublisher.publish(dataSyncPush.getTenantId(), dataSyncPush.getTableName());
			} else {
				//如果没有租户id说明是静态配置数据，考虑是否需要广播到所有客户的所有设备
				//TODO 广播同步数据
				mqttPublisher.broadcast(dataSyncPush.getTableName(), JSONUtil.toJsonStr(dataSyncPush));
			}
		}
	}
}
