package com.xyy.saas.datasync.client.entity;

import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import org.springframework.beans.factory.config.SingletonBeanRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.annotation.MergedAnnotation;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;

import java.lang.annotation.Annotation;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据同步实体扫描器
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-21 14:21
 */
public class DataSyncEntitysScannerRegistrar implements ImportBeanDefinitionRegistrar {

    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry beanDefinitionRegistry) {
		ClassPathDataSyncEntityScanner scanner = new ClassPathDataSyncEntityScanner(
			beanDefinitionRegistry);
//		AnnotationAttributes annoAttrs = AnnotationAttributes
//			.fromMap(importingClassMetadata.getAnnotationAttributes(EnableDataSyncScan.class.getName()));
		MergedAnnotation<Annotation> annotationMergedAnnotation = importingClassMetadata.getAnnotations().get(EnableDataSyncScan.class.getName());
		registerSyncType(annotationMergedAnnotation, beanDefinitionRegistry);

		List<String> basePackages = new ArrayList<>();
		if (annotationMergedAnnotation != null) {
			for (String pkg : annotationMergedAnnotation.getStringArray("scanBasePackages")) {
                if (StringUtils.hasText(pkg)) {
                    basePackages.add(pkg);
                }
            }
            if (basePackages.isEmpty()) {
                basePackages.add(getDefaultBasePackage(importingClassMetadata));
            }
        }
		String[] excludePackages = annotationMergedAnnotation.getStringArray("excludePackages");
		scanner.doScanFindCandidateDatasyncEntity(annotationMergedAnnotation.getClassArray("baseEntity"), StringUtils.toStringArray(basePackages), excludePackages);
    }

    private static String getDefaultBasePackage(AnnotationMetadata importingClassMetadata) {
        return ClassUtils.getPackageName(importingClassMetadata.getClassName());
    }

	private void registerSyncType(MergedAnnotation<Annotation> annotationMergedAnnotation, BeanDefinitionRegistry beanDefinitionRegistry) {
		EnableDataSyncScan.SyncType syncType = annotationMergedAnnotation.getValue("syncType", EnableDataSyncScan.SyncType.class)
			.orElse(EnableDataSyncScan.SyncType.client);
		if (beanDefinitionRegistry instanceof SingletonBeanRegistry) {
			SingletonBeanRegistry singletonBeanRegistry = (SingletonBeanRegistry) beanDefinitionRegistry;
			if (!singletonBeanRegistry.containsSingleton(EnableDataSyncScan.SyncType.class.getName())) {
				singletonBeanRegistry.registerSingleton(EnableDataSyncScan.SyncType.class.getName(), syncType);
			}
		}
		DataContextHolder.setSyncType(syncType);
	}

}
