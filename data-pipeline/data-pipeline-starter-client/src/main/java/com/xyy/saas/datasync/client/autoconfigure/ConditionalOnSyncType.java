package com.xyy.saas.datasync.client.autoconfigure;

import com.xyy.saas.datasync.client.EnableDataSyncScan;
import org.springframework.context.annotation.Conditional;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/13 下午9:11
 */
@Retention(RetentionPolicy.RUNTIME)
@Conditional(SyncTypeCondition.class)
public @interface ConditionalOnSyncType {

	EnableDataSyncScan.SyncType value();
	
}
