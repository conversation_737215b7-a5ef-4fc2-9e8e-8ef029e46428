//package com.xyy.saas.datasync.client.support;
//
//import com.xyy.saas.datasync.client.autoconfigure.DataSyncClientProperties;
//import com.xyy.saas.datasync.client.protocol.DataSubscriber;
//import lombok.Getter;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.TimeUnit;
//import java.util.concurrent.atomic.AtomicBoolean;
//import java.util.function.Function;
//
///**
// * @Desc 数据同步拉取任务
// * <AUTHOR>  <EMAIL>
// * @Date Created in 2021-07-27 15:06
// */
//public class DataPullTask implements Runnable{
//
//    private static final Logger log = LoggerFactory.getLogger(DataPullTask.class);
//
//    private DataSyncClientWorker dataSyncClientWorker;
//
//    private DataSyncClientProperties dataSyncClientProperties;
//
//    private volatile AtomicBoolean pullRunning = new AtomicBoolean(false);
//
//	/* pull任务始终只有一个task执行*/
//	@Getter
//	private static CountDownLatch countDownLatch = new CountDownLatch(1);
//
//	//	@Getter
////	private static Semaphore semaphore = new Semaphore(1);
//
//
//	/* 重试阶乘间隔, 可以理解成异常次数 */
//    private int factorialNum = 1;
//
//    public DataPullTask(DataSyncClientWorker dataSyncClientWorker,
//                        DataSubscriber dataSubscriber,
//                        DataSyncClientProperties dataSyncClientProperties) {
//        this.dataSyncClientWorker = dataSyncClientWorker;
//        this.dataSyncClientProperties = dataSyncClientProperties;
//    }
//
//    @Override
//    public void run() {
//        boolean exception = false;
//        try {
//            dataSyncClientWorker.getPullSubscriberExecutor().pullInvoker();
//        } catch (Exception e) {
//            e.printStackTrace();
//            exception = true;
//            log.error("数据同步执行异常message:[{}]", e.getMessage());
//		}
//		try {
//			countDownLatch.await();
//		} catch (InterruptedException e) {
//			throw new RuntimeException(e);
//		}
//		scheduleNextTask(exception);
//		//调度了下次任务后，需要把countDownLatch重置为1，pullInvoker这个方法以及调用了countDownLatch.countDown()方法，保证只有一个task执行
//		countDownLatch = new CountDownLatch(1);
//    }
//
//
//
//    /**
//	 * 调度下一次任务，超时重试，调度的时间是PullInterval(5分钟)的倍数，最多factorialNum为PullMaxFactorial(5次)，
//	 * 所以连续超时失败的最大时间间隔为5分钟*5次=25分钟
//	 * @param timeout 是否超时
//     */
//    private void scheduleNextTask(boolean timeout) {
//        factorialNum = timeout ? Math.min(++factorialNum, dataSyncClientProperties.getPullMaxFactorial()) : 1;
//        if (timeout) {
//			log.error("Data pull scheduleNextTask timeout, ClienId: [{}] ,factorialNum : [{}]", DataContextHolder.getTenantId(), factorialNum);
//        }
//        int pullInterval = dataSyncClientProperties.getPullInterval() * factorialNum;
//		log.info("开始下次数据同步任务 DataPullTask, TenantId: [{}], pullInterval : [{}]秒", DataContextHolder.getTenantId(), pullInterval);
//        //调度下次任务
//		dataSyncClientWorker.getScheduledExecutorService().schedule(this,
//                pullInterval, TimeUnit.SECONDS);
//    }
//
//}
