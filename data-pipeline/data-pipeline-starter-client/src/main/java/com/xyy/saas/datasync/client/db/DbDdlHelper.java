//package com.xyy.saas.datasync.client.db;
//
//import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;
//import com.xyy.saas.datasync.client.entity.DataSyncException;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.logging.Log;
//import org.apache.ibatis.logging.LogException;
//import org.apache.ibatis.logging.LogFactory;
//import org.springframework.util.StringUtils;
//
//import java.lang.reflect.Constructor;
//import java.sql.Driver;
//import java.util.List;
//
///**
// * @Desc
// * <AUTHOR>  <EMAIL>
// * @Date Created in 2021-08-09 13:34
// */
//@Slf4j
//public class DbDdlHelper {
//
//    private static Class<Driver> jdbcDriverClass = null;
//
//    private static String sqliteName = "org.sqlite.JDBC";
//
//    private static String mysqlName = "com.mysql.jdbc.Driver";
//
//    static {
//        tryImplementation(new Runnable() {
//            @Override
//            public void run() {
//                useSqliteDriver();
//            }
//        });
//        tryImplementation(new Runnable() {
//            @Override
//            public void run() {
//                useMysqlDriver();
//            }
//        });
//    }
//
//    private static void tryImplementation(Runnable runnable) {
//        if (jdbcDriverClass == null) {
//            try {
//                runnable.run();
//            } catch (Throwable t) {
//                // ignore
//            }
//        }
//    }
//
//    public static synchronized void useSqliteDriver() {
//        setImplementation(sqliteName);
//    }
//
//    public static synchronized void useMysqlDriver() {
//        setImplementation(mysqlName);
//    }
//
//    private static void setImplementation(String className) {
//        try {
//            jdbcDriverClass = (Class<Driver>) Class.forName(className);
//        } catch (Throwable t) {
//            throw new DataSyncException("数据同步初始化, Error setting Driver implementation.  Cause: " + t, t);
//        }
//    }
//
//    /**
//     * 获取建表语句
//     * @param dataSyncEntityDefinition
//     * @return
//     */
//    public static String getDdlStatement(DataSyncEntityDefinition dataSyncEntityDefinition) {
//        String tableName = dataSyncEntityDefinition.getTableName();
//        if (StringUtils.isEmpty(tableName)) {
//            throw new DataSyncException("数据同步获取建表语句异常, Error tableName Empty.");
//        }
//        StringBuilder stringBuilder = new StringBuilder();
//        //添加表名
//        stringBuilder.append("CREATE TABLE '");
//
//        stringBuilder.append(tableName);
//        stringBuilder.append("' (");
//
//        if (jdbcDriverClass == null) {
//            throw new DataSyncException("数据同步获取建表语句异常, Error jdbcDriverClass Empty.");
//        } else if (jdbcDriverClass.getName().equals(sqliteName)) {
//            transformToSqlite(dataSyncEntityDefinition, stringBuilder);
//        } else if (jdbcDriverClass.getName().equals(mysqlName)) {
//            transformToMysql(dataSyncEntityDefinition, stringBuilder);
//        }
//        stringBuilder.append(");");
//        return stringBuilder.toString();
//    }
//
//    /**
//     * 转换成Sqlite的建表语句
//     * @param dataSyncEntityDefinition
//     * @param stringBuilder
//     * @return
//     */
//    private static void transformToSqlite(DataSyncEntityDefinition dataSyncEntityDefinition, StringBuilder stringBuilder) {
//        List<DataSyncEntityDefinition.FieldDefinition> fieldDefinitions = dataSyncEntityDefinition.getFields();
//        for(DataSyncEntityDefinition.FieldDefinition fieldDefinition : fieldDefinitions) {
//            //添加列名
//            stringBuilder.append("'");
//            stringBuilder.append(fieldDefinition.getFieldName());
//            stringBuilder.append("' ");
//            //添加类型
//            Class<?> fieldType = fieldDefinition.getFieldType();
//            switch (fieldType.getName()) {
//                case "java.lang.Long" :
//                case "long" :
//                    stringBuilder.append("BIGINT");
//                    break;
//                case "java.lang.Integer" :
//                case "int" :
//                case "java.lang.Boolean" :
//                case "boolean" :
//                    stringBuilder.append("INTEGER");
//                    break;
//                case "java.lang.String" :
//                    stringBuilder.append("VARCHAR");
//                    break;
//                case "java.util.Date" :
//                    stringBuilder.append("TIMESTAMP");
//                    break;
//                default :
//                    throw new DataSyncException("数据同步初始化,transformToSqlite switch fieldType not find. Field ClassName:" + fieldType.getName());
//            }
//            stringBuilder.append(" ");
//            //判断是否主键,是否主键
//            if (fieldDefinition.isPrimaryKey()) {
//                stringBuilder.append("PRIMARY KEY ");
//            }
//            //判断是否自增,是否自增
//            if (fieldDefinition.isAutoIncrement()) {
//                stringBuilder.append("AUTOINCREMENT ");
//            }
//            //添加默认值
//            if (fieldDefinition.getDefaultValue() != null) {
//                stringBuilder.append("DEFAULT ");
//                if (fieldType.getName().equals("java.lang.String")) {
//                    stringBuilder.append("'" + fieldDefinition.getDefaultValue() + "'");
//                } else {
//                    stringBuilder.append(fieldDefinition.getDefaultValue());
//                }
//            }
//            //添加Not Null
//            if (fieldDefinition.isNotNull()) {
//                stringBuilder.append("NOT NULL ");
//            }
//            stringBuilder.append(",");
//        }
//        stringBuilder.setLength(stringBuilder.length() - 2);
//    }
//
//    private static void transformToMysql(DataSyncEntityDefinition dataSyncEntityDefinition, StringBuilder stringBuilder) {
//        //TODO
//    }
//
//
//}
