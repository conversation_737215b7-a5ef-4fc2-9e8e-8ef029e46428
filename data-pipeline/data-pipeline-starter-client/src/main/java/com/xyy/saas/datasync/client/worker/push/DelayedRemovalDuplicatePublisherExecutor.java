package com.xyy.saas.datasync.client.worker.push;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

public class DelayedRemovalDuplicatePublisherExecutor implements PublisherExecutor {

	private final Map<String, Long> pendingTasks = new ConcurrentHashMap<>();
	private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
	private final PublisherExecutor delegate;
	private final AtomicBoolean isRunning = new AtomicBoolean(false);

	public DelayedRemovalDuplicatePublisherExecutor(PublisherExecutor delegate) {
		this.delegate = delegate;
	}

	@Override
	public void pushInvoker() {
		delegate.pushInvoker();
	}

	@Override
	public void pushInvoker(String tableName) {
		long currentTime = System.currentTimeMillis();
		pendingTasks.putIfAbsent(tableName, currentTime);
		if (isRunning.compareAndSet(false, true)) {
			startScheduler();
		}
	}

	private void startScheduler() {
		scheduler.schedule(this::processPendingTasks, 5, TimeUnit.SECONDS);
	}

	private void processPendingTasks() {
		long currentTime = System.currentTimeMillis();
		Iterator<Map.Entry<String, Long>> iterator = pendingTasks.entrySet().iterator();
		while (iterator.hasNext()) {
			Map.Entry<String, Long> entry = iterator.next();
			if (currentTime - entry.getValue() >= 5000) {
				iterator.remove();
				// 先移除，再执行push，避免在putIfAbsent时，导致超过5秒的任务没有被执行pushInvoker
				delegate.pushInvoker(entry.getKey());
			}
		}

		if (pendingTasks.isEmpty()) {
			isRunning.set(false);
		} else {
			scheduler.schedule(this::processPendingTasks, 5, TimeUnit.SECONDS);
		}
	}
}
