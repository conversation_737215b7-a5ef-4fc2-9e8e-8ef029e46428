package com.xyy.saas.datasync.client.filter;

import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.constants.DataSyncUtil;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;

import java.io.IOException;

/**
 * desc 将请求header中的租户id 注入到上下文
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class TenantHeaderFilter implements Filter {
	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
		throws IOException, ServletException {

		HttpServletRequest httpRequest = (HttpServletRequest) request;

		// 从请求头中获取自定义header，并设置到请求属性或上下文中
		String tenantId = httpRequest.getHeader(DataSyncUtil.HEADER_TENANT_ID);
		if (tenantId != null) {
			DataContextHolder.setTenantId(tenantId);
		}

		chain.doFilter(request, response);
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		Filter.super.init(filterConfig);
	}

	@Override
	public void destroy() {
		Filter.super.destroy();
	}
}
