package com.xyy.saas.datasync.client.protocol.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xyy.saas.datasync.client.serialize.MapLocalDateTimeSerializer;
import lombok.Builder;
import lombok.Data;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-26 18:51
 */
@Setter
@Data
@Builder
public class DataPullVO {

	// private T clazz;

	/**
	 * 表名
	 */
	private String tableName;

	private Long id;

	/**
	 * 数据库 dml操作 @link{DmlType}
	 */
	private String dmlType;

	private Long offsetBaseVersion;

	@JsonSerialize(contentUsing = MapLocalDateTimeSerializer.class)
	private List<Map<String, Object>> data;


	static class TableRecord {

	}

}
