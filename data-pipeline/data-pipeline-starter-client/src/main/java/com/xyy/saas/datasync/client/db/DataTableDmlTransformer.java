package com.xyy.saas.datasync.client.db;

import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;
import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @Desc 数据库dml语句转换器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-26 9:28
 */
public interface DataTableDmlTransformer {


	/**
	 * 初始化表，比如 data_sync_push
	 */
	void initializeTable();

	/**
	 * 检查或者更新表结构
	 *
	 * @param dataSource
	 * @param dataSyncEntityDefinition
	 */
    <T> void doCheckAndUpdateTable(DataSource dataSource, DataSyncEntityDefinition<T> dataSyncEntityDefinition);

    /**
     * 检测表
     * @param tableName
     * @return
     */
    String getDetectionTable(String tableName);

    /**
     * 获取建表SQL
     */
	<T> String createTableSql(DataSyncEntityDefinition<T> dataSyncEntityDefinition);

    /**
     * 获取修改表语句
     *
     * @param tableName
     * @param fieldDefinition
     * @param stringBuilder
     */
    void alterTableSql(String tableName, DataSyncEntityDefinition.FieldDefinition fieldDefinition, TableColumn tableColumn,
        StringBuilder stringBuilder);

    /**
     * 检查有变更的字段
     */
	<T> void checkFieldUpdate(DataSyncEntityDefinition<T> dataSyncEntityDefinition);

    /**
	 * 根据字段定义 or 云端表结构 拼装字段SQL
     * @param fieldDefinition
     * @param tableColumn
     * @return
     */
    String transformToFieldSql(DataSyncEntityDefinition.FieldDefinition fieldDefinition, TableColumn tableColumn, StringBuilder stringBuilder);

	/**
	 * 检查表中是否存在某列
	 *
	 * @param tableName
	 * @param columnName
	 * @return
	 */
	boolean checkTableColumnExist(String tableName, String columnName);

	Long getMaxBaseVersion(String tableName);
}
