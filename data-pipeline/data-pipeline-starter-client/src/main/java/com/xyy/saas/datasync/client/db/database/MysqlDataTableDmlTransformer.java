package com.xyy.saas.datasync.client.db.database;

import cn.hutool.extra.spring.SpringUtil;
import com.xyy.saas.datasync.client.constants.DataSyncUtil;
import com.xyy.saas.datasync.client.db.DataTableDmlTransformer;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/5/11 下午5:59
 */
public class MysqlDataTableDmlTransformer implements DataTableDmlTransformer {

	private JdbcTemplate jdbcTemplate;

	public MysqlDataTableDmlTransformer(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	/**
	 * 初始化表
	 */
	@Override
	public void initializeTable() {

	}

	/**
	 * @param dataSource
	 * @param dataSyncEntityDefinition
	 */
	@Override
	public <T> void doCheckAndUpdateTable(DataSource dataSource, DataSyncEntityDefinition<T> dataSyncEntityDefinition) {
	}

	/**
	 * @param tableName
	 * @return
	 */
	@Override
	public String getDetectionTable(String tableName) {
		return "";
	}

	/**
	 * @param dataSyncEntityDefinition
	 * @return
	 */
	@Override
	public <T> String createTableSql(DataSyncEntityDefinition<T> dataSyncEntityDefinition) {
		return "";
	}

	/**
	 * @param tableName
	 * @param fieldDefinition
	 * @param stringBuilder
	 */
	@Override
	public void alterTableSql(String tableName, DataSyncEntityDefinition.FieldDefinition fieldDefinition, TableColumn tableColumn, StringBuilder stringBuilder) {

	}

	/**
	 * @param dataSyncEntityDefinition
	 */
	@Override
	public <T> void checkFieldUpdate(DataSyncEntityDefinition<T> dataSyncEntityDefinition) {

	}

	/**
	 * @param fieldDefinition
	 * @param stringBuilder
	 * @return
	 */
	@Override
	public String transformToFieldSql(DataSyncEntityDefinition.FieldDefinition fieldDefinition, TableColumn tableColumn, StringBuilder stringBuilder) {
		return "";
	}

	/**
	 * @param tableName
	 * @param columnName
	 * @return
	 */
	@Override
	public boolean checkTableColumnExist(String tableName, String columnName) {
		String databaseName = SpringUtil.getProperty("spring.datasource.dynamic.datasource.master.name");
		// 查询表结构，检查是否存在指定字段
		String queryColumnSql = "SELECT COUNT(*) FROM information_schema.columns WHERE TABLE_SCHEMA = database() AND table_schema = ? AND table_name = ? AND column_name = ?";
		int count = jdbcTemplate.queryForObject(queryColumnSql, Integer.class, databaseName, tableName, columnName);
		return count > 0;
	}

	@Override
	public Long getMaxBaseVersion(String tableName) {
		String maxVersionSql = "SELECT MAX(" + DataSyncUtil.BASE_VERSION + ") FROM " + tableName;
		boolean isStaticTable = checkTableColumnExist(tableName, DataSyncUtil.TENANT_ID);
		if (!isStaticTable) {
			maxVersionSql = maxVersionSql + " where tenant_id = " + DataContextHolder.getTenantId();
		}
		Long maxBaseVersion = jdbcTemplate.queryForObject(maxVersionSql, Long.class);
		return maxBaseVersion != null ? maxBaseVersion : 0L;
	}
}
