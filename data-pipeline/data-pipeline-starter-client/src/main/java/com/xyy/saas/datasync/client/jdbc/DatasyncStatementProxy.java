//package com.xyy.saas.datasync.client.jdbc;
//
//import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.executor.Executor;
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.mapping.SqlCommandType;
//import org.apache.ibatis.plugin.*;
//import org.springframework.context.annotation.Lazy;
//
//import jakarta.annotation.Resource;
//
///**
// * @Desc 事务埋点 ，在执行写操作的时候，自动在数据同步表中插入需要同步的数据
// * <AUTHOR>  <EMAIL>
// * @Date Created in 2021-08-04 16:54
// */
//@Slf4j
//@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
//public class DatasyncStatementProxy implements Interceptor {
//
//	//    @Lazy
////    @Resource(type = DatasyncHandler.class)
//    private DatasyncDelegate delegate;
//
//	public DatasyncStatementProxy(DatasyncDelegate delegate) {
//		this.delegate = delegate;
//	}
//
//	@Override
//    public Object plugin(Object target) {
//        if (target instanceof Executor) {
//            return Plugin.wrap(target, this);
//        }
//        return target;
//    }
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//		//只处理 INSERT DELETE UPDATE 语句
//		MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
//		if (ms.getSqlCommandType() != SqlCommandType.INSERT
//			&& ms.getSqlCommandType() != SqlCommandType.UPDATE
//			&& ms.getSqlCommandType() != SqlCommandType.DELETE) {
//			// 跳过
//			return invocation.proceed();
//		}
////		ms.getSqlSource().getBoundSql(null).getSql();
//
//		Object parameter = invocation.getArgs()[1];
//		BoundSql boundSql = ms.getBoundSql(parameter);
//		String sql = boundSql.getSql();
//		//这里这个SQL是
//		//INSERT INTO infra_api_access_log  ( id, trace_id,  user_type, application_name, request_method, request_url, request_params,  user_ip, user_agent, operate_module, operate_name, operate_type, begin_time, end_time, duration, result_code, result_msg, create_time, update_time, creator, updater )  VALUES (  ?, ?,  ?, ?, ?, ?, ?,  ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?  )
//		//我想把这个sql新加一个字段base_version,后查一下infra_api_access_log这个表的base_version字段的最大值 +1，然后在插入的时候把这个值加到base_version字段上。
//
//		SQLDatasyncContext datasyncContext = SQLDatasyncUtil.getDatasyncContext(sql);
//		if (!DataSyncEntityDispatcher.isEnableSync(datasyncContext.getTable())) {
//			log.debug("[数据同步] 跳过 table: {}, sql: {}", datasyncContext.getTable(), sql);
//			return invocation.proceed();
//		}
//
//		log.debug("[数据同步] table: {}, sql: {}", datasyncContext.getTable(), sql);
//		return this.delegate.exec(datasyncContext, invocation, ms, boundSql);
//	}
//
////	private String getBaseVersion(String sql) {
////		// 这里应该查询infra_api_access_log这个表的base_version字段的最大值 +1
////		//实现对sql的解析，然后获取base_version字段的最大值 +1
////
////		return "1";
////	}
//
//}
