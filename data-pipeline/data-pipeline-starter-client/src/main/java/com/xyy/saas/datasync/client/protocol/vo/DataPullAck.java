package com.xyy.saas.datasync.client.protocol.vo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @Desc 数据确认ack
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-28 17:11
 */
@Getter
@Setter
@Builder
public class DataPullAck implements Serializable {

    /** 机构id*/
    private String organId;

    /** 已同步ids*/
    private List<Long> ids;

}
