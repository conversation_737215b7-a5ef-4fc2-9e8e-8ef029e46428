package com.xyy.saas.datasync.client.event;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 10:00
 */
@Getter
@Setter
public class DataSyncEvent extends ApplicationEvent {

    /** 触发数据事件的描述*/
    private String desc;

    public DataSyncEvent(Object source) {
        super(source);
    }

}
