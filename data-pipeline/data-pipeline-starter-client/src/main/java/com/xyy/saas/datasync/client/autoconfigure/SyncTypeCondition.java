package com.xyy.saas.datasync.client.autoconfigure;

import com.xyy.saas.datasync.client.EnableDataSyncScan;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.MergedAnnotation;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.core.type.StandardAnnotationMetadata;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/13 下午4:44
 */
public class SyncTypeCondition implements Condition {

	@Override
	public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
		//获取到EnableDataSyncScan注解上的syncType属性
		EnableDataSyncScan.SyncType syncType = getSyncTypeFromConfigurationBeans(context);
		if (syncType == null) {
			return false;
		}

		//获取到ConditionalOnSyncType注解上的value属性
		MergedAnnotation<ConditionalOnSyncType> syncTypeAnnotation = metadata.getAnnotations().get(ConditionalOnSyncType.class);
		if (syncTypeAnnotation == null) {
			return false;
		}
		EnableDataSyncScan.SyncType conditionalSyncType = syncTypeAnnotation.getValue("value", EnableDataSyncScan.SyncType.class)
			.orElse(EnableDataSyncScan.SyncType.client);

		return syncType == conditionalSyncType;
	}

	private EnableDataSyncScan.SyncType getSyncTypeFromConfigurationBeans(ConditionContext context) {
		// 尝试从上下文缓存中获取
		Object cacheSyncType = context.getBeanFactory().getSingleton(EnableDataSyncScan.SyncType.class.getName());
		if (cacheSyncType instanceof EnableDataSyncScan.SyncType) {
			return (EnableDataSyncScan.SyncType) cacheSyncType;
		}

		// 获取所有 @Configuration 类的名称
		String[] beanNames = context.getBeanFactory().getBeanNamesForAnnotation(Configuration.class);
		for (String beanName : beanNames) {
			// 获取 bean 的定义
			BeanDefinition beanDefinition = context.getBeanFactory().getBeanDefinition(beanName);

			// 检查 bean 的类是否包含 EnableDataSyncScan 注解
			if (beanDefinition instanceof AnnotatedBeanDefinition) {
				AnnotatedBeanDefinition annotatedBeanDefinition = (AnnotatedBeanDefinition) beanDefinition;
				StandardAnnotationMetadata metadata = (StandardAnnotationMetadata) annotatedBeanDefinition.getMetadata();

				// 获取 EnableDataSyncScan 注解
				MergedAnnotation<EnableDataSyncScan> enableDataSyncScanAnnotation = metadata.getAnnotations().get(EnableDataSyncScan.class);
				if (enableDataSyncScanAnnotation != null) {
					EnableDataSyncScan.SyncType syncType = enableDataSyncScanAnnotation
						.getValue("syncType", EnableDataSyncScan.SyncType.class)
						.orElse(EnableDataSyncScan.SyncType.client);

					// 将 syncType 缓存到上下文中
					context.getBeanFactory().registerSingleton(EnableDataSyncScan.SyncType.class.getName(), syncType);
					return syncType;
				}
			}
		}
		return null;
	}
}
