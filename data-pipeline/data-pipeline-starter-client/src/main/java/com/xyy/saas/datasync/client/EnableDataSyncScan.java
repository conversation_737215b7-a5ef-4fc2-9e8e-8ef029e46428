package com.xyy.saas.datasync.client;

import com.xyy.saas.datasync.client.autoconfigure.EnableDataSyncClientConfig;
import com.xyy.saas.datasync.client.entity.DataSyncEntitysScannerRegistrar;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 数据初始化
 * 数据同步
 * 数据比对
 * 数据效验
 * 数据恢复
 * 数据重新拉取
 *
 * @Desc 激活数据同步客户端
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-21 13:47
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@EnableDataSyncClientConfig
@Import(DataSyncEntitysScannerRegistrar.class)
// DB文件初始化 {LocalDBConfig}、TODO DB本地目录路径变化了，给出提示
// table表变更 {link DataTableDmlTransformer}
// 数据进度同步、pull(云同步到端)，同步配置表
// 重下、上传到云端。
//TODO  删除
//TODO  数据容错，数据查看，云端执行本地SQL
//TODO  重传冪等-支持update/delete, insert暂未支持
//TODO  数据一致性，数据ack
//TODO  流量控制,Redis、或者DB乐观锁
//TODO  单表限流、按userId维度，如：5个userId令牌  全量限流-获取令牌, pull时传全量标识, 限流
//TODO  数据协议设计,同步数据要注意脏数据抓取,云端脏数据修改,数据失败报警
//TODO  MQ消息拉取
public @interface EnableDataSyncScan {


	@AliasFor("scanBasePackages")
	String[] value() default {};

	@AliasFor("value")
	String[] scanBasePackages() default {};

	/* 排除的包 */
	String[] excludePackages() default {};


	/**
	 * 继承的基类
	 */
	Class<?>[] baseEntity() default {};

	/**
	 * 排除的类
	 * @return
	 */
	Class<?>[] excludes() default {};

	SyncType syncType() default SyncType.client;

	enum SyncType {
		client,
		server
	}

}
