package com.xyy.saas.datasync.client.autoconfigure;

import com.xyy.saas.datasync.client.constants.SyncStrategy;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @Desc 数据同步属性配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-22 17:10
 */
@Data
@ConfigurationProperties(prefix = "datasync")
public class DataSyncClientProperties {

	/**
	 * 数据同步Url
	 */
	private String url;

	/* 首次pull拉取数据的时间间隔,秒 */
	private int firstPullInterval = 5;

	/* 数据拉取时间间隔, 秒 */
	private int cyclePullInterval = 5 * 60;

	/* pull异常最大阶乘 */
	private int pullMaxFactorial = 5;

	/*首次Push上传数据的时间间隔*/
	private int firstPushInterval = 10;

	/* 数据上传时间间隔, 秒 */
	private int cyclePushInterval = 10 * 60;


	/**
	 * 上传数据的Size
	 */
	private int pushSize = 1000;

	/**
	 * 一次全量拉取的次数
	 */
	private int pullCount = 1000;

	/**
	 * 拉取数据的Size
	 */
	private int pullSize = 1000;

	/*拉取一次1000条同步数据后等待的时间间隔*/
	private Long waitCountTime = 300L;

	/**
	 * 拉取数据最大间隔,异常状态下，最大7200秒，2小时pull一次
	 */
	private int pullMaxInterval = 7200;


	/*push异常最大阶乘*/
	private int pushMaxFactorial = 5;


	private SyncStrategy strategy = SyncStrategy.baseVerion;

	/*超时时间*/
	private long timeout = 30000;


}
