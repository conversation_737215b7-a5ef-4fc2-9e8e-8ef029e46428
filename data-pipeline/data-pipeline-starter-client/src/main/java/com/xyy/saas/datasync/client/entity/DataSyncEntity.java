package com.xyy.saas.datasync.client.entity;

import com.xyy.saas.datasync.client.constants.DataSyncDirection;
import com.xyy.saas.datasync.client.idempotent.IdempotentStrategy;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.lang.annotation.*;

/**
 * @Desc 数据同步实体
 *       不支持修改列名，不支持修改列类型，只能新增字段
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-21 13:59
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface DataSyncEntity {

	boolean enable() default true;

	String primaryKey() default "id";

	boolean autoIncrement() default true;

	/**
	 * 同步方向,1:云向端同步,2:端向云同步
	 */
	DataSyncDirection direction() default DataSyncDirection.NONE;

	int order() default 0;

	/**
	 * 写数据触发同步
	 */
	boolean actionSync() default false;

	String tableName() default "";

	boolean ack() default false;

	/**
	 * 是否触发同步事件
	 */
	boolean syncFinishedEvent() default false;

    /** 冪等策略*/
    IdempotentStrategy idempotentStrategy() default IdempotentStrategy.ignore ;

}
