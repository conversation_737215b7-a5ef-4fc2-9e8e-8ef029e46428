package com.xyy.saas.datasync.client.entity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationAttributes;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-30 9:19
 */
@Slf4j
public class DataSyncEntityDispatcher {


	public static DataSyncEntityDefinition getTableEntity(String tableName) {
		return DataSyncEntityContext.tableEntityMap.get(tableName);
	}

//    public static Map<String, DataSyncEntityDefinition> getDataSyncEntityMap() {
//        return dataSyncEntityMap;
//    }

	/**
	 * 处理需要埋点的sql,数据同步操作 See {@link DataSyncEntity} for details.
	 *
	 * @param tableName
	 */
	public static boolean isEnableSync(String tableName) {
		DataSyncEntityDefinition ed = DataSyncEntityContext.tableEntityMap.get(tableName);
		if (ed == null) {
			return false;
		}
		if (ed.getAnnotationAttributes().containsKey("enable") && !ed.getAnnotationAttributes().getBoolean("enable")) {
			//有DataSyncEntity注解并且屏蔽了同步
			return false;
		}
		return true;
	}

	public static class DataSyncEntityContext {

		/**
		 * 类名, 表定义
		 */
		private static ConcurrentHashMap<String, DataSyncEntityDefinition> dataSyncEntityMap;

		/**
		 * 表名, 表定义
		 */
		private static ConcurrentHashMap<String, DataSyncEntityDefinition> tableEntityMap;

		/**
		 * 表名
		 */
		private static List<String> tableNames;

		static {
			dataSyncEntityMap = new ConcurrentHashMap<>();
			tableEntityMap = new ConcurrentHashMap<>();
			tableNames = Collections.synchronizedList(new ArrayList<>());
		}

		public static void putDataSyncEntity(String className,
			DataSyncEntityDefinition entityDefinition) {
			if (dataSyncEntityMap.contains(className)) {
				log.error(
					"数据同步 DataSyncEntityDispatcher 添加数据同步类Class:[{}] 已存在 entityDefinition[{}], entityDefinition[{}]",
					className, dataSyncEntityMap.get(className).toString(),
					entityDefinition == null ? entityDefinition : entityDefinition.toString());
			}
			dataSyncEntityMap.put(className, entityDefinition);
			tableEntityMap.put(entityDefinition.getTableName(), entityDefinition);
		}

		public static void resetTableNames() {
			tableNames.clear();
			tableNames.addAll(tableEntityMap.keySet());
			// 实例间有依赖,必须先同步表1再同步表2
			Collections.sort(tableNames, new Comparator<String>() {
				@Override
				public int compare(String o1, String o2) {
					AnnotationAttributes aa1 = tableEntityMap.get(o1).getAnnotationAttributes();
					AnnotationAttributes aa2 = tableEntityMap.get(o2).getAnnotationAttributes();
					if (!aa1.containsKey("order") || !aa2.containsKey("order")) {
						return 0;
					}
					return aa1.getNumber("order").intValue() - aa2.getNumber("order").intValue();
				}
			});

		}

//        /**
//         * 根据className获取 tableName
//         * @param entityDefinition
//         * @return
//         */
//        private static String getTableNameByClassName(DataSyncEntityDefinition entityDefinition) {
//            String tableName = entityDefinition.getAnnotationAttributes().getString("tableName");
//            if (StringUtils.isEmpty(tableName)) {
//                String className = entityDefinition.getClassName();
//                String shortName = ClassUtils.getShortName(className);
//                tableName = new PropertyNamingStrategy.SnakeCaseStrategy().translate(shortName);
//                entityDefinition.setTableName(tableName);
//            }
//            return tableName;
//        }

		public static Map<String, DataSyncEntityDefinition> getDataSyncEntityMap() {
			return dataSyncEntityMap;
		}

		public static Map<String, DataSyncEntityDefinition> getTableEntityMap() {
			return tableEntityMap;
		}

		public static List<String> getTableNames() {
			return tableNames;
		}
    }

}
