package com.xyy.saas.datasync.client.protocol;

import cn.hutool.extra.spring.SpringUtil;
import com.xyy.saas.datasync.client.constants.DataSyncUtil;
import com.xyy.saas.datasync.client.db.DataTableDmlTransformer;
import com.xyy.saas.datasync.client.protocol.vo.DataPullAck;
import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

/**
 * @Desc 数据订阅组实现
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/03/31 17:20
 */
@Slf4j
public class DataSubscriberImpl implements DataSubscriber {

	private final DataSyncClient dataSyncClient;

	public DataSubscriberImpl(DataSyncClient dataSyncClient) {
		this.dataSyncClient = dataSyncClient;
	}

	/**
	 * 向云端拉取数据
	 *
	 * @param tenantId 机构号
	 */
	@Override
	public DataPullVO pull(String tenantId, String tableName) {
		Long maxBaseVersion = SpringUtil.getBean(DataTableDmlTransformer.class)
			.getMaxBaseVersion(tableName);
		return pull(tenantId, tableName, maxBaseVersion);
	}


	/**
	 * 在这个方法扫描后会sort排序,在这里同步的时候解决先后关系 DataSyncEntityContext.resetTableNames
	 *
	 * @param tenantId
	 * @param tableName
	 * @return
	 */
	@Override
	public DataPullVO pull(String tenantId, String tableName, Long baseVersion) {
		CommonResult<DataPullVO> result = null;
		try {
			result = dataSyncClient.pull(tenantId, "0", tableName, baseVersion);
		} catch (Exception e) {
			log.error("--------------pull data from cloud error:{}----------------", e.getMessage(), e);
		}
		if (result == null || result.getCode() != 0) {
			return null;
		}
		return result.getData();
	}

	/**
	 * 向云端确认数据拉取成功
	 *
	 * @param dataPullAck
	 */
	@Override
	public boolean confirm(DataPullAck dataPullAck) {
		CommonResult<Boolean> result = null;
		try {
			result = dataSyncClient.ack(dataPullAck);
		} catch (Exception e) {
			log.error("--------------pull data ack to cloud error:{}----------------", e.getMessage(), e);
		}
		if (result == null || result.getCode() != 0) {
			return false;
		}
		return result.getData();
	}
}
