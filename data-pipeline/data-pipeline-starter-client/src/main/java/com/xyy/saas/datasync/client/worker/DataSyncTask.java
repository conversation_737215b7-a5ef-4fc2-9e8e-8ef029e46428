package com.xyy.saas.datasync.client.worker;

import com.xyy.saas.datasync.client.constants.DataContextHolder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/12 下午2:17
 */
@Slf4j
public class DataSyncTask implements Runnable {

	private String taskName;

	/* 同类型的任务始终只有一个task执行*/
	@Getter
	private CountDownLatch countDownLatch = new CountDownLatch(1);


	private Supplier<Boolean> task;

	/*首次拉取间隔*/
	private int firstInterval;

	/*最大阶乘次数*/
	private int maxFactorial;

	/* 轮询间隔 */
	private int cycleInterval;

	private ScheduledExecutorService scheduledExecutorService;

	//	private volatile AtomicBoolean pullRunning = new AtomicBoolean(false);
	/* 重试阶乘间隔, 可以理解成异常次数 */
	private int factorialNum = 1;

	public DataSyncTask(
		int firstInterval, int cycleInterval, int maxFactorial,
		String taskName,
		Supplier<Boolean> task) {
		this.task = task;
		this.firstInterval = firstInterval;
		this.cycleInterval = cycleInterval;
		this.maxFactorial = maxFactorial;
		this.taskName = taskName;
		this.scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
	}

	public ScheduledFuture<?> start() {
		return scheduledExecutorService.schedule(this, 0, TimeUnit.SECONDS);
	}

	public void stop() {
		scheduledExecutorService.shutdown();
	}

	@Override
	public void run() {
		boolean exception = false;
		try {
			task.get();
		} catch (Exception e) {
			e.printStackTrace();
			exception = true;
			log.error("数据同步 taskName:[{}] ,执行异常message:[{}]", taskName, e.getMessage());
		}
		try {
			countDownLatch.await();
		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}
		scheduleNextTask(exception);
		//调度了下次任务后，需要把countDownLatch重置为1，pullInvoker这个方法以及调用了countDownLatch.countDown()方法，保证只有一个task执行
		countDownLatch = new CountDownLatch(1);
	}


	/**
	 * 调度下一次任务，超时重试，调度的时间是interval(5分钟)的倍数，最多factorialNum为PullMaxFactorial(5次)，
	 * 所以连续超时失败的最大时间间隔为5分钟*5次=25分钟
	 *
	 * @param timeout 是否超时
	 */
	private void scheduleNextTask(boolean timeout) {
		factorialNum = timeout ? Math.min(++factorialNum, maxFactorial) : 1;
		if (timeout) {
			log.error("Data pull scheduleNextTask:[{}] timeout, TenantId: [{}] ,factorialNum : [{}]", taskName, DataContextHolder.getTenantId(), factorialNum);
		}
		int interval = this.cycleInterval * factorialNum;
		log.info("开始下次数据同步任务 DataSyncTask:[{}], TenantId: [{}], interval : [{}]秒", taskName, DataContextHolder.getTenantId(), interval);
		//调度下次任务
		this.scheduledExecutorService.schedule(this, interval, TimeUnit.SECONDS);
	}

}
