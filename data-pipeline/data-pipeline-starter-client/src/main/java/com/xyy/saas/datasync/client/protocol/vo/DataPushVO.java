package com.xyy.saas.datasync.client.protocol.vo;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Builder;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-26 19:00
 */
@Builder
@Data
public class DataPushVO {

	/* 表名 */
	private String tableName;

	/* 插入新增场景的数据 */
	private List<Map<String, Object>> insertList;

	/* 插入更新场景的数据 */
	private List<Map<String, Object>> updateList;

	/* 删除场景的数据 */
	private List<Long> deleteList;

	public boolean checkData() {
		if (StringUtils.isEmpty(tableName)) {
			return false;
		}
		return CollectionUtil.isNotEmpty(insertList) || CollectionUtil.isNotEmpty(updateList) || CollectionUtil.isNotEmpty(deleteList);
	}


}
