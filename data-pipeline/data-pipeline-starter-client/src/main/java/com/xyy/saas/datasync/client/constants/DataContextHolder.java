package com.xyy.saas.datasync.client.constants;

import com.xyy.saas.datasync.client.EnableDataSyncScan;

import java.util.concurrent.atomic.AtomicReference;
import lombok.Data;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-27 15:57
 */
@Data
public class DataContextHolder {

	/* 租户id,机构号id*/
	public static final String TENANT_ID = "TENANT_ID";

	/* 设备id*/
	private static final String DEVICE_ID = "DEVICE_ID";
	/* 同步状态 */
    public static AtomicReference<SyncState> syncState = new AtomicReference<>(SyncState.NONE);

	/* 同步类型 */
	private static EnableDataSyncScan.SyncType syncType;

	/* 机构号 */
	private static volatile String tenantId = System.getProperty(TENANT_ID);

	private static volatile String deviceId = System.getProperty(DEVICE_ID);

	public static EnableDataSyncScan.SyncType getSyncType() {
		return DataContextHolder.syncType;
	}

	public static void setSyncType(EnableDataSyncScan.SyncType syncType) {
		DataContextHolder.syncType = syncType;
	}

	public static String getTenantId() {
		return DataContextHolder.tenantId;
    }

	public static void setTenantId(String tenantId) {
		DataContextHolder.tenantId = tenantId;
	}

	public static String getDeviceId() {
		return DataContextHolder.deviceId;
	}

	public static void setDeviceId(String deviceId) {
		DataContextHolder.deviceId = deviceId;
	}

}
