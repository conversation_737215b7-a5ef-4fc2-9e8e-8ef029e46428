package com.xyy.saas.datasync.client.worker;

import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;
import com.xyy.saas.datasync.client.protocol.vo.DataPushVO;

import java.util.List;
import java.util.Map;

/**
 * @Desc 数据同步lookup接口主要是为了数据同步服务动态转发pull请求到业务服务获取数据
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/04/03 14:36
 */
public interface DataSyncLookup {

	/**
	 * 获取本服务的数据同步表
	 *
	 * @return 服务器名, 表名
	 */

	Map<String, List<String>> getServerTableNames();

	/**
	 * 批量 获取表&列信息
	 *
	 * @param tableNameList 表名
	 * @return 列名
	 */
	Map<String, List<TableColumn>> getTableInfo(List<String> tableNameList);

	/**
	 * 获取baseVersion之后的1000条数据
	 *
	 * @param biz         业务线
	 * @param tenantId
	 * @param tableName   表名
	 * @param baseVersion 最大版本号
	 * @return
	 */
	DataPullVO invokerLookup(String biz, String tenantId, String tableName, Long baseVersion);

	List<Long> invokerPush(String tenantId, DataPushVO dataPushVO);
}
