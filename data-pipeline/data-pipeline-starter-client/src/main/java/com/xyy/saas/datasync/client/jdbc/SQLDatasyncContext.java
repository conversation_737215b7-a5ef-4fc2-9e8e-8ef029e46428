//package com.xyy.saas.datasync.client.jdbc;
//
//import com.xyy.saas.datasync.client.constants.DmlType;
//import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;
//import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher;
//import lombok.Getter;
//import lombok.Setter;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * <p>
// */
//public class SQLDatasyncContext {
//	@Getter
//	private final DmlType type;
//	@Getter
//	private final String table;
//
//	/*原始sql语句**/
//	@Getter
//	private final String targetSql;
//	@Getter
//	private final DataSyncEntityDefinition entityDefinition;
//
//	@Getter
//    @Setter
//    private String convertSql;
//
//    /** 修改删除 受影响的主键列表 **/
//    @Getter
//    @Setter
//    private Object[] primaryKeys;
//
//    /** 需同步的数据 */
//    @Getter
//    @Setter
//    private List<Object> data;
//
//	public SQLDatasyncContext(DmlType type, String table, String targetSql) {
//		this.type = type;
//		this.table = table;
//		this.targetSql = targetSql;
//		this.entityDefinition = DataSyncEntityDispatcher.getTableEntity(table);
//	}
//
//    public String primaryKeyName() {
//        return this.entityDefinition.getPrimaryKey().getFieldName();
//    }
//    public Class primaryKeyType() {
//        return this.entityDefinition.getPrimaryKey().getFieldType();
//    }
//}
