package com.xyy.saas.datasync.client.worker.push;

import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.worker.DataSyncClientWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/17 下午5:19
 */
@Slf4j
public class SingleThreadPublisherExecutor implements PublisherExecutor {

	private final static ExecutorService EXECUTOR_SERVICE = Executors.newSingleThreadExecutor((r) -> {    // 创建单线程的线程池);
		Thread thread = new Thread(r);
		thread.setName("single-thread-push-subscriber-executor");
		return thread;
	});

	/* 委托对象 */
	private PublisherExecutor delegate;

	public SingleThreadPublisherExecutor(PublisherExecutor delegate) {
		this.delegate = delegate;
	}


	/**
	 *
	 */
	@Override
	public void pushInvoker() {
		EXECUTOR_SERVICE.submit(new Runnable() {
			@Override
			public void run() {
				StopWatch stopWatch = new StopWatch();
				stopWatch.start();
				log.info("数据同步,单线程推送全量任务开始, 机构号:[{}].", DataContextHolder.getTenantId());
				delegate.pushInvoker();
				stopWatch.stop();
				DataSyncClientWorker.getDataPushTask().getCountDownLatch().countDown();
				log.info("数据同步,单线程推送全量任务结束,释放信号量, 机构号:[{}], 耗时:[{}]ms.", DataContextHolder.getTenantId(), stopWatch.getTotalTimeMillis());
			}
		});
	}

	/**
	 * @param tableName 表名
	 */
	@Override
	public void pushInvoker(String tableName) {
		EXECUTOR_SERVICE.submit(new Runnable() {
			@Override
			public void run() {
				StopWatch stopWatch = new StopWatch();
				stopWatch.start();
				log.info("数据同步,单线程推送任务开始, 机构号:[{}], 表名:[{}].", DataContextHolder.getTenantId(), tableName);
				delegate.pushInvoker(tableName);
				stopWatch.stop();
//				DataSyncClientWorker.getDataPullTask().getCountDownLatch().countDown();
				log.info("数据同步,单线程推送任务结束,释放信号量, 机构号:[{}], 表名:[{}], 耗时:[{}]ms", DataContextHolder.getTenantId(), tableName, stopWatch.getTotalTimeMillis());
			}
		});
	}
}
