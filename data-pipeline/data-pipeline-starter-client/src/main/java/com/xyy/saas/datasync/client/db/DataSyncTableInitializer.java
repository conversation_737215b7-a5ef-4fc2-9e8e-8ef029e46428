package com.xyy.saas.datasync.client.db;

import com.xyy.saas.datasync.client.entity.DataSyncEntityDefinition;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher.DataSyncEntityContext;
import com.xyy.saas.datasync.client.entity.dto.TableColumn;
import com.xyy.saas.datasync.client.protocol.CommonResult;
import com.xyy.saas.datasync.client.protocol.DataSyncClient;
import com.xyy.saas.datasync.client.protocol.vo.DataPullVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.ObjectProvider;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Desc 数据同步数据表初始化器，本地自动生成所有需要检测的表的结构，并且检测表结构是否一致，不一致则进行更新
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-21 14:45
 */
@Slf4j
public class DataSyncTableInitializer implements InitializingBean {

    private ObjectProvider<DataTableDmlTransformer> dmlProvider;
    private ObjectProvider<DataSource> dataSourceProvider;
    private ObjectProvider<DataSyncClient> dataSyncClientProvider;

    public DataSyncTableInitializer(ObjectProvider<DataTableDmlTransformer> dmlProvider,
                                    ObjectProvider<DataSource> dataSourceProvider,
                                    ObjectProvider<DataSyncClient> dataSyncClientProvider) {
        this.dmlProvider = dmlProvider;
        this.dataSourceProvider = dataSourceProvider;
        this.dataSyncClientProvider = dataSyncClientProvider;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DataTableDmlTransformer dmlTransformer = dmlProvider.getIfAvailable();
        if (dmlTransformer == null) {
            log.error("数据同步检测表, DataSyncTableInitializer dmlTransformer is Empty.");
            return;
        }
        DataSource dataSource = dataSourceProvider.getIfAvailable();
        if (dataSource == null) {
            log.error("数据同步检测表, DataSyncTableInitializer dataSource is Empty.");
            return;
        }

		Map<String, DataSyncEntityDefinition> tableEntityMapMap = DataSyncEntityContext.getTableEntityMap();

		Map<String,List<TableColumn>> tableInfoMap = getTableInfo(DataSyncEntityContext.getTableNames());

        for (String tableName : tableEntityMapMap.keySet()) {
            DataSyncEntityDefinition<?> dataSyncEntityDefinition = tableEntityMapMap.get(tableName);

			// 本地检测表结构
			if (tableInfoMap != null && tableInfoMap.get(tableName) != null) {
				Map<String, TableColumn> tableColumnMap = tableInfoMap.get(tableName)
					.stream().collect(Collectors.toMap(TableColumn::name, Function.identity()));

				dataSyncEntityDefinition.setTableColumnMap(tableColumnMap);
			}
            dmlTransformer.doCheckAndUpdateTable(dataSource, dataSyncEntityDefinition);
        }
    }

	/**
	 * 获取云端表结构
	 * @param tableNameList
	 * @return
	 */
	public Map<String, List<TableColumn>> getTableInfo(List<String> tableNameList) {
		DataSyncClient dataSyncClient = dataSyncClientProvider.getIfAvailable();
		if (dataSyncClient == null) {
			// log.error("数据同步检测表, DataSyncTableInitializer dataSyncClient is Empty.");
			// return;
			return Map.of();
		}

		CommonResult<Map<String, List<TableColumn>>> result = null;
		try {
			result = dataSyncClient.getTableInfo(tableNameList);
		} catch (Exception e) {
			log.error("--------------getTableInfo from cloud error:{}----------------", e.getMessage(), e);
		}
		if (result == null || result.getCode() != 0) {
			return Map.of();
		}
		return result.getData();
	}
}
