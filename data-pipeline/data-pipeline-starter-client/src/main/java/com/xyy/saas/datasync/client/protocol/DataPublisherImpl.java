package com.xyy.saas.datasync.client.protocol;

import com.xyy.saas.datasync.client.protocol.vo.DataPushVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/12 下午4:56
 */
@Slf4j
public class DataPublisherImpl implements DataPublisher {

	private DataSyncClient dataSyncClient;

	public DataPublisherImpl(DataSyncClient dataSyncClient) {
		this.dataSyncClient = dataSyncClient;
	}

	/**
	 *
	 */
	@Override
	public List<Long> push(String tenantId, DataPushVO dataPushVO) {
		CommonResult<List<Long>> result = dataSyncClient.push(tenantId, dataPushVO);
		if (!result.isSuccess()) {
			// 抛异常
			throw new RuntimeException(result.getMsg());
			// return Collections.emptyList();
		}
		return result.getData();
	}

}
