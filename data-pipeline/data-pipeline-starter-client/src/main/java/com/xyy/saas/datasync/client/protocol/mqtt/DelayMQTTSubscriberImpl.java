package com.xyy.saas.datasync.client.protocol.mqtt;

import com.xyy.saas.datasync.client.protocol.CommonResult;
import com.xyy.saas.datasync.client.protocol.DataSyncClient;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.datasync.client.worker.pull.PullSubscriberExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.SmartLifecycle;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/6/13 下午1:32
 */
@Slf4j
public class DelayMQTTSubscriberImpl implements MQTTSubscriber, SmartLifecycle {

	private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(
		(a) -> new Thread("DelayMQTTSubscriberImpl-scheduler"));

	private DataSyncClient dataSyncClient;

	private PullSubscriberExecutor pullSubscriberExecutor;

	public DelayMQTTSubscriberImpl(DataSyncClient dataSyncClient, PullSubscriberExecutor pullSubscriberExecutor) {
		this.dataSyncClient = dataSyncClient;
		this.pullSubscriberExecutor = pullSubscriberExecutor;
	}

	@Override
	public void subscribe(String tableName) {
		//对接pull方法,拉取tableName对应的最新数据
		pullSubscriberExecutor.pullInvoker(tableName);
	}

	/**
	 * 每3秒拉取一次需要同步的表
	 */
	private void startScheduler() {
		scheduler.scheduleAtFixedRate(() -> {
			CommonResult<List<String>> listCommonResult = dataSyncClient.checkTableUpdate(DataContextHolder.getTenantId(), DataContextHolder.getDeviceId());
			List<String> tableNames = listCommonResult.getData();
			if (CollectionUtils.isEmpty(tableNames)) {
				return;
			}
			tableNames.forEach(tableName -> {
				subscribe(tableName);
			});
		}, 0, 3, TimeUnit.SECONDS);
	}

	@Override
	public void start() {
		startScheduler();
	}

	@Override
	public void stop() {
		scheduler.shutdown();
	}

	@Override
	public boolean isRunning() {
		return !scheduler.isShutdown();
	}
}
