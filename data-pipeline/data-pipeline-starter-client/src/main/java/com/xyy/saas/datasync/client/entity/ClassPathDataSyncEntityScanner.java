package com.xyy.saas.datasync.client.entity;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher.DataSyncEntityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanDefinitionStoreException;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ClassPathBeanDefinitionScanner;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.ClassMetadata;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.Assert;

import java.io.IOException;

/**
 * @Desc 数据实体扫描器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-07-29 17:54
 */
@Slf4j
public class ClassPathDataSyncEntityScanner extends ClassPathBeanDefinitionScanner {

    static final String DEFAULT_RESOURCE_PATTERN = "**/*.class";

    /**
     * Create a new {@code ClassPathBeanDefinitionScanner} for the given bean factory.
     *
     * @param registry the {@code BeanFactory} to load bean definitions into, in the form
     *                 of a {@code BeanDefinitionRegistry}
     */
    public ClassPathDataSyncEntityScanner(BeanDefinitionRegistry registry) {
        super(registry, true);
    }

    @Override
    protected void registerDefaultFilters() {
        addIncludeFilter(new AnnotationTypeFilter(DataSyncEntity.class));
    }

	public void doScanFindCandidateDatasyncEntity(Class<?>[] baseEntity, String[] basePackages, String[] excludePackages) {
		Assert.notEmpty(basePackages,
			"doScanFindCandidateDatasyncEntity At least one base package must be specified");
		for (String basePackage : basePackages) {
			findCandidateDatasyncEntity(baseEntity, basePackage, excludePackages);
		}
		DataSyncEntityContext.resetTableNames();

		int size = DataSyncEntityContext.getDataSyncEntityMap().size();
		if (size == 0) {
			log.error("数据同步没有在basePackage:[{}], 扫描到@DataSyncEntity的class", (Object) basePackages);
			return;
		}
		log.info("数据同步扫描basePackage:[{}],扫描到@DataSyncEntity的class size:[{}]",
			basePackages, size);
	}


	private void findCandidateDatasyncEntity(Class<?>[] baseEntity, String basePackage, String[] excludePackages) {
        try {
            String packageSearchPath = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX +
                    resolveBasePackage(basePackage) + '/' + DEFAULT_RESOURCE_PATTERN;
            ResourceLoader resourceLoader = getResourceLoader();
            if (!(resourceLoader instanceof ResourcePatternResolver)) {
                log.error("数据同步resourceLoader:[{}] 不是 ResourcePatternResolver", resourceLoader);
                return;
            }
            ResourcePatternResolver r = (ResourcePatternResolver) resourceLoader;
            Resource[] resources = r.getResources(packageSearchPath);
            boolean traceEnabled = logger.isTraceEnabled();
            boolean debugEnabled = logger.isDebugEnabled();
			AntPathMatcher matcher = new AntPathMatcher();
            for (Resource resource : resources) {
				if (ArrayUtil.isNotEmpty(excludePackages) && excludePackage(matcher, resource, excludePackages, debugEnabled)) {
					//TODO 当Resource的类名包含excludePackages，则跳过
					continue;
				}
				if (traceEnabled) {
                    logger.trace("Scanning " + resource);
                }
                if (resource.isReadable()) {
                    try {
                        MetadataReader metadataReader = this.getMetadataReaderFactory().getMetadataReader(resource);
//						if (isCandidateComponent(metadataReader)) {
						//判断 classMetadata 这个类是不是 baseEntity子类，如果是子类，就构造DataSyncEntityDefinition
						AnnotationMetadata annotationMetadata = metadataReader.getAnnotationMetadata();
						AnnotationAttributes annoAttrs = AnnotationAttributes
							.fromMap(annotationMetadata.getAnnotationAttributes(
								DataSyncEntity.class.getName()));

						ClassMetadata classMetadata = metadataReader.getClassMetadata();
						//没有标注DataSyncEntity注解、也不是扫描类的子类
						if (annoAttrs == null) {
							if (!isSubClass(baseEntity, classMetadata)) {
								continue;
							}
							annoAttrs = AnnotationAttributes
								.fromMap(annotationMetadata.getAnnotationAttributes(
									TableName.class.getName()));
							if (annoAttrs == null) {
								continue;
							}
						}
						String className = annotationMetadata.getClassName();
							//构造数据同步实体
						DataSyncEntityDefinition dataSyncEntityDefinition = DataSyncEntityDefinition.builder()
							.className(className)
							.annotationAttributes(annoAttrs)
							.build();
						DataSyncEntityContext.putDataSyncEntity(className,
							dataSyncEntityDefinition);
						log.debug("数据同步扫描@DataSyncEntity类,注册Entity Class:[{}]",
							className);
//						}
//                        else {
//                            if (traceEnabled) {
//                                logger.trace("Ignored because not matching any filter: " + resource);
//                            }
//                        }
                    }
                    catch (Throwable ex) {
                        throw new BeanDefinitionStoreException(
                                "ClassPathDataSyncEntityScanner Failed to read candidate DataSync class: " + resource, ex);
                    }
                }
                else {
                    if (traceEnabled) {
                        logger.trace("ClassPathDataSyncEntityScanner Ignored because not readable: " + resource);
                    }
                }
            }
        }
        catch (IOException ex) {
            throw new DataSyncException("ClassPathDataSyncEntityScanner I/O failure during classpath scanning", ex);
        }
    }

	private boolean excludePackage(AntPathMatcher matcher, Resource resource, String[] excludePackages, boolean debugEnabled) throws IOException {
		// 获取类名
		MetadataReader metadataReader = this.getMetadataReaderFactory().getMetadataReader(resource);
		String className = metadataReader.getAnnotationMetadata().getClassName();

		// 检查类名是否包含在排除列表中
		for (String excludePattern : excludePackages) {
			if (matcher.match(excludePattern + ".*", className)) {
				if (debugEnabled) {
					log.debug("数据同步扫描@DataSyncEntity类,排除包:[{}],扫描到class:[{}]", excludePattern, resource.getURL());
				}
				return true; // 跳过本次循环迭代
			}
		}
		return false;
	}

	/**
	 * 判断是否是baseEntity的子类
	 *
	 * @param baseEntity
	 * @param classMetadata
	 * @return
	 */
	private boolean isSubClass(Class<?>[] baseEntity, ClassMetadata classMetadata) {
		if (ArrayUtil.isNotEmpty(baseEntity)) {
			try {
				Class<?> targetClass = Class.forName(classMetadata.getClassName());
				for (Class<?> base : baseEntity) {
					if (base.isAssignableFrom(targetClass)) {
						return true; // 一旦匹配到一个 baseEntity，就跳出循环
					}
				}
				return false;
			} catch (ClassNotFoundException e) {
				throw new RuntimeException(e);
			}
		}
		return false;
	}

}
