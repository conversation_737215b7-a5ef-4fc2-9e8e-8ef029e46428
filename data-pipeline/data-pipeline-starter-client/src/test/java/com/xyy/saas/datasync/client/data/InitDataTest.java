package com.xyy.saas.datasync.client.data;

import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.datasync.client.XyySaaSLocalServerTest;
import com.xyy.saas.datasync.client.autoconfigure.DataSyncClientAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
//TODO import org.junit.runner.RunWith;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.context.support.AnnotationConfigWebApplicationContext;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-09-15 18:43
 */
@XyySaaSLocalServerTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ExtendWith(SpringExtension.class)
@EnableDataSyncScan({"com.xyy.saas.datasync.client.entity"})
//@ComponentScan("com.xyy.saas.datasync")
@DirtiesContext
@Slf4j
public class InitDataTest {

    @Autowired
    private AnnotationConfigWebApplicationContext ac;


    //TODO    @Before
    @BeforeEach
    public void before() {
//        DataSyncTableInitializer dataSyncTableInitializer
        ac.register(DataSyncClientAutoConfiguration.class);
    }

    @Test
    public void init() {
        System.out.println("1");
    }


}
