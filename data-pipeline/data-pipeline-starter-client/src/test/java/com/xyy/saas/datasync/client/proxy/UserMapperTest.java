package com.xyy.saas.datasync.client.proxy;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-12 9:16
 */
@Mapper
public interface UserMapperTest {

//    @Select("SELECT * FROM users WHERE id = #{userId}")
//    User getUser(@Param("userId") String userId);

//    @Update("")
    @Insert("insert into user_info (id,name) values(#{id},#{name})")
    void insert(UserInfo userInfo);
    @Insert("insert into user_info (id,name) values(11,'xiaohong')")
    void insert1();

}
