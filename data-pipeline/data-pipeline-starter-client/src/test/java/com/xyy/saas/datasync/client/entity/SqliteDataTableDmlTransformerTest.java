//package com.xyy.saas.datasync.client.entity;
//
//import com.xyy.saas.datasync.client.XyySaaSLocalServerTest;
////import com.xyy.saas.datasync.client.db.database.SqliteDataTableDmlTransformer;
//import org.junit.jupiter.api.Test;
//import org.springframework.context.annotation.AnnotationConfigApplicationContext;
//import org.springframework.test.annotation.DirtiesContext;
//
//import java.util.Map;
//
///**
// * @Desc
// * <AUTHOR>  <EMAIL>
// * @Date Created in 2021-08-20 18:38
// */
//@XyySaaSLocalServerTest
////@RunWith(SpringRunner.class)
//@DirtiesContext
//public class SqliteDataTableDmlTransformerTest {
//
//    @Test
//    public void testDataSyncEntityScanner() {
////        SqliteDataTableDmlTransformer sqliteDataTableDmlTransformer = new SqliteDataTableDmlTransformer();
//
//
////        sqliteDataTableDmlTransformer.alterTableSql()
//    }
//
//}
