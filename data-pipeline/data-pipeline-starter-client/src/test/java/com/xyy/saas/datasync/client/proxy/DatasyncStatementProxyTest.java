package com.xyy.saas.datasync.client.proxy;

import com.xyy.saas.datasync.client.XyySaaSLocalServerTest;
//import org.junit.runner.RunWith;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-12 9:10
 */
@XyySaaSLocalServerTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//TODO @RunWith(SpringRunner.class)
@DirtiesContext
public class DatasyncStatementProxyTest {

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    @Test
    public void testProxySQL() {
        UserMapperTest mapper = sqlSessionTemplate.getMapper(UserMapperTest.class);
        UserInfo userInfo = new UserInfo();
        userInfo.setId(3);
        userInfo.setName("xiaoming");
        mapper.insert(userInfo);

//        sqlSessionTemplate.select();

//        assertThat(tableName).isEqualTo("datasync_entity_test");
    }
}
