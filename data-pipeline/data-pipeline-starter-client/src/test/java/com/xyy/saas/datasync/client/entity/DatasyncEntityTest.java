package com.xyy.saas.datasync.client.entity;

import com.xyy.saas.datasync.client.constants.DataSyncDirection;
import com.xyy.saas.datasync.client.entity.DataSyncEntity;
import lombok.Builder;
import lombok.NonNull;
import org.apache.ibatis.annotations.SelectKey;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;

/**
 * @Desc 数据同步实体
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-03 21:48
 */
@DataSyncEntity(direction = DataSyncDirection.CLOUD_TO_LOCAL, ack = true)
public class DatasyncEntityTest {

    /** id*/
    @PrimaryKey
    @AutoIncrement
    private Integer id;

    @NotNull
    private String a1;

    @DefaultValue(value = "abc")
    private String b1;

    @Length(max = 100)
    private boolean a2;

}
