package com.xyy.saas.datasync.client.scanner;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
//import com.xyy.saas.datasync.client.XyySaaSLocalServerTest;
import com.xyy.saas.datasync.client.entity.*;
import com.xyy.saas.datasync.client.entity.DataSyncEntityDispatcher.DataSyncEntityContext;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
//import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.util.ClassUtils;

import java.util.Arrays;
import java.util.Map;


/**
 * @Desc 数据同步实体扫描器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-08-03 17:44
 */
//@XyySaaSLocalServerTest
@DirtiesContext
//@Import(DataSyncEntitysScannerRegistrar.class)
public class ClassPathDataSyncEntityScannerTest {

    /**
     * 测试实体类扫描,生成DataSyncEntityDefinition
     */
    @Test
    public void testDataSyncEntityScanner() {
        AnnotationConfigApplicationContext ac = new AnnotationConfigApplicationContext();
        ac.refresh();
        ClassPathDataSyncEntityScanner scanner = new ClassPathDataSyncEntityScanner(ac);
		scanner.doScanFindCandidateDatasyncEntity(null, new String[]{"com.xyy.saas.datasync.client.entity"}, null);
        Map<String, DataSyncEntityDefinition> dataSyncEntityMap = DataSyncEntityContext.getDataSyncEntityMap();
        DataSyncEntityDefinition dataSyncEntityDefinition = dataSyncEntityMap.get(
            DatasyncEntityTest.class.getName());
        String tableName = dataSyncEntityDefinition.getTableName();
        Assertions.assertThat(tableName).isEqualTo("datasync_entity_test");
        DataSyncEntityDefinition.FieldDefinition primaryKey = dataSyncEntityDefinition.getPrimaryKey();
        Assertions.assertThat(primaryKey.getFieldName()).isEqualTo("id");
        Assertions.assertThat(primaryKey.isAutoIncrement()).isTrue();
    }


    /**
     * 测试实体类类名，类型驼峰大写转下划线
     */
    @Test
    public void testSnakeCaseTableName() {
        String className = "com.xyy.saas.dayasync.client.entity.DatasyncEntityTest";
        String shortName = ClassUtils.getShortName(className);
        String tableName = new PropertyNamingStrategy.SnakeCaseStrategy().translate(shortName);
        Assertions.assertThat(tableName).isEqualTo("datasync_entity_test");
    }

}
