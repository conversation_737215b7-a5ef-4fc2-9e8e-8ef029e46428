server:
  port: 8080
  servlet:
    encoding:
      charset: UTF-8
      force: true
      enabled: true
spring:
  profiles:
    active: test
#  datasource:
#    type: com.zaxxer.hikari.HikariDataSource
#    url: jdbc:mysql://${mysql.addr:localhost:3306}/${mysql.db:test}?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=utf8&autoReconnect=true&allowMultiQueries=true&zeroDateTimeBehavior=convertToNull&useSSL=false&amp;serverTimezone=GMT&amp;allowPublicKeyRetrieval=true
#    driver-class-name: org.sqlite.JDBC
#    username: ${mysql.user:root}
#    password: ${mysql.pwd:zhangpeng123}
  application:
    name: data-pipeline-client
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: NON_NULL #序列化全部不为空的属性
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 0
      #单个数据的大小
      max-file-size: 10000000
  mvc:
    servlet:
      load-on-startup: 1
#    date-format: yyyy-MM-dd HH:mm:ss  #MVC的时间配置  需要3处配置  jackson时间和时区
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  mapper-locations: classpath*:/mapper/**/*.xml
  #typeAliasesPackage: com.service.*.entity;


