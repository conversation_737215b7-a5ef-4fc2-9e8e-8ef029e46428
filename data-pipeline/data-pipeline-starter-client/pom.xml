<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>data-pipeline</artifactId>
        <groupId>com.xyy.saas</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>data-pipeline-starter-client</artifactId>
	<version>${revision}</version>

    <dependencies>

		<dependency>
			<artifactId>spring-boot-starter</artifactId>
			<groupId>org.springframework.boot</groupId>
		</dependency>
		<dependency>
			<artifactId>spring-boot-starter-web</artifactId>
			<groupId>org.springframework.boot</groupId>
		</dependency>
		<dependency>
			<artifactId>spring-boot-starter-webflux</artifactId>
			<groupId>org.springframework.boot</groupId>
		</dependency>
		<dependency>
			<artifactId>spring-boot-starter-test</artifactId>
			<groupId>org.springframework.boot</groupId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<artifactId>slf4j-api</artifactId>
			<groupId>org.slf4j</groupId>
		</dependency>
		<dependency>
			<artifactId>lombok</artifactId>
			<groupId>org.projectlombok</groupId>
		</dependency>
		<dependency>
			<artifactId>utils-starter</artifactId>
			<groupId>com.xyy.saas</groupId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<artifactId>db-starter</artifactId>
			<groupId>com.xyy.saas</groupId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<artifactId>spring-boot-devtools</artifactId>
			<groupId>org.springframework.boot</groupId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<artifactId>hibernate-validator</artifactId>
			<groupId>org.hibernate.validator</groupId>
			<version>8.0.0.Final</version>
		</dependency>
		<dependency>
			<artifactId>jakarta.validation-api</artifactId>
			<groupId>jakarta.validation</groupId>
			<scope>compile</scope>
			<version>3.0.2</version>
		</dependency>
		<!--<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.1</version>
			<scope>test</scope>
		</dependency>-->
	</dependencies>


	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>
