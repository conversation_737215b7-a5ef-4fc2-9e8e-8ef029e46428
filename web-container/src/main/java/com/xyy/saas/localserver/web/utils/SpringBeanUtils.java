package com.xyy.saas.localserver.web.utils;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/1/20 18:37
 */
@Component
public class SpringBeanUtils implements ApplicationContextAware {

	private static SpringBeanUtils instance;

	@Autowired
	private ApplicationContext applicationContext;

	private SpringBeanUtils() {
		super();
	}

	/**
	 * 获取applicationContext
	 *
	 * @return
	 */
	public static ApplicationContext getApplicationContext() {
		return instance.applicationContext;
	}

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}

	/**
	 * 通过name获取 Bean.
	 *
	 * @param name
	 * @return
	 */
	public static Object getBean(String name) {
		return instance.getApplicationContext().getBean(name);
	}

	/**
	 * 通过class获取Bean.
	 *
	 * @param clazz
	 * @return
	 */
	public static <T> T getBean(Class<T> clazz) {
		return instance.getApplicationContext().getBean(clazz);
	}

	/**
	 * 通过name,以及Clazz返回指定的Bean
	 *
	 * @param name
	 * @param clazz
	 * @return
	 */
	public static <T> T getBean(String name, Class<T> clazz) {
		return instance.getApplicationContext().getBean(name, clazz);
	}

	@PostConstruct
	public final void init() {
		instance = this;
	}
}

