package com.xyy.saas.localserver.web;

import com.xyy.saas.localserver.utils.DateUtils;
import com.xyy.saas.localserver.web.utils.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.core.env.Environment;

/**
 * Hello world!
 *
 */
@Slf4j
@SpringBootApplication
public class LocalServerApplication {

    public static void main(String[] args) {
		long start = System.currentTimeMillis();
		SpringApplication app = new SpringApplication(LocalServerApplication.class);
		app.setBannerMode(Banner.Mode.OFF);
		app.run(args);
		String now = DateUtils.getNowFormatDate();
		Environment environment = SpringBeanUtils.getApplicationContext().getEnvironment();
		String property = environment.getProperty("spring.application.name");
		log.error(">>>>>applicationName [{}] 服务启动成功 Time:{} ,cost{}<<<<<", property, now,
			System.currentTimeMillis() - start);
	}

	/**
	 * 首次登陆，无登陆状态，需要请求登陆接口，鉴权账号有效性
	 *
	 *
	 *
	 *
	 *
	 * */

}
