<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.xyy.saas</groupId>
		<artifactId>local-server-cloud</artifactId>
		<version>${revision}</version>
	</parent>

	<artifactId>web-container</artifactId>
	<version>${revision}</version>
	<packaging>jar</packaging>


	<name>web-container</name>
	<!-- FIXME change it to the project's website -->

	<dependencies>
	  <dependency>
		  <artifactId>spring-boot-starter-webflux</artifactId>
		  <groupId>org.springframework.boot</groupId>
	  </dependency>
	  <dependency>
		  <artifactId>spring-boot-starter-test</artifactId>
		  <groupId>org.springframework.boot</groupId>
		  <scope>test</scope>
	  </dependency>

	  <dependency>
		  <artifactId>spring-boot-starter-web</artifactId>
		  <exclusions>
			  <exclusion>
				  <artifactId>spring-boot-starter-tomcat</artifactId>
				  <groupId>org.springframework.boot</groupId>
			  </exclusion>
		  </exclusions>
		  <groupId>org.springframework.boot</groupId>
	  </dependency>
	  <dependency>
		  <artifactId>spring-boot-starter-undertow</artifactId>
		  <groupId>org.springframework.boot</groupId>
	  </dependency>
	  <dependency>
		  <artifactId>spring-boot-starter-actuator</artifactId>
		  <groupId>org.springframework.boot</groupId>
	  </dependency>
	  <dependency>
		  <artifactId>lombok</artifactId>
		  <groupId>org.projectlombok</groupId>
		  <!--      <scope>provided</scope>-->
	  </dependency>

	  <dependency>
		  <artifactId>utils-starter</artifactId>
		  <groupId>com.xyy.saas</groupId>
	  </dependency>

	  <dependency>
		  <artifactId>db-starter</artifactId>
		  <groupId>com.xyy.saas</groupId>
	  </dependency>

<!-- 	  <dependency> -->
<!-- 		  <artifactId>data-pipeline-sqlite</artifactId> -->
<!-- 		  <groupId>com.xyy.saas</groupId> -->
<!-- 		  <version>${project.version}</version> -->
<!-- 	  </dependency> -->

	</dependencies>

</project>
