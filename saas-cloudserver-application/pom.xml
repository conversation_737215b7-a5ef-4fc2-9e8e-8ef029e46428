<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xyy.saas</groupId>
        <artifactId>saas-cloudserver</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>saas-cloudserver-application</artifactId>
    <version>${revision}</version>

    <dependencies>
        <!--<dependency>
            <groupId>com.imadcn.framework</groupId>
            <artifactId>idworker</artifactId>
            <version>1.6.0</version>
        </dependency>-->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-system-biz</artifactId>
            <version>${yudao.revision}</version>
            <exclusions>
                <exclusion>
                    <artifactId>yudao-spring-boot-starter-mq</artifactId>
                    <groupId>cn.iocoder.boot</groupId>
                </exclusion>
                <!--<exclusion>
                    <artifactId>mysql-connector-j</artifactId>
                    <groupId>com.mysql</groupId>
                </exclusion>-->
                <exclusion>
                    <artifactId>yudao-spring-boot-starter-mybatis</artifactId>
                    <groupId>cn.iocoder.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-infra-biz</artifactId>
            <version>${yudao.revision}</version>
            <exclusions>
                <exclusion>
                    <artifactId>yudao-spring-boot-starter-mq</artifactId>
                    <groupId>cn.iocoder.boot</groupId>
                </exclusion>
                <!--<exclusion>
                    <artifactId>mysql-connector-j</artifactId>
                    <groupId>com.mysql</groupId>
                </exclusion>-->
            </exclusions>
        </dependency>

        <!-- 会员中心。默认注释，保证编译速度 -->
        <!--        <dependency>-->
        <!--            <groupId>cn.iocoder.boot</groupId>-->
        <!--            <artifactId>yudao-module-member-biz</artifactId>-->
        <!--            <version>${yudao.revision}</version>-->
        <!--        </dependency>-->

        <!-- 数据报表。默认注释，保证编译速度 -->
        <!--        <dependency>-->
        <!--            <groupId>cn.iocoder.boot</groupId>-->
        <!--            <artifactId>yudao-module-report-biz</artifactId>-->
        <!--            <version>${revision}</version>-->
        <!--        </dependency>-->
        <!-- 工作流。默认注释，保证编译速度 -->
        <!--        <dependency>-->
        <!--            <groupId>cn.iocoder.boot</groupId>-->
        <!--            <artifactId>yudao-module-bpm-biz</artifactId>-->
        <!--            <version>${revision}</version>-->
        <!--        </dependency>-->
        <!-- 支付服务。默认注释，保证编译速度 -->
        <!--        <dependency>-->
        <!--            <groupId>cn.iocoder.boot</groupId>-->
        <!--            <artifactId>yudao-module-pay-biz</artifactId>-->
        <!--            <version>${revision}</version>-->
        <!--        </dependency>-->

        <!-- 微信公众号模块。默认注释，保证编译速度 -->
        <!--        <dependency>-->
        <!--            <groupId>cn.iocoder.boot</groupId>-->
        <!--            <artifactId>yudao-module-mp-biz</artifactId>-->
        <!--            <version>${revision}</version>-->
        <!--        </dependency>-->

        <!-- 商城相关模块。默认注释，保证编译速度-->
        <!--        <dependency>-->
        <!--            <groupId>cn.iocoder.boot</groupId>-->
        <!--            <artifactId>yudao-module-promotion-biz</artifactId>-->
        <!--            <version>${yudao.revision}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>cn.iocoder.boot</groupId>-->
        <!--            <artifactId>yudao-module-product-biz</artifactId>-->
        <!--            <version>${yudao.revision}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>cn.iocoder.boot</groupId>-->
        <!--            <artifactId>yudao-module-trade-biz</artifactId>-->
        <!--            <version>${yudao.revision}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>cn.iocoder.boot</groupId>-->
        <!--            <artifactId>yudao-module-statistics-biz</artifactId>-->
        <!--            <version>${yudao.revision}</version>-->
        <!--        </dependency>-->

        <!-- ERP 相关模块。默认注释，保证编译速度 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-module-erp-biz</artifactId>
            <version>${yudao.revision}</version>
        </dependency>

        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关,幂等 -->
        <dependency>
            <groupId>cn.iocoder.boot</groupId>
            <artifactId>yudao-spring-boot-starter-protection</artifactId>
            <version>${yudao.revision}</version>
        </dependency>
        <dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>data-pipeline-plugin</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xyy.saas</groupId>
            <artifactId>data-pipeline-starter-server</artifactId>
            <version>${project.version}</version>
        </dependency>


    </dependencies>

    <repositories>
        <repository>
            <id>sonatype-oss-snapshots</id>
            <name>Sonatype OSS Snapshots Repository</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </repository>
    </repositories>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>sqlite</id>
            <dependencies>
                <dependency>
                    <groupId>cn.iocoder.boot</groupId>
                    <artifactId>yudao-module-system-biz</artifactId>
                    <version>${yudao.revision}</version>
                    <exclusions>
                        <exclusion>
                            <artifactId>yudao-spring-boot-starter-mq</artifactId>
                            <groupId>cn.iocoder.boot</groupId>
                        </exclusion>
                        <exclusion>
                            <groupId>mysql</groupId>
                            <artifactId>mysql-connector-java</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
            </dependencies>
            <build>

            </build>
        </profile>
        <profile>
            <id>shade</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-shade-plugin</artifactId>
                        <version>3.5.1</version>
                        <dependencies>
                            <dependency>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-maven-plugin</artifactId>
                                <version>${spring.boot.version}</version>
                            </dependency>
                            <dependency>
                                <groupId>org.graalvm.buildtools</groupId>
                                <artifactId>native-maven-plugin</artifactId>
                                <version>${graalvm.native.version}</version>
                            </dependency>
                        </dependencies>
                        <configuration>
                            <!-- 自动移除项目中没有使用到的依赖，以此来最小化 jar 包的体积 -->
                            <minimizeJar>false</minimizeJar>
                            <keepDependenciesWithProvidedScope>true</keepDependenciesWithProvidedScope>
                            <createDependencyReducedPom>true</createDependencyReducedPom>
                            <filters>
                                <filter>
                                    <artifact>org.springframework.*</artifact>
                                    <includes>
                                        <include>**</include>
                                    </includes>
                                </filter>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                        <executions>
                            <execution>
                                <id>package-jar</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>shade</goal>
                                </goals>
                                <configuration>
                                    <transformers>
                                        <transformer
                                                implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                            <manifestEntries>
                                                <mainClass>${main.class}</mainClass>
                                                <X-Compile-Source-JDK>${maven.compiler.source}</X-Compile-Source-JDK>
                                                <X-Compile-Target-JDK>${maven.compiler.target}</X-Compile-Target-JDK>
                                            </manifestEntries>
                                        </transformer>
                                        <transformer
                                                implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                            <resource>META-INF/spring.handlers</resource>
                                        </transformer>
                                        <transformer
                                                implementation="org.springframework.boot.maven.PropertiesMergingResourceTransformer">
                                            <resource>META-INF/spring.factories</resource>
                                        </transformer>
                                        <transformer
                                                implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                            <resource>META-INF/spring.schemas</resource>
                                        </transformer>
                                        <transformer
                                                implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                            <resource>META-INF/spring.tooling</resource>
                                        </transformer>
                                        <transformer
                                                implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>

                                    </transformers>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>native</id>
            <!-- <dependencies> -->
            <!--     <dependency> -->
            <!--         <groupId>org.mybatis.spring.native</groupId> -->
            <!--         <artifactId>mybatis-spring-native-core</artifactId> -->
            <!--         <version>0.1.0-SNAPSHOT</version> -->
            <!--     </dependency> -->
            <!--     <dependency> -->
            <!--         <groupId>org.mybatis.spring.native</groupId> -->
            <!--         <artifactId>mybatis-spring-native-extensions</artifactId> -->
            <!--         <version>0.1.0-SNAPSHOT</version> -->
            <!--     </dependency> -->
            <!-- </dependencies> -->
            <build>
                <!--<resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <excludes>
                            <exclude>**/*.properties</exclude>
                            <exclude>**/*.xml</exclude>
                        </excludes>
                        <filtering>false</filtering>
                    </resource>
                    <resource>
                        <directory>src/main/java</directory>
                        <includes>
                            <include>**/*.properties</include>
                            <include>**/*.xml</include>
                        </includes>
                        <filtering>false</filtering>
                    </resource>
                </resources>-->
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <image>
                                <builder>paketobuildpacks/builder:tiny</builder>
                                <env>
                                    <BP_NATIVE_IMAGE>true</BP_NATIVE_IMAGE>
                                    <!-- <BP_NATIVE_IMAGE_BUILD_ARGUMENTS>&#45;&#45;no-fallback</BP_NATIVE_IMAGE_BUILD_ARGUMENTS> -->
                                </env>
                            </image>
                        </configuration>
                        <executions>
                            <execution>
                                <id>process-aot</id>
                                <goals>
                                    <goal>process-aot</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- Maven resource 插件 -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-resources-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy-resources</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>copy-resources</goal>
                                </goals>
                                <configuration>
                                    <overwrite>false</overwrite>
                                    <encoding>${project.build.sourceEncoding}</encoding>
                                    <outputDirectory>release/resources</outputDirectory>
                                    <resources>
                                        <resource>
                                            <directory>src/main/resources</directory>
                                        </resource>
                                    </resources>
                                </configuration>
                            </execution>
                            <execution>
                                <id>copy-jar</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>copy-resources</goal>
                                </goals>
                                <configuration>
                                    <overwrite>true</overwrite>
                                    <encoding>${project.build.sourceEncoding}</encoding>
                                    <outputDirectory>release/lib</outputDirectory>
                                    <resources>
                                        <resource>
                                            <directory>target</directory>
                                            <includes>
                                                <include>${project.artifactId}.jar</include>
                                            </includes>
                                        </resource>
                                    </resources>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.graalvm.buildtools</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <version>${graalvm.native.version}</version>
                        <configuration>
                            <resourcesConfigDirectory>src/main/resources</resourcesConfigDirectory>
                            <resourceIncludedPatterns>
                                <resourceIncludedPattern>
                                    **/*.*
                                </resourceIncludedPattern>
                            </resourceIncludedPatterns>
                            <!-- imageName用于设置生成的二进制文件名称 -->
                            <imageName>${project.build.finalName}</imageName>
                            <!-- mainClass用于指定main方法类路径 -->
                            <mainClass>${main.class}</mainClass>
                            <skipNativeTests>true</skipNativeTests>
                            <sharedLibrary>false</sharedLibrary>
                            <debug>false</debug>
                            <verbose>true</verbose>
                            <fallback>false</fallback>
                            <quickBuild>false</quickBuild>
                            <skip>false</skip>
                            <useArgFile>true</useArgFile>
                            <resourcesConfigDirectory></resourcesConfigDirectory>
                            <buildArgs>
                                -H:+AddAllCharsets
                                -H:+ReportExceptionStackTraces
                                -H:+PrintClassInitialization
                                -H:IncludeResources=".*.xml|.*.conf|.*.properties"
                                -H:+ReportUnsupportedElementsAtRuntime
                                <!--                                &#45;&#45;features=cn.iocoder.yudao.server.natives.RuntimeRegistrationFeature-->
                                --initialize-at-build-time=org.apache.log4j.PatternLayout
                                --initialize-at-build-time=org.apache.log4j.Layout
                                --initialize-at-build-time=org.slf4j.MDC
                                --initialize-at-build-time=org.slf4j.LoggerFactory
                                --initialize-at-build-time=org.slf4j.impl.StaticLoggerBinder
                                --initialize-at-build-time=org.apache.log4j.helpers.Loader
                                --initialize-at-build-time=org.apache.log4j.Logger
                                --initialize-at-build-time=org.apache.log4j.helpers.LogLog
                                --initialize-at-build-time=org.apache.log4j.LogManager
                                --initialize-at-build-time=org.apache.log4j.spi.LoggingEvent
                                --initialize-at-build-time=org.slf4j.impl.Log4jLoggerFactory
                                --initialize-at-build-time=org.slf4j.impl.Log4jLoggerAdapter
                                --initialize-at-build-time=com.lang.server.handler.FarChannelHandler
                                --initialize-at-build-time=java.beans.Introspector
                                --initialize-at-build-time=com.sun.beans.introspect.ClassInfo
                                --initialize-at-build-time=cn.hutool.core.convert.BasicType
                                --initialize-at-build-time=cn.hutool.core.util.CharsetUtil
                                --initialize-at-build-time=cn.hutool.core.util.ClassLoaderUtil
                                --initialize-at-run-time=io.netty.channel.epoll.Epoll
                                --initialize-at-run-time=io.netty.channel.epoll.Native
                                --initialize-at-run-time=io.netty.channel.epoll.EpollEventLoop
                                --initialize-at-run-time=io.netty.channel.epoll.EpollEventArray
                                --initialize-at-run-time=io.netty.channel.DefaultFileRegion
                                --initialize-at-run-time=io.netty.channel.kqueue.KQueueEventArray
                                --initialize-at-run-time=io.netty.channel.kqueue.KQueveEventLoop
                                --initialize-at-run-time=io.netty.channel.kqueue.Native
                                --initialize-at-run-time=io.netty.channel.unix.Errors
                                --initialize-at-run-time=io.netty.channel.unix.1ovArray
                                --initialize-at-run-time=io.netty.channel.unix.Limits
                                --initialize-at-run-time=io.netty.util.internal.logging.Log4JLogger
                                --initialize-at-run-time=io.netty.channel.unix.Socket
                                --initialize-at-run-time=io.netty.channel.ChannelHandlerMask
                                --initialize-at-build-time=org.springframework.util.unit.DataSize
                                --initialize-at-build-time=ch.qos.logback.classic.Level
                                --initialize-at-build-time=ch.qos.logback.classic.Logger
                                --initialize-at-build-time=ch.qos.logback.core.util.StatusPrinter
                                --initialize-at-build-time=ch.qos.logback.core.status.StatusBase
                                --initialize-at-build-time=ch.qos.logback.core.status.InfoStatus
                                --initialize-at-build-time=ch.qos.logback.core.spi.AppenderAttachableImpl
                                --initialize-at-build-time=ch.qos.logback.core.util.Loader
                                --initialize-at-build-time=ch.qos.logback.classic.spi.ThrowableProxy
                                --initialize-at-build-time=ch.qos.logback.core.CoreConstants
                                --report-unsupported-elements-at-runtime
                                --allow-incomplete-classpath
                                --enable-url-protocols=http
                                -H:EnableURLProtocols=http
                                -H:EnableURLProtocols=https
                                <!--                                -H:-CheckToolchain-->
                                <!-- -H:ReflectionConfigurationFiles=src/main/resources/META-INF/native-image/reflect-config.json -->
                                <!-- -H:ReflectionConfigurationFiles=target/spring-aot/main/resources/META-INF/native-image/cn.iocoder.boot/yudao-server/reflect-config.json -->
                                <!-- 新增 -->
                                <!-- trace 表示编译时 进行 跟踪，有些情况下可能会报错，比如在这里设置了A类，但是A类没有MAIN方法 会导致报错-->
                                <!-- trace-class-initialization=org.apache.log4j.PatternLayout -->
                                <!-- trace-class-initialization=org.apache.log4j.Layout -->
                                --trace-class-initialization=ch.qos.logback.classic.Logger
                                --trace-class-initialization=ch.qos.logback.core.status.InfoStatus
                                --trace-class-initialization=ch.qos.logback.core.status.StatusBase
                                --trace-class-initialization=ch.qos.logback.core.util.StatusPrinter
                                --trace-class-initialization=ch.qos.logback.classic.Level
                                --trace-class-initialization=ch.qos.logback.core.util.Loader
                                --trace-class-initialization=java.beans.Introspector
                                --trace-class-initialization=com.sun.beans.introspect.ClassInfo
                                --trace-class-initialization=cn.hutool.core.convert.BasicType
                                --trace-class-initialization=cn.hutool.core.util.CharsetUtil
                                --trace-class-initialization=cn.hutool.core.util.ClassLoaderUtil
                                -Dio.netty.tryReflectionSetAccessible=true
                                --add-exports=java.base/java.nio=ALL-UNNAMED
                                --add-opens java.base/java.nio=ALL-UNNAMED
                                --features=cn.iocoder.yudao.server.natives.RuntimeRegistrationFeature
                            </buildArgs>
                            <classesDirectory>${project.build.outputDirectory}</classesDirectory>
                            <metadataRepository>
                                <enabled>true</enabled>
                            </metadataRepository>
                            <requiredVersion>22.3</requiredVersion>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-native</id>
                                <goals>
                                    <goal>compile-no-fork</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <mainClass>${main.class}</mainClass>
                                </configuration>
                            </execution>
                            <execution>
                                <id>add-reachability-metadata</id>
                                <goals>
                                    <goal>add-reachability-metadata</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>nativeTest</id>
            <dependencies>
                <dependency>
                    <groupId>org.junit.platform</groupId>
                    <artifactId>junit-platform-launcher</artifactId>
                    <scope>test</scope>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>process-test-aot</id>
                                <goals>
                                    <goal>process-test-aot</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.graalvm.buildtools</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <configuration>
                            <classesDirectory>${project.build.outputDirectory}</classesDirectory>
                            <metadataRepository>
                                <enabled>true</enabled>
                            </metadataRepository>
                            <requiredVersion>22.3</requiredVersion>
                        </configuration>
                        <executions>
                            <execution>
                                <id>native-test</id>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>