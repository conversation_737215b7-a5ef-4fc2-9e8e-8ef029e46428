package com.xyy.saas.cloudserver.config.db;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;


/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/3/8 18:32
 */
@Slf4j
public class TenantIdentifierGenerator implements IdentifierGenerator {

    private static final long MAX_TENANT_ID = 9999999L; // 最大租户ID
    private static final long MAX_DEVICE_ID = 999L; // 最大电脑ID
    private static final long MAX_INCREMENT_ID = 999999999L; // 最大自增ID
    /**
     * 业务类型
     */
    private static int type = 1;
    private JdbcTemplate jdbcTemplate;
    private Integer deviceId = 1;

    private Map<String, AtomicLong> tableIdMap = new ConcurrentHashMap<>();

    public TenantIdentifierGenerator(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public static void main(String[] args) {
        System.out.println(Long.MAX_VALUE);
        //1000001001000000001
        //9223372036854775807
    }

    private Long getIncrementId(String tableName) {
        if (!StringUtils.hasText(tableName)) {
            return 0L;
        }
        AtomicLong incrementId = tableIdMap.get(tableName);
        if (incrementId == null) {
            Long tenantId = TenantContextHolder.getTenantId();
            if (tenantId == null || tenantId == 0) {
                tenantId = 0L;
            }
            String sql = "SELECT MAX(id) FROM " + tableName;
            Long maxIncrementId = jdbcTemplate.queryForObject(sql, Long.class);
            if (maxIncrementId == null || maxIncrementId == 0 || maxIncrementId < 0000001001000000000L) {
                //第一次id初始化
                maxIncrementId = fullId(tenantId, 0L);
            } else {
                Long minId = fullId(tenantId, 0L);
                if (maxIncrementId < minId) {
                    log.error("tableName:[{}],最小id:[{}],存在id:[{}],", tableName, minId, maxIncrementId);
                    maxIncrementId = minId;
                }
                Long maxId = fullId(tenantId, MAX_INCREMENT_ID);
                if (maxIncrementId > maxId) {
                    log.error("tableName:[{}],最大id:[{}],存在id:[{}]", tableName, maxId, maxIncrementId);
                }
            }
            incrementId = new AtomicLong(maxIncrementId);
            tableIdMap.putIfAbsent(tableName, incrementId);
        }
        return incrementId.incrementAndGet();
    }

    private Long fullId(Long tenantId, Long maxIncrementId) {
        StringBuilder sb = new StringBuilder();
        sb.append(type);
        sb.append(String.format("%06d", tenantId)); // 设置2-7位为租户ID
        sb.append(String.format("%03d", deviceId)); // 设置8-10位为电脑ID
        sb.append(String.format("%09d", maxIncrementId)); // 设置11-20位为自增ID
        return Long.parseLong(sb.toString());
    }

    @Override
    public Number nextId(Object entity) {
        Long tenantId = TenantContextHolder.getTenantId();
        StringBuilder sid = new StringBuilder();

        if (tenantId != null && (tenantId < 0 || tenantId > MAX_TENANT_ID)) {
            throw new IllegalArgumentException("Invalid tenant ID");
        }
        if (deviceId < 0 || deviceId > MAX_DEVICE_ID) {
            throw new IllegalArgumentException("Invalid computer ID");
        }
        String tableName = getTableName(entity);
        return getIncrementId(tableName);
    }

    private String getTableName(Object entity) {
        TableName annotation = entity.getClass().getAnnotation(TableName.class);
        return annotation.value();
    }

}
