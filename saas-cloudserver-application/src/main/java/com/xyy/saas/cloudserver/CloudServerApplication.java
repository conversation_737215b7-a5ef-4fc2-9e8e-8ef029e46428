package com.xyy.saas.cloudserver;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceHttpClientImpl;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceJoddHttpImpl;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceOkHttpImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.iocoder.yudao.framework.idempotent.config.YudaoIdempotentConfiguration;
import cn.iocoder.yudao.framework.ratelimiter.config.YudaoRateLimiterConfiguration;
import cn.iocoder.yudao.framework.redis.config.YudaoRedisAutoConfiguration;
import cn.iocoder.yudao.framework.signature.config.YudaoApiSignatureAutoConfiguration;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.binarywang.spring.starter.wxjava.miniapp.properties.WxMaProperties;
import com.binarywang.spring.starter.wxjava.mp.enums.HttpClientType;
import com.binarywang.spring.starter.wxjava.mp.properties.WxMpProperties;
import com.xyy.saas.datasync.client.EnableDataSyncScan;
import com.xyy.saas.cloudserver.config.db.TenantIdentifierGenerator;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceHttpClientImpl;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.api.impl.WxMpServiceJoddHttpImpl;
import me.chanjar.weixin.mp.api.impl.WxMpServiceOkHttpImpl;
import me.chanjar.weixin.mp.config.WxMpConfigStorage;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.apache.dubbo.config.spring.context.annotation.EnableDubboConfig;
import org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration;
import org.redisson.spring.starter.RedissonAutoConfigurationV2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * @Desc 云端服务
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/4/30 下午2:26
 */
//
@SpringBootApplication(
        scanBasePackages = {"${yudao.info.base-package}.server",
                "${yudao.info.base-package}.module", "com.xyy.saas.datasync.server.controller",
                "com.xyy.saas.localserver",}, exclude = {
        RedisAutoConfiguration.class,
        YudaoIdempotentConfiguration.class,
        YudaoRedisAutoConfiguration.class,
        DubboAutoConfiguration.class,
        YudaoRateLimiterConfiguration.class,
        YudaoApiSignatureAutoConfiguration.class,
//        YudaoMQAutoConfiguration.class,
//    YudaoRedisMQAutoConfiguration.class,
//    RedissonAutoConfiguration.class,
        RedissonAutoConfigurationV2.class})
@Slf4j
@EnableDataSyncScan(
        syncType = EnableDataSyncScan.SyncType.server,
        scanBasePackages = "cn.iocoder.yudao.module.*.dal.dataobject.*",
        excludePackages = "cn.iocoder.yudao.module.infra.dal.dataobject.demo",
        baseEntity = {cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO.class})
public class CloudServerApplication {

    public static void main(String[] args) {
        System.setProperty("spring.main.allow-circular-references", "true");
        SpringApplication app = new SpringApplication(CloudServerApplication.class);
        app.setAllowCircularReferences(true);
        app.run(args);
//        new SpringApplicationBuilder(YudaoServerApplication.class)
//                .applicationStartup(new BufferingApplicationStartup(20480))
//                .run(args);


//        log.error(">>>>>applicationName [{}] 服务启动成功 Time:{} <<<<<" , property, new Date());
    }

    @Bean
    public IdentifierGenerator idGenerator(JdbcTemplate jdbcTemplate) {
        return new TenantIdentifierGenerator(jdbcTemplate);
    }

    @Bean
    public WxMpService wxMpService(WxMpConfigStorage configStorage, WxMpProperties wxMpProperties) {
        HttpClientType httpClientType = wxMpProperties.getConfigStorage().getHttpClientType();
        WxMpService wxMpService;
        switch (httpClientType) {
            case OkHttp:
                wxMpService = newWxMpServiceOkHttpImpl();
                break;
            case JoddHttp:
                wxMpService = newWxMpServiceJoddHttpImpl();
                break;
            case HttpClient:
                wxMpService = newWxMpServiceHttpClientImpl();
                break;
            default:
                wxMpService = newWxMpServiceImpl();
                break;
        }
        if (configStorage != null && configStorage.getAppId() != null) {
            wxMpService.setWxMpConfigStorage(configStorage);
        }
        return wxMpService;
    }

    private WxMpService newWxMpServiceImpl() {
        return new WxMpServiceImpl();
    }

    private WxMpService newWxMpServiceHttpClientImpl() {
        return new WxMpServiceHttpClientImpl();
    }

    private WxMpService newWxMpServiceOkHttpImpl() {
        return new WxMpServiceOkHttpImpl();
    }

    private WxMpService newWxMpServiceJoddHttpImpl() {
        return new WxMpServiceJoddHttpImpl();
    }


    @Bean
//    @ConditionalOnBean({WxMaConfig.class})
    public WxMaService wxMaService(WxMaConfig wxMaConfig, WxMaProperties wxMaProperties) {
        com.binarywang.spring.starter.wxjava.miniapp.enums.HttpClientType httpClientType = wxMaProperties.getConfigStorage().getHttpClientType();
        Object wxMaService;
        switch (httpClientType) {
            case OkHttp:
                wxMaService = new WxMaServiceOkHttpImpl();
                break;
            case JoddHttp:
                wxMaService = new WxMaServiceJoddHttpImpl();
                break;
            case HttpClient:
                wxMaService = new WxMaServiceHttpClientImpl();
                break;
            default:
                wxMaService = new WxMaServiceImpl();
        }
        if (wxMaConfig != null && wxMaConfig.getAppid() != null) {
            ((WxMaService) wxMaService).setWxMaConfig(wxMaConfig);
        }
        return (WxMaService) wxMaService;
    }

}
