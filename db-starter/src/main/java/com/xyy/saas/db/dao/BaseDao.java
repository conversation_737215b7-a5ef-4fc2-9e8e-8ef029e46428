package com.xyy.saas.db.dao;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.baomidou.mybatisplus.extension.service.IService;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xyy.saas.db.model.Entity;
import com.xyy.saas.db.supper.IBaseMapper;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/1/21 10:59
 */
public interface BaseDao<M extends IBaseMapper<T>, T extends Entity<?>> extends IService<T> {

    /**
     * 保存一个实体，null的属性也会保存，不会使用数据库默认值
     * @param entity
     * @return
     */
    boolean insert(T entity);

    /**
     * 保存一个实体，null的属性不会保存，会使用数据库默认值
     *
     *
     * @param entity
     */
    boolean insertSelective(T entity);

    /**
     * 批量新增
     *
     * @param entitys
     * @return
     */
    boolean insertBatch(List<T> entitys);


    /**
     * 批量保存实体，null的属性不会保存，会使用数据库默认值
     *
     * @param entitys
     */
    boolean insertBatchSelective(List<T> entitys);

    /**
     * 逻辑删除,业务删除
     * @param entity
     * @return
     */
    int deleteLogic(T entity);

    boolean delete(T entity);

    boolean delete(Wrapper<T> wrapper);

    boolean deleteById(Serializable id);

    int deleteByIds(Collection<? extends Serializable> ids);

    /**
     * 屏蔽
     * @param entity
     * @return
     */
    boolean disabled(T entity);

    boolean updateById(T entity);

    boolean updateList(List<T> entitys);

    boolean updateSelectiveById(T entity);

    T selectOne(T entity);

    T selectOneByList(T entity);

    T selectOneByList(Wrapper<T> queryWrapper);

    T selectById(Serializable id);

    List<T> selectListByIds(Collection<? extends Serializable> ids);

    List<T> selectList(T entity);

    List<T> selectListAll();

    int selectCount(T entity);

    Page<?> selectPage(Page<T> page, T entity);

    Page<T> selectPage(Page<T> page, Wrapper<T> queryWrapper);

//    BasePage<?> selectDefinePage(BasePage<?> page, PageParamDto PageParamDto);

}
