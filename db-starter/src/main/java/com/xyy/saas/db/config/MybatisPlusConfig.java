//package com.xyy.saas.db.config;
//
////import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
////import com.baomidou.mybatisplus.core.injector.ISqlInjector;
////import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
//
////import com.baomidou.mybatisplus.mapper.AutoSqlInjector;
////import com.baomidou.mybatisplus.mapper.ISqlInjector;
////import com.baomidou.mybatisplus.plugins.PaginationInterceptor;
//import com.baomidou.mybatisplus.core.injector.ISqlInjector;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.transaction.annotation.EnableTransactionManagement;
//
///**
// * @Desc MybatisPlus  配置类
// * <AUTHOR>  <EMAIL>
// * @Date Created in 2021/1/21 10:28
// */
//@Configuration
////@EnableTransactionManagement(order = 2)
//public class MybatisPlusConfig {
//
////    @Bean
////    public PaginationInterceptor getPaginationInterceptor() {
////        PaginationInterceptor page = new PaginationInterceptor();
////        page.setDialectType("mysql");
////        return page;
////    }
//
//    /** SQL 自动注入器*/
////    @Bean
////    public ISqlInjector getISqlInjector() {
//////        return new DefaultSqlInjector();
////        return new AutoSqlInjector();
////    }
//
//}
