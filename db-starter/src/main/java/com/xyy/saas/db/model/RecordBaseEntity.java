package com.xyy.saas.db.model;

import java.io.Serializable;
import java.util.Date;

/**
 * @Desc 需要操作人的实体
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/1/21 10:41
 */
public class RecordBaseEntity <ID extends Serializable> extends BaseEntity<ID> {

    public final static transient String DEFAULT_USERNAME = "system_user";

    /** 创建人 */
    private String creater;

    /** 更新人 */
    private String updater;

    public void deleteRecordEntity(ID id, String updater) {
        Date deleteTime = new Date();
        this.updater = getDefaultUpdater(updater);
        this.deleteEntity(id);
    }

    public String getDefaultUpdater(String updater) {
        if(updater == null || updater.equals("")) {
            return DEFAULT_USERNAME;
        }
        return updater;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }
}
