package com.xyy.saas.db.dao.impl;

//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.boot.common.model.db.BasePage;
//import com.boot.common.model.restful.PageParamDto;
//import com.service.db.conf.DataSourceEnum;
//import com.service.db.conf.DynamicDataSource;
//import com.service.db.dao.BaseDao;
//import com.service.db.model.BaseEntity;
//import com.service.db.support.IBaseMapper;
//import com.utility.CollectionsUtils;
//import com.utility.New;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xyy.saas.db.dao.BaseDao;
import com.xyy.saas.db.model.BaseEntity;
import com.xyy.saas.db.model.DisabledBaseEntity;
import com.xyy.saas.db.model.Entity;
import com.xyy.saas.db.supper.IBaseMapper;

import java.io.Serializable;
import java.util.*;

/**
 * 常用CURD操作DAO封装
 *
 * <AUTHOR> Zhang.Peng
 * @date : 2017年9月27日 上午9:55:42
 */
public class BaseDaoImpl<M extends IBaseMapper<T>, T extends Entity<?>> extends ServiceImpl<M, T> implements BaseDao<M, T> {

//	public static BaseDaoImpl<?, ?> baseInstance = null;
//
//	@PostConstruct
//	public void init() {
//		 baseInstance = this;
//	}

    public M getMapper() {
        return this.baseMapper;
    }

    @Override
    public boolean insert(T entity) {
//        BaseEntity entity1 = BaseEntity.class.cast(entity);
        if(entity instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) entity;
            baseEntity.setCreateTime(new Date());
        }
        return baseMapper.insert(entity) > 0;
    }

    @Override
    public boolean insertSelective(T entity) {
        if(entity == null) {
            return false;
        }
        return baseMapper.insert(entity) > 0;
    }

    @Override
    public boolean insertBatch(List<T> entitys) {
        if (entitys == null || entitys.size() == 0) {
            return false;
        }
        return insertBatch(entitys);
    }

//    @Override
//    public int insertListGetNum(List<T> entitys) {
//        if (entitys == null || entitys.size() == 0) {
//            return 0;
//        }
//        boolean saveBatch = insertBatch(entitys);
//        return saveBatch ? entitys.size() : 0;
//    }

    @Override
    public boolean insertBatchSelective(List<T> entitys) {
        if (entitys == null || entitys.size() == 0) {
            return false;
        }
        return insertBatch(entitys);
    }

    @Override
    public int deleteLogic(T entity) {
        if(entity instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) entity;
            baseEntity.setDeleted(true);
        }
        return baseMapper.updateById(entity);
    }

    @Override
    public boolean delete(T entity) {
        Wrapper<T> queryWrapper = new QueryWrapper<T>(entity);
        return baseMapper.delete(queryWrapper) > 0;
    }

    @Override
    public boolean delete(Wrapper<T> wrapper) {
        return baseMapper.delete(wrapper) > 0;
    }

    @Override
    public boolean deleteById(Serializable id) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public int deleteByIds(Collection<? extends Serializable> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    @Override
    public boolean disabled(T entity){
        if(!(entity instanceof DisabledBaseEntity)) {
            return false;
        }
        DisabledBaseEntity disabledBaseEntity = (DisabledBaseEntity) entity;
        disabledBaseEntity.setDisabled(false);
        return baseMapper.updateById(entity) > 0;
    }

    @Override
    public boolean updateById(T entity) {
        if(entity instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) entity;
            baseEntity.setUpdateTime(new Date());
        }
        return super.updateById(entity);
    }

    @Override
    public boolean updateList(List<T> entitys) {
        return updateBatchById(entitys);
    }

    @Override
    public boolean updateSelectiveById(T entity) {
            // TODO 不报存null字段
        return updateById(entity);
    }

    @Override
    public T selectOne(T entity) {
        Wrapper<T> queryWrapper = new QueryWrapper<T>(entity);
        T result = baseMapper.selectOne(queryWrapper);
        return result;
    }

    @Override
    public T selectOneByList(T entity) {
        Wrapper<T> queryWrapper = new QueryWrapper<T>(entity);
        List<T> resultList = baseMapper.selectList(queryWrapper);
        if (resultList == null || resultList.size() == 0) {
            return null;
        } else {
            return resultList.get(0);
        }
    }

    @Override
    public T selectOneByList(Wrapper<T> queryWrapper) {
        List<T> resultList = baseMapper.selectList(queryWrapper);
        if (resultList == null || resultList.size() == 0) {
            return null;
        } else {
            return resultList.get(0);
        }
    }

    @Override
    public T selectById(Serializable id) {
        return baseMapper.selectById(id);
    }

    @Override
    public List<T> selectListByIds(Collection<? extends Serializable> ids) {
        return baseMapper.selectBatchIds(ids);
    }

    @Override
    public List<T> selectList(T entity) {
        Wrapper<T> queryWrapper = new QueryWrapper<T>(entity);
        List<T> list = baseMapper.selectList(queryWrapper);
        return list;
    }

    @Override
    public List<T> selectListAll() {
        // TODO 查询全部的
        return baseMapper.selectList(null);
    }

    @Override
    public int selectCount(T entity) {
        int count = 0;
        Wrapper<T> queryWrapper = new QueryWrapper<T>(entity);
        count = Math.toIntExact(baseMapper.selectCount(queryWrapper));
        return count;
    }

    @Override
    public Page<?> selectPage(Page<T> page, T entity) {
        Wrapper<T> queryWrapper = new QueryWrapper<T>(entity);
        page = (Page<T>) baseMapper.selectPage(page, queryWrapper);
        return page;
    }

    @Override
    public Page<T> selectPage(Page<T> page, Wrapper<T> queryWrapper) {
        page = (Page<T>) baseMapper.selectPage(page, queryWrapper);
        return page;
    }

//    @Override
//    public BasePage<?> selectDefinePage(BasePage<?> page, PageParamDto pageParamDto) {
//        Page iPage = new Page(page.getCurrent(),page.getSize());
//        iPage = (Page) baseMapper.selectDefinePage(iPage, pageParamDto);
//        page = BasePage.valueOf(iPage.getRecords(),iPage.getTotal(),iPage.getSize(),iPage.getCurrent());
//        return page;
//    }

}

