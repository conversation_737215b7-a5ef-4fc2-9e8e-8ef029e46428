package com.xyy.saas.db.model;

import java.io.Serializable;
import java.util.Date;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/1/21 10:53
 */
public class DisabledBaseEntity<ID extends Serializable> extends RecordBaseEntity<ID> {

    /** 是否屏蔽（true：是；false：否 */
    private Boolean disabled = false;

    /**
     * 屏蔽记录
     * @param id
     * @param updater
     * @param flag
     */
    public void disableEntity(ID id, String updater, Boolean flag){
        Date updateTime = new Date();
        this.setId(id);
        this.setUpdater(getDefaultUpdater(updater));
        this.setUpdateTime(updateTime);
        this.setDisabled(flag);
    }


    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }
}
