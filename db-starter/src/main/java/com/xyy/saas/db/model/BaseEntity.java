package com.xyy.saas.db.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

//import com.baomidou.mybatisplus.annotation.IdType;
//import com.baomidou.mybatisplus.annotation.TableId;

/**
 * @Desc 实体基类
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/1/21 10:32
 */
public class BaseEntity <ID extends Serializable> extends Entity implements Serializable {

    /** 主键id */
    @TableId(type = IdType.AUTO)
    private ID id;

    /** 创建时间 */
//	@CreatedDate
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 删除时间 */
    private Date deleteTime = null;

    /** 是否删除（true：是；false：否 */
    private Boolean deleted = false;

    public void deleteEntity(ID id) {
        Date deleteTime = new Date();
        this.setDeleted(true);
        this.setDeleteTime(deleteTime);
        this.setUpdateTime(deleteTime);
        if (id != null) {
            this.setId(id);
        }
    }

    public ID getId() {
        return id;
    }

    public void setId(ID id) {
        this.id = id;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
