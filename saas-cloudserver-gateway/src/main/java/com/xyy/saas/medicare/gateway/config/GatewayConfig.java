package com.xyy.saas.entity.gateway.config;

import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Desc 网关配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021-12-13 19:31
 */
@Configuration
public class GatewayConfig {

//    @Bean
//    public RouteLocator routes(RouteLocatorBuilder builder) {
//        return builder.routes()
//                      .route("circuitbreaker_route", r -> r.path("/consumingServiceEndpoint")
//                                                              .filters(f -> f.circuitBreaker(c -> c.name("myCircuitBreaker")
//                                                                                                   .fallbackUri("forward:/inCaseOfFailureUseThis")
//                                                                                                   .addStatusCode("INTERNAL_SERVER_ERROR"))
//                                                                             .rewritePath("/consumingServiceEndpoint", "/backingServiceEndpoint"))
//                                                              .uri("lb://backing-service:8088"))
//                      .build();
//    }
}
