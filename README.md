<img src="./docs/images/logo.png" height=80></img>

[![ASF2](http://img.shields.io/badge/license-ASF2-blue.svg)](http://www.apache.org/licenses/LICENSE-2.0.txt)

local-server-cloud
-------
local-server-cloud是一套基于端到云服务完整解决方案

## Features
* 支持数据同步(云到端,端到端,端到云)
* 支持db基础CURD
* 支持设备管理
* 支持网络代理
* 支持通用工具包
* 支持web容器


## Docker
使用 docker-compose 部署Docker镜像


## 架构
![](./docs/images/lsc.png)


## 组件
lsc由`data-pipeline`, `db`,`device-manager`,`network-proxy`,`utils`,`web-container`组成

### data-pipeline
data-pipeline数据同步管道[更多](./docs/data-pipeline.md)。

### db基础工具包
db工具包,支持基础CURD操作等[更多](./docs/db.md)。

### device-manager
设备管理平台[更多](./docs/device-manager.md)。

### network-proxy
网络代理[更多](./docs/network-proxy.md)。

### utils
工具包[更多](./docs/utils.md)。

### web-container
web容器[更多](./docs/web-container.md)。

### Portal
后台管理中心[更多](./docs/portal.md)。

### Modules
相关任务，消息，监控等组件[更多](./docs/modules.md)。

# 交流方式
<img src="./docs/images/wx.png" height=400></img>




