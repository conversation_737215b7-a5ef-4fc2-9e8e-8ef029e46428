<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.xyy.saas</groupId>
	<artifactId>local-server-cloud</artifactId>
	<version>${revision}</version>
	<packaging>pom</packaging>

	<modules>
		<module>data-pipeline</module>
		<module>device-manager</module>
		<module>network-proxy</module>
		<module>web-container</module>
		<module>utils-starter</module>
		<module>db-starter</module>
		<module>biz-soa-starter</module>
		<module>saas-cloud-dependencies-bom</module>
		<module>rocketmq-event-bus-spring-boot-starter</module>
	</modules>

	<properties>
		<!-- 统一版本 -->
		<revision>2.0.0-SNAPSHOT</revision>

		<java.version>21</java.version>
		<guava.version>33.2.1-jre</guava.version>
		<hutool.version>5.8.29</hutool.version>
		<jakarta.version>2.1.1</jakarta.version>
		<springboot.version>3.3.4</springboot.version>
		<mybatis-plus.version>3.5.7</mybatis-plus.version>
		<yudao.version>2.3.0-SNAPSHOT</yudao.version>
		<dubbo.version>3.3.0</dubbo.version>
		<jackson.version>2.17.0</jackson.version>

		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

		<maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
		<flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>

		<dubbo.version>3.3.0</dubbo.version>
		<protobuf.version>4.28.0</protobuf.version>
		<nacos.version>2.4.2</nacos.version>
		<springcloud.alibaba.version>2023.0.1.0</springcloud.alibaba.version>
		<springcloud.zookeeper.version>4.0.0</springcloud.zookeeper.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>cn.iocoder.boot</groupId>
				<artifactId>yudao-dependencies</artifactId>
				<version>${yudao.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${springboot.version}</version>
				<scope>import</scope>
				<type>pom</type>
			</dependency>

			<!-- Dubbo ，不能放开会导致spring jar依赖5.x -->
			<!--<dependency>
				<artifactId>dubbo-bom</artifactId>
				<groupId>org.apache.dubbo</groupId>
				<scope>import</scope>
				<type>pom</type>
				<version>${dubbo.version}</version>
			</dependency>-->

			<!--<dependency>
				<artifactId>dubbo-dependencies-zookeeper-curator5</artifactId>
				<groupId>org.apache.dubbo</groupId>
				<type>pom</type>
				<version>${dubbo.version}</version>
			</dependency>-->

			<!-- -->
			<dependency>
				<artifactId>utils-starter</artifactId>
				<groupId>com.xyy.saas</groupId>
				<version>${revision}</version>
			</dependency>
			<dependency>
				<artifactId>db-starter</artifactId>
				<groupId>com.xyy.saas</groupId>
				<version>${revision}</version>
			</dependency>
			<!--<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-api</artifactId>
				<version>1.7.25</version>
			</dependency>-->
			<dependency>
				<artifactId>slf4j-api</artifactId>
				<groupId>org.slf4j</groupId>
				<version>2.0.7</version>
			</dependency>
			<dependency>
				<artifactId>guava</artifactId>
				<groupId>com.google.guava</groupId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<artifactId>jakarta.annotation-api</artifactId>
				<groupId>jakarta.annotation</groupId>
				<version>${jakarta.version}</version>
			</dependency>
			<dependency>
				<artifactId>hutool-all</artifactId>
				<groupId>cn.hutool</groupId>
				<version>${hutool.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<profiles>
		<profile>
			<!--生产环境deploy url-->
			<id>prod</id>
			<distributionManagement>
				<repository>
					<id>maven-releases</id>
					<name>maven-releases</name>
					<url>http://maven.int.ybm100.com/repository/maven-releases/</url>
				</repository>
				<snapshotRepository>
					<id>maven-snapshots</id>
					<name>maven-snapshots</name>
					<url>http://maven.int.ybm100.com/repository/maven-snapshots/</url>
				</snapshotRepository>
			</distributionManagement>
		</profile>
		<profile>
			<!--测试环境deploy url-->
			<id>test</id>
			<distributionManagement>
				<repository>
					<id>maven-releases</id>
					<name>maven-releases</name>
					<url>http://mvn.int.ybm100.com/repository/maven-releases/</url>
				</repository>
				<snapshotRepository>
					<id>maven-snapshots</id>
					<name>maven-snapshots</name>
					<url>http://mvn.int.ybm100.com/repository/maven-snapshots/</url>
				</snapshotRepository>
			</distributionManagement>
		</profile>
	</profiles>

	<pluginRepositories>
		<pluginRepository>
			<id>spring-snapshots</id>
			<url>https://repo.spring.io/snapshot</url>
		</pluginRepository>
		<pluginRepository>
			<id>spring-milestones</id>
			<url>https://repo.spring.io/milestone</url>
		</pluginRepository>
	</pluginRepositories>

	<build>
		<pluginManagement>
			<plugins>
				<!-- 统一 revision 版本  https://blog.csdn.net/weixin_43762091/article/details/137960139 -->
				<plugin>
					<groupId>org.codehaus.mojo</groupId>
					<artifactId>flatten-maven-plugin</artifactId>
					<version>${flatten-maven-plugin.version}</version>
					<configuration>
						<flattenMode>resolveCiFriendliesOnly</flattenMode>
						<updatePomFile>true</updatePomFile>
					</configuration>
					<executions>
						<execution>
							<goals>
								<goal>flatten</goal>
							</goals>
							<id>flatten</id>
							<phase>process-resources</phase>
						</execution>
						<execution>
							<goals>
								<goal>clean</goal>
							</goals>
							<id>flatten.clean</id>
							<phase>clean</phase>
						</execution>
					</executions>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<source>21</source>
					<target>21</target>
					<parameters>true</parameters>
				</configuration>
			</plugin>

			<!-- 统一 revision 版本  https://blog.csdn.net/weixin_43762091/article/details/137960139 -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>spring-snapshots</id>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
			<url>https://repo.spring.io/snapshot</url>
		</repository>
		<repository>
			<id>spring-milestones</id>
			<url>https://repo.spring.io/milestone</url>
		</repository>
	</repositories>

</project>
