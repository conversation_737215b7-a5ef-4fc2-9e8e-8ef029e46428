<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.xyy.saas</groupId>
  <artifactId>saas-localserver</artifactId>
  <packaging>pom</packaging>
  <version>${revision}</version>
  <description>SaaS 药店系统 本地服务</description>

  <modules>
    <module>saas-localserver-application</module>
    <module>saas-localserver-business</module>
    <module>saas-localserver-dsl</module>
    <module>saas-localserver-resource</module>
    <module>saas-localserver-entity</module>
  </modules>

  <properties>
    <!-- 统一版本 -->
    <revision>2.0.0-SNAPSHOT</revision>

    <yudao.version>2.3.0-SNAPSHOT</yudao.version>
    <xyy.soa.version>2.0.0-SNAPSHOT</xyy.soa.version>
    <xyy.sqlite.version>2.0.0-SNAPSHOT</xyy.sqlite.version>
    <xyy.h2.version>2.0.0-SNAPSHOT</xyy.h2.version>
    <saas-web-container.version>2.0.0-SNAPSHOT</saas-web-container.version>
    <!-- Maven 相关 -->
    <java.version>21</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!--构建时跳过测试的编译和执行-->
    <maven.test.skip>true</maven.test.skip>
    <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
    <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
    <maven-springboot-plugin.version>3.3.4</maven-springboot-plugin.version>
    <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
    <lombok.version>1.18.34</lombok.version>
    <spring.boot.version>3.3.4</spring.boot.version>
    <mapstruct.version>1.5.5.Final</mapstruct.version>

    <!-- native image -->
    <project.main-class>com.xyy.saas.localserver.LocalserverApplication</project.main-class>

    <inquiry.version>2.0.0-SNAPSHOT</inquiry.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-dependencies</artifactId>
        <version>${yudao.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-framework</artifactId>
        <version>${yudao.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-module-infra-biz</artifactId>
        <version>${yudao.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>spring-boot-admin-starter-server</artifactId>
            <groupId>de.codecentric</groupId>
          </exclusion>
          <exclusion>
            <artifactId>yudao-spring-boot-starter-monitor</artifactId>
            <groupId>cn.iocoder.boot</groupId>
          </exclusion>
          <exclusion>
            <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
            <groupId>com.baomidou</groupId>
          </exclusion>
          <exclusion>
            <artifactId>checker-qual</artifactId>
            <groupId>org.checkerframework</groupId>
          </exclusion>
          <exclusion>
            <artifactId>jsr305</artifactId>
            <groupId>com.google.code.findbugs</groupId>
          </exclusion>
          <exclusion>
            <artifactId>error_prone_annotations</artifactId>
            <groupId>com.google.errorprone</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.xyy.saas</groupId>
        <artifactId>biz-soa-starter</artifactId>
        <version>${xyy.soa.version}</version>
      </dependency>

      <dependency>
        <groupId>com.xyy.saas</groupId>
        <artifactId>data-pipeline-sqlite</artifactId>
        <version>${xyy.sqlite.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xyy.saas</groupId>
        <artifactId>data-pipeline-h2</artifactId>
        <version>${xyy.sqlite.version}</version>
      </dependency>

      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-module-bpm-biz</artifactId>
        <version>${yudao.version}</version>
      </dependency>


      <dependency>
        <groupId>com.xyy.saas</groupId>
        <artifactId>web-container</artifactId>
        <version>${saas-web-container.version}</version>
      </dependency>
<!--      <dependency>-->
<!--        <groupId>com.xyy.saas</groupId>-->
<!--        <artifactId>saas-inquiry-user-server</artifactId>-->
<!--        <version>${revision}</version>-->
<!--      </dependency>-->
<!--      <dependency>-->
<!--        <groupId>com.xyy.saas</groupId>-->
<!--        <artifactId>saas-inquiry-product-server</artifactId>-->
<!--        <version>${revision}</version>-->
<!--      </dependency>-->
      <dependency>
        <groupId>com.xyy.saas</groupId>
        <artifactId>saas-inquiry-product-api</artifactId>
        <version>${inquiry.version}</version>
      </dependency>
<!--      <dependency>-->
<!--        <groupId>com.xyy.saas</groupId>-->
<!--        <artifactId>rocketmq-event-bus-spring-boot-starter</artifactId>-->
<!--        <version>${rocketmq-event-bus.version}</version>-->
<!--      </dependency>-->
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <!-- maven-surefire-plugin 插件，用于运行单元测试。 -->
        <!-- 注意，需要使用 3.0.X+，因为要支持 Junit 5 版本 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
        </plugin>
        <!-- maven-compiler-plugin 插件，解决 spring-boot-configuration-processor + Lombok + MapStruct 组合 -->
        <!-- https://stackoverflow.com/questions/33483697/re-run-spring-boot-configuration-annotation-processor-to-update-generated-metada -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
          <configuration>
            <annotationProcessorPaths>
              <path>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>${spring.boot.version}</version>
              </path>
              <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
              </path>
              <path>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
              </path>
            </annotationProcessorPaths>
            <!-- 编译参数写在 arg 内，解决 Spring Boot 3.2 的 Parameter Name Discovery 问题 -->
            <debug>false</debug>
            <compilerArgs>
              <arg>-parameters</arg>
            </compilerArgs>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>flatten-maven-plugin</artifactId>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <!-- 统一 revision 版本  https://blog.csdn.net/weixin_43762091/article/details/137960139 -->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${flatten-maven-plugin.version}</version>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
        </configuration>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
      </plugin>


    </plugins>
  </build>

  <!-- 控制项目的发布和部署 指定项目的构建产物（如JAR文件、WAR文件等）应该被部署到哪个远程仓库 -->
  <profiles>
    <profile>
      <!--生产环境deploy url-->
      <id>prod</id>
      <distributionManagement>
        <repository>
          <id>maven-releases</id>
          <name>maven-releases</name>
          <url>https://maven.int.ybm100.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
          <id>maven-snapshots</id>
          <name>maven-snapshots</name>
          <url>https://maven.int.ybm100.com/repository/maven-snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
    <profile>
      <!--测试环境deploy url-->
      <id>test</id>
      <distributionManagement>
        <repository>
          <id>maven-releases</id>
          <name>maven-releases</name>
          <url>https://mvn.int.ybm100.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
          <id>maven-snapshots</id>
          <name>maven-snapshots</name>
          <url>https://mvn.int.ybm100.com/repository/maven-snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
  </profiles>

  <!--    定义项目构建过程中所需的外部依赖库的来源-->
  <repositories>
    <!-- 使用  aliyun 的 Maven 源，提升下载速度 -->
    <repository>
      <id>aliyunmaven</id>
      <name>aliyun</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>
    <repository>
      <id>mvn-snapshots</id>
      <url>https://mvn.int.ybm100.com/repository/maven-public/</url>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </repository>
  </repositories>

</project>
