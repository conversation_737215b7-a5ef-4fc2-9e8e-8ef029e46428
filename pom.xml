<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-cloudserver</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <modules>
        <module>saas-cloudserver-gateway</module> <!-- 网关服务() -->
        <module>saas-cloudserver-entity</module> <!-- 实体服务() -->
        <module>saas-cloudserver-data-synchronizer</module> <!-- 数据同步服务() -->
        <module>saas-cloudserver-business</module> <!-- 业务服务() -->
        <module>saas-cloudserver-application</module> <!-- 应用服务() -->
    </modules>

    <properties>
        <!-- 统一版本 -->
        <revision>2.0.0-SNAPSHOT</revision>

        <yudao.revision>2.3.0-SNAPSHOT</yudao.revision>
        <!-- Maven 相关 -->
        <java.version>21</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
        <!-- 看看咋放到 bom 里 -->
        <lombok.version>1.18.30</lombok.version>
        <spring.boot.version>3.3.4</spring.boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <spring-cloud.version>2021.0.0</spring-cloud.version>

        <main.class>com.xyy.saas.cloudserver.CloudServerApplication</main.class>
        <graalvm.native.version>0.9.22</graalvm.native.version>
        <querydsl.version>5.0.0</querydsl.version>
        <inquiry.version>2.0.0-SNAPSHOT</inquiry.version>
        <maven-springboot-plugin.version>3.3.4</maven-springboot-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.iocoder.boot</groupId>
                <artifactId>yudao-dependencies</artifactId>
                <version>${yudao.revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <!--生产环境deploy url-->
            <id>prod</id>
            <distributionManagement>
                <repository>
                    <id>maven-releases</id>
                    <name>maven-releases</name>
                    <url>http://maven.int.ybm100.com/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>maven-snapshots</id>
                    <name>maven-snapshots</name>
                    <url>http://maven.int.ybm100.com/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
        <profile>
            <!--测试环境deploy url-->
            <id>test</id>
            <distributionManagement>
                <repository>
                    <id>maven-releases</id>
                    <name>maven-releases</name>
                    <url>http://mvn.int.ybm100.com/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>maven-snapshots</id>
                    <name>maven-snapshots</name>
                    <url>http://mvn.int.ybm100.com/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
    </profiles>

    <build>
        <plugins>
            <!-- 统一 revision 版本  https://blog.csdn.net/weixin_43762091/article/details/137960139 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>