package com.xyy.saas.device.appinfo;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Desc LocalServer实例信息
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/3/30 11:18
 */
@Slf4j
@Data
public class InstanceInfo {

    /** 业务App*/
    private String appName;

    /** app 组*/
    private String appGroupName;

    /** ip 信息*/
    private String ipAddr;

    /** 版本号*/
    private String versionCode;

    /** 注册时间*/
    private long registryTimestamp;

    /** 健康检查url */
    private String healthCheckUrl;

    /** 设备实例状态*/
    private volatile InstanceStatus status = InstanceStatus.ON_LINE;

}
