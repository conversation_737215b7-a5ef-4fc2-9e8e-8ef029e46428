package com.xyy.saas.device.appinfo;

import lombok.extern.slf4j.Slf4j;

/**
 * @Desc 实例状态
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/3/30 11:35
 */
@Slf4j
public enum InstanceStatus {

    /** 初始化*/
    INIT,

    /** 开始中*/
    STARTING,

    /** 在线*/
    ON_LINE,

    /** 离线*/
    OFF_LINE,

    /** 不服务*/
    OUT_OF_SERVICE,

    /** 未知*/
    UNKNOWN;


    public static InstanceStatus toEnum(String s) {
        if (s != null) {
            try {
                return InstanceStatus.valueOf(s.toUpperCase());
            } catch (IllegalArgumentException e) {
                // ignore and fall through to unknown
//                log.info("illegal argument supplied to InstanceStatus.valueOf: {}, defaulting to {}", s, UNKNOWN);
            }
        }
        return UNKNOWN;
    }
}
