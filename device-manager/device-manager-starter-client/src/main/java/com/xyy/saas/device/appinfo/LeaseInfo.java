package com.xyy.saas.device.appinfo;

/**
 * @Desc 续租信息
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/3/30 11:27
 */
public class LeaseInfo {


    /** 默认续租时间*/
    public static final int DEFAULT_LEASE_RENEWAL_INTERVAL = 30;
    /** 默认租约有效期持续时间*/
    public static final int DEFAULT_LEASE_DURATION = 90;

    // Client settings
    /** 续租持续时间*/
    private int renewalIntervalInSecs = DEFAULT_LEASE_RENEWAL_INTERVAL;

    /** 续租持续时间*/
    private int durationInSecs = DEFAULT_LEASE_DURATION;

    // Server settings
    /** 租约的注册*/
    private long registrationTimestamp;

    /** 最后一次续租时间*/
    private long lastRenewalTimestamp;

    /** 下线时间*/
    private long evictionTimestamp;

    /** 上线时间*/
    private long serviceUpTimestamp;

}
