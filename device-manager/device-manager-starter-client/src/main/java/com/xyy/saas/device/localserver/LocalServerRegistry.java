package com.xyy.saas.device.localserver;

import com.xyy.saas.device.appinfo.InstanceInfo;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2021/3/30 14:22
 */
public interface LocalServerRegistry<R extends InstanceInfo> {

    /**
     *
     * @param registration
     */
    void register(R registration);

    /**
     *
     * @param registration
     */
    void deregister(R registration);

    /**
     *
     */
    void close();

    /**
     *
     * @param registration
     * @param status
     */
    void setStatus(R registration, String status);

    /**
     *
     * @param registration
     * @param <T>
     * @return
     */
    <T> T getStatus(R registration);

}
